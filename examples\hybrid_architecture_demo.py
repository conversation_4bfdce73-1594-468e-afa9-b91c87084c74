#!/usr/bin/env python3
"""
Hybrid Architecture Demo - Service + Event Queue Pattern.

This demo showcases the hybrid approach combining:
1. Synchronous service operations for immediate responses
2. Asynchronous event processing for background optimization
3. RabbitMQ integration for production event handling
"""

import sys
import os
import asyncio
import time
from datetime import date, datetime
from typing import Dict, Any

# Add src to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import after path setup
try:
    from services.scheduler_service import SchedulerService, SchedulerRequest
    from api.service_clients import ServiceType
    from events.rabbitmq_integration import (
        EventType, EventPriority, publish_event_rabbitmq,
        initialize_rabbitmq, get_rabbitmq_event_bus
    )
    from loguru import logger
except ImportError as e:
    print(f"Import error: {e}")
    print("Please run this script from the project root directory")
    sys.exit(1)


class HybridArchitectureDemo:
    """Demonstration of the hybrid architecture."""
    
    def __init__(self):
        self.scheduler_service = SchedulerService(use_mock_services=True)
        self.event_bus = get_rabbitmq_event_bus()
        self.logger = logger.bind(component="Demo")
    
    def run_demo(self):
        """Run the complete demonstration."""
        self.logger.info("🚀 Starting Hybrid Architecture Demonstration")
        self.logger.info("=" * 60)
        
        # Demo 1: Synchronous Service Operations
        self._demo_synchronous_operations()
        
        # Demo 2: Asynchronous Event Processing
        self._demo_asynchronous_events()
        
        # Demo 3: Hybrid Operations
        self._demo_hybrid_operations()
        
        # Demo 4: Real-world Scenario
        self._demo_real_world_scenario()
        
        self.logger.info("=" * 60)
        self.logger.info("✅ Hybrid Architecture Demonstration Completed")
    
    def _demo_synchronous_operations(self):
        """Demonstrate synchronous service operations."""
        self.logger.info("📋 Demo 1: Synchronous Service Operations")
        self.logger.info("-" * 40)
        
        # 1. Get providers (immediate response)
        self.logger.info("1. Getting providers for skilled nursing...")
        request = SchedulerRequest(
            operation="get_providers",
            data={},
            service_type=ServiceType.SKILLED_NURSING
        )
        response = self.scheduler_service.process_request(request)
        
        if response.success:
            providers = response.data.get('providers', [])
            self.logger.info(f"   ✅ Retrieved {len(providers)} providers immediately")
            self.logger.info(f"   ⏱️  Response time: {response.processing_time:.3f}s")
        else:
            self.logger.error(f"   ❌ Failed: {response.message}")
        
        # 2. Get patients (immediate response)
        self.logger.info("2. Getting patients...")
        request = SchedulerRequest(
            operation="get_patients",
            data={}
        )
        response = self.scheduler_service.process_request(request)
        
        if response.success:
            patients = response.data.get('patients', [])
            self.logger.info(f"   ✅ Retrieved {len(patients)} patients immediately")
            self.logger.info(f"   ⏱️  Response time: {response.processing_time:.3f}s")
        else:
            self.logger.error(f"   ❌ Failed: {response.message}")
        
        # 3. Get current schedule (immediate response)
        self.logger.info("3. Getting current schedule...")
        request = SchedulerRequest(
            operation="get_schedule",
            data={},
            target_date=date.today()
        )
        response = self.scheduler_service.process_request(request)
        
        if response.success:
            schedule_data = response.data
            self.logger.info(f"   ✅ Retrieved schedule for {schedule_data.get('target_date')}")
            self.logger.info(f"   📅 {len(schedule_data.get('appointments', []))} appointments")
            self.logger.info(f"   ⏱️  Response time: {response.processing_time:.3f}s")
        else:
            self.logger.error(f"   ❌ Failed: {response.message}")
        
        self.logger.info("")
    
    def _demo_asynchronous_events(self):
        """Demonstrate asynchronous event processing."""
        self.logger.info("🔄 Demo 2: Asynchronous Event Processing")
        self.logger.info("-" * 40)
        
        # 1. Publish optimization event
        self.logger.info("1. Publishing schedule optimization event...")
        event = publish_event_rabbitmq(
            EventType.SCHEDULE_OPTIMIZATION_REQUESTED,
            {
                'optimization_type': 'assignment',
                'service_type': ServiceType.SKILLED_NURSING.value,
                'target_date': date.today().isoformat(),
                'requested_by': 'demo_user'
            },
            priority=EventPriority.HIGH,
            correlation_id='demo_correlation_123'
        )
        
        self.logger.info(f"   ✅ Event published: {event.id}")
        self.logger.info(f"   📊 Event type: {event.type.value}")
        self.logger.info(f"   🎯 Priority: {event.priority.name}")
        self.logger.info(f"   🔗 Correlation ID: {event.correlation_id}")
        
        # 2. Publish appointment created event
        self.logger.info("2. Publishing appointment created event...")
        event = publish_event_rabbitmq(
            EventType.APPOINTMENT_CREATED,
            {
                'appointment_id': 'demo_appointment_456',
                'appointment_date': date.today().isoformat(),
                'service_type': ServiceType.SKILLED_NURSING.value,
                'patient_id': 'demo_patient_789'
            },
            priority=EventPriority.HIGH,
            correlation_id='demo_correlation_123'
        )
        
        self.logger.info(f"   ✅ Event published: {event.id}")
        self.logger.info(f"   📊 Event type: {event.type.value}")
        
        # 3. Publish provider availability changed event
        self.logger.info("3. Publishing provider availability changed event...")
        event = publish_event_rabbitmq(
            EventType.PROVIDER_AVAILABILITY_CHANGED,
            {
                'provider_id': 'demo_provider_101',
                'affected_dates': [date.today().isoformat()],
                'service_type': ServiceType.SKILLED_NURSING.value,
                'change_type': 'unavailable'
            },
            priority=EventPriority.CRITICAL,
            correlation_id='demo_correlation_123'
        )
        
        self.logger.info(f"   ✅ Event published: {event.id}")
        self.logger.info(f"   📊 Event type: {event.type.value}")
        self.logger.info(f"   🚨 Priority: {event.priority.name} (Critical)")
        
        self.logger.info("")
    
    def _demo_hybrid_operations(self):
        """Demonstrate hybrid operations combining sync and async."""
        self.logger.info("🔄 Demo 3: Hybrid Operations (Sync + Async)")
        self.logger.info("-" * 40)
        
        # 1. Create appointment (synchronous) with background optimization (asynchronous)
        self.logger.info("1. Creating appointment with background optimization...")
        
        appointment_data = {
            'patient_id': 'demo_patient_123',
            'provider_id': 'demo_provider_456',
            'appointment_date': date.today().isoformat(),
            'service_type': ServiceType.SKILLED_NURSING.value,
            'duration_min': 60,
            'required_skills': ['wound_care', 'assessment']
        }
        
        request = SchedulerRequest(
            operation="create_appointment",
            data=appointment_data,
            correlation_id='hybrid_demo_123'
        )
        
        start_time = time.time()
        response = self.scheduler_service.process_request(request)
        processing_time = time.time() - start_time
        
        if response.success:
            self.logger.info(f"   ✅ Appointment created immediately")
            self.logger.info(f"   📋 Appointment ID: {response.data.get('id')}")
            self.logger.info(f"   ⏱️  Response time: {processing_time:.3f}s")
            self.logger.info(f"   🔄 Background optimization triggered")
        else:
            self.logger.error(f"   ❌ Failed: {response.message}")
        
        # 2. Request schedule optimization (synchronous request, asynchronous processing)
        self.logger.info("2. Requesting schedule optimization...")
        
        request = SchedulerRequest(
            operation="optimize_schedule",
            data={'requested_by': 'demo_user'},
            service_type=ServiceType.SKILLED_NURSING,
            target_date=date.today(),
            correlation_id='hybrid_demo_456'
        )
        
        start_time = time.time()
        response = self.scheduler_service.process_request(request)
        processing_time = time.time() - start_time
        
        if response.success:
            self.logger.info(f"   ✅ Optimization request accepted immediately")
            self.logger.info(f"   📋 Event ID: {response.data.get('event_id')}")
            self.logger.info(f"   ⏱️  Response time: {processing_time:.3f}s")
            self.logger.info(f"   🔄 Background optimization started")
        else:
            self.logger.error(f"   ❌ Failed: {response.message}")
        
        self.logger.info("")
    
    def _demo_real_world_scenario(self):
        """Demonstrate a real-world healthcare scheduling scenario."""
        self.logger.info("🏥 Demo 4: Real-World Healthcare Scenario")
        self.logger.info("-" * 40)
        
        self.logger.info("Scenario: Patient books urgent appointment, system handles:")
        self.logger.info("  1. Immediate validation and booking")
        self.logger.info("  2. Background schedule optimization")
        self.logger.info("  3. Provider and patient notifications")
        self.logger.info("  4. Integration with external systems")
        self.logger.info("")
        
        # Step 1: Patient books urgent appointment
        self.logger.info("Step 1: Patient books urgent appointment...")
        
        urgent_appointment = {
            'patient_id': 'patient_urgent_001',
            'provider_id': 'provider_rn_001',
            'appointment_date': date.today().isoformat(),
            'service_type': ServiceType.SKILLED_NURSING.value,
            'duration_min': 90,
            'urgent': True,
            'required_skills': ['wound_care', 'assessment'],
            'notes': 'Patient has infected wound, needs immediate attention'
        }
        
        request = SchedulerRequest(
            operation="create_appointment",
            data=urgent_appointment,
            correlation_id='urgent_case_001'
        )
        
        start_time = time.time()
        response = self.scheduler_service.process_request(request)
        booking_time = time.time() - start_time
        
        if response.success:
            self.logger.info(f"   ✅ Urgent appointment booked immediately")
            self.logger.info(f"   📋 Appointment ID: {response.data.get('id')}")
            self.logger.info(f"   ⏱️  Booking time: {booking_time:.3f}s")
            self.logger.info(f"   🚨 Urgent case handled synchronously")
            
            # Step 2: Background processes triggered
            self.logger.info("Step 2: Background processes triggered...")
            self.logger.info("   🔄 Schedule optimization started")
            self.logger.info("   📧 Provider notification queued")
            self.logger.info("   📧 Patient notification queued")
            self.logger.info("   🔗 External system sync queued")
            self.logger.info("   📊 Audit trail updated")
            
            # Step 3: Show event flow
            self.logger.info("Step 3: Event flow demonstration...")
            
            # Publish events that would be triggered by the appointment creation
            events = [
                (EventType.SCHEDULE_OPTIMIZATION_REQUESTED, {
                    'optimization_type': 'assignment',
                    'trigger': 'urgent_appointment_created',
                    'appointment_id': response.data.get('id'),
                    'priority': 'high'
                }),
                (EventType.NOTIFICATION_SEND_REQUESTED, {
                    'type': 'urgent_appointment_assigned',
                    'recipient': 'provider:provider_rn_001',
                    'message': f'URGENT: New appointment {response.data.get("id")} assigned',
                    'priority': 'critical'
                }),
                (EventType.NOTIFICATION_SEND_REQUESTED, {
                    'type': 'appointment_confirmed',
                    'recipient': 'patient:patient_urgent_001',
                    'message': f'Your urgent appointment has been scheduled: {response.data.get("id")}',
                    'priority': 'high'
                }),
                (EventType.EXTERNAL_SYSTEM_SYNC_REQUESTED, {
                    'system': 'EMR',
                    'operation': 'appointment_created',
                    'appointment_id': response.data.get('id'),
                    'priority': 'high'
                })
            ]
            
            for event_type, event_data in events:
                event = publish_event_rabbitmq(
                    event_type,
                    event_data,
                    priority=EventPriority.HIGH,
                    correlation_id='urgent_case_001'
                )
                self.logger.info(f"   📤 {event_type.value}: {event.id}")
            
            self.logger.info("")
            self.logger.info("✅ Real-world scenario completed successfully!")
            self.logger.info("   🎯 Immediate response: Patient gets instant confirmation")
            self.logger.info("   🔄 Background processing: Optimization and notifications")
            self.logger.info("   🏥 Healthcare workflow: Seamless integration")
            
        else:
            self.logger.error(f"   ❌ Failed to book urgent appointment: {response.message}")
        
        self.logger.info("")


def main():
    """Run the hybrid architecture demonstration."""
    demo = HybridArchitectureDemo()
    demo.run_demo()


if __name__ == "__main__":
    main() 