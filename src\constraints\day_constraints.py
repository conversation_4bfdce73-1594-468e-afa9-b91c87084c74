"""
Day planning stage constraints for healthcare scheduling optimization.

This module contains active constraints for the second stage of optimization:
- Assigning time slots to appointments that already have providers and dates

This file now serves as a coordinator that imports and combines constraints
"""

from timefold.solver.score import constraint_provider, ConstraintFactory
from typing import Optional

# Import grouped constraint modules
from constraints.c010_schd_timeslot_availability_validation import timeslot_availability_validation_constraints
from constraints.c011_schd_appointment_overlap_prevention import appointment_overlap_prevention_constraints
from constraints.c012_schd_flexible_appointment_timing_optimization import flexible_appointment_timing_optimization_constraints
from constraints.c013_schd_healthcare_task_sequencing import healthcare_task_sequencing_constraints
from constraints.c014_schd_route_travel_time_optimization import route_travel_time_optimization_constraints
from constraints.c015_schd_timed_appointment_pinning import timed_appointment_pinning_constraints
from constraints.c016_schd_route_optimization import route_optimization_constraints


@constraint_provider
def define_day_constraints(constraint_factory: ConstraintFactory):
    """Define all active constraints for the day planning stage."""
    constraints = []

    # Hard constraints - must be satisfied (always enabled)
    constraints.extend(timeslot_availability_validation_constraints(constraint_factory))
    constraints.extend(appointment_overlap_prevention_constraints(constraint_factory))
    constraints.extend(flexible_appointment_timing_optimization_constraints(constraint_factory))

    # Soft constraints - optimization preferences (always enabled with decorators)
    # Temporarily disabled due to Timefold translation issues
    # constraints.extend(route_travel_time_optimization_constraints(constraint_factory))
    constraints.extend(timed_appointment_pinning_constraints(constraint_factory))
    constraints.extend(healthcare_task_sequencing_constraints(constraint_factory))
    
    # Route optimization constraints - temporarily disabled
    # constraints.extend(route_optimization_constraints(constraint_factory))

    return constraints
