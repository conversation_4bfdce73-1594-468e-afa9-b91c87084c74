"""
Flexible Appointment Timing Optimization Constraint (C012)

This constraint handles appointment duration fitting and preferred hours optimization.
This includes both HARD constraints (duration fit) and SOFT constraints (preferred hours).
"""

from datetime import time, datetime

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint

from model.domain import Provider, AppointmentData
from model.planning_models import TimeSlotAssignment


def appointment_duration_fit(constraint_factory: ConstraintFactory) -> Constraint:
    """Appointment duration must fit within the assigned time slot."""
    # Handle Timefold validation calls where factory is None
    if constraint_factory is None:
        return constraint_factory.for_each(TimeSlotAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (constraint_factory
            .for_each(TimeSlotAssignment)
            .filter(lambda assignment: (assignment.time_slot is not None and
                                        assignment.scheduled_appointment.appointment_data.duration_min > 60))  # Assuming 1 hour slots
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: 1)
            .as_constraint("Appointment duration fit"))


def preferred_hours(constraint_factory: ConstraintFactory) -> Constraint:
    """Prefer assignments within provider's preferred working hours."""
    # Handle Timefold validation calls where factory is None
    if constraint_factory is None:
        return constraint_factory.for_each(TimeSlotAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (constraint_factory
            .for_each(TimeSlotAssignment)
            .filter(lambda assignment: (assignment.time_slot is not None and
                                        not _is_within_preferred_hours(assignment.scheduled_appointment.provider,
                                                                       assignment.scheduled_appointment.assigned_date,
                                                                       assignment.time_slot)))
            .penalize(HardSoftScore.ONE_SOFT, lambda assignment: 1)
            .as_constraint("Preferred hours"))


def _is_within_preferred_hours(provider: Provider, assigned_date, time_slot: time) -> bool:
    """Check if time slot is within provider's preferred working hours and not during break periods."""
    if provider.availability is None:
        # Fallback: standard healthcare hours: 8 AM to 6 PM
        preferred_start = 8  # 8 AM
        preferred_end = 18  # 6 PM
        slot_hour = time_slot.hour
        return preferred_start <= slot_hour < preferred_end

    # Create datetime for availability check
    check_datetime = datetime.combine(assigned_date, time_slot)

    # Use ProviderAvailability.is_available_at_time() which includes break_periods check
    return provider.availability.is_available_at_time(check_datetime)


def flexible_appointment_timing_optimization_constraints(factory: ConstraintFactory):
    """Return all flexible appointment timing optimization constraints."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return []
        
    return [
        appointment_duration_fit(factory),
        preferred_hours(factory),
    ]

# --- Public Utility Functions (moved from utils) ---
def is_urgent_appointment(appointment: AppointmentData) -> bool:
    """
    Check if an appointment is marked as urgent.
    
    Args:
        appointment: AppointmentData object
    
    Returns:
        bool: True if appointment is urgent, False otherwise
    """
    return appointment.urgent
