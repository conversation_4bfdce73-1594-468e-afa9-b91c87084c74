"""
RabbitMQ Integration for CareAXL Scheduling Engine.

This module provides RabbitMQ integration for production-ready event processing
in the hybrid service + event queue architecture.
"""

import json
import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable, Coroutine
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
from loguru import logger
import uuid
import ssl
from contextlib import asynccontextmanager

# Note: aio_pika would be used in production, but we'll create a mock for now
RABBITMQ_AVAILABLE = False
try:
    import aio_pika  # type: ignore
    RABBITMQ_AVAILABLE = True
except ImportError:
    logger.warning("aio_pika not available. Using mock RabbitMQ implementation.")


class RabbitMQConfig:
    """Configuration for RabbitMQ connection."""
    
    def __init__(self, 
                 host: str = "localhost",
                 port: int = 5672,
                 username: str = "guest",
                 password: str = "guest",
                 virtual_host: str = "/",
                 use_ssl: bool = False,
                 ssl_context: Optional[ssl.SSLContext] = None,
                 connection_timeout: int = 30,
                 heartbeat: int = 600):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.virtual_host = virtual_host
        self.use_ssl = use_ssl
        self.ssl_context = ssl_context
        self.connection_timeout = connection_timeout
        self.heartbeat = heartbeat
    
    def get_connection_url(self) -> str:
        """Get RabbitMQ connection URL."""
        protocol = "amqps" if self.use_ssl else "amqp"
        return f"{protocol}://{self.username}:{self.password}@{self.host}:{self.port}/{self.virtual_host}"


class EventPriority(Enum):
    """Event priority levels for RabbitMQ."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class EventType(Enum):
    """Event types for the scheduling system."""
    # Appointment events
    APPOINTMENT_CREATED = "appointment.created"
    APPOINTMENT_UPDATED = "appointment.updated"
    APPOINTMENT_CANCELLED = "appointment.cancelled"
    APPOINTMENT_ASSIGNED = "appointment.assigned"
    
    # Provider events
    PROVIDER_AVAILABILITY_CHANGED = "provider.availability.changed"
    PROVIDER_SCHEDULE_UPDATED = "provider.schedule.updated"
    
    # Patient events
    PATIENT_PREFERENCES_CHANGED = "patient.preferences.changed"
    PATIENT_CARE_EPISODE_STARTED = "patient.care_episode.started"
    PATIENT_CARE_EPISODE_ENDED = "patient.care_episode.ended"
    
    # Optimization events
    SCHEDULE_OPTIMIZATION_REQUESTED = "schedule.optimization.requested"
    SCHEDULE_OPTIMIZATION_COMPLETED = "schedule.optimization.completed"
    SCHEDULE_OPTIMIZATION_FAILED = "schedule.optimization.failed"
    
    # Notification events
    NOTIFICATION_SEND_REQUESTED = "notification.send.requested"
    NOTIFICATION_SENT = "notification.sent"
    NOTIFICATION_FAILED = "notification.failed"
    
    # Integration events
    EXTERNAL_SYSTEM_SYNC_REQUESTED = "external_system.sync.requested"
    EXTERNAL_SYSTEM_SYNC_COMPLETED = "external_system.sync.completed"
    EXTERNAL_SYSTEM_SYNC_FAILED = "external_system.sync.failed"


@dataclass
class Event:
    """Event data structure for RabbitMQ."""
    id: str
    type: EventType
    priority: EventPriority
    timestamp: datetime
    source: str
    data: Dict[str, Any]
    correlation_id: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary for serialization."""
        return {
            'id': self.id,
            'type': self.type.value,
            'priority': self.priority.value,
            'timestamp': self.timestamp.isoformat(),
            'source': self.source,
            'data': self.data,
            'correlation_id': self.correlation_id,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Event':
        """Create event from dictionary."""
        return cls(
            id=data['id'],
            type=EventType(data['type']),
            priority=EventPriority(data['priority']),
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data['source'],
            data=data['data'],
            correlation_id=data.get('correlation_id'),
            retry_count=data.get('retry_count', 0),
            max_retries=data.get('max_retries', 3)
        )


class MockRabbitMQEventBus:
    """Mock RabbitMQ event bus for development and testing."""
    
    def __init__(self, config: RabbitMQConfig, enable_file_logging: bool = True):
        self.config = config
        self.events: List[Event] = []
        self.handlers: Dict[EventType, List[Callable]] = {}
        self.logger = logger.bind(component="MockRabbitMQEventBus")
        self.connected = False
        self.enable_file_logging = enable_file_logging
        
        # Queue names for different priorities
        self.priority_queues = {
            EventPriority.LOW: "events.low",
            EventPriority.NORMAL: "events.normal", 
            EventPriority.HIGH: "events.high",
            EventPriority.CRITICAL: "events.critical"
        }
        
        # Dead letter queue for failed events
        self.dead_letter_queue = "events.dead_letter"
    
    async def connect(self) -> None:
        """Mock connection to RabbitMQ."""
        self.connected = True
        self.logger.info("Mock RabbitMQ connected")
    
    async def disconnect(self) -> None:
        """Mock disconnection from RabbitMQ."""
        self.connected = False
        self.logger.info("Mock RabbitMQ disconnected")
    
    async def publish_event(self, event: Event) -> None:
        """Publish an event to mock RabbitMQ."""
        if not self.connected:
            raise RuntimeError("Not connected to RabbitMQ")
        
        try:
            # Store event in memory
            self.events.append(event)
            
            # Process handlers immediately (for mock)
            await self._process_event(event)
            
            self.logger.info(f"Published event {event.id} ({event.type.value}) to Mock RabbitMQ")
            
        except Exception as e:
            self.logger.error(f"Failed to publish event {event.id}: {e}")
            raise
    
    async def publish_event_sync(self, event_type: EventType, data: Dict[str, Any],
                                priority: EventPriority = EventPriority.NORMAL,
                                source: str = "scheduler",
                                correlation_id: Optional[str] = None) -> Event:
        """Publish an event synchronously and return the event."""
        event = Event(
            id=str(uuid.uuid4()),
            type=event_type,
            priority=priority,
            timestamp=datetime.now(),
            source=source,
            data=data,
            correlation_id=correlation_id
        )
        
        await self.publish_event(event)
        return event
    
    async def subscribe(self, event_type: EventType, handler: Callable) -> None:
        """Subscribe a handler to an event type."""
        if event_type not in self.handlers:
            self.handlers[event_type] = []
        self.handlers[event_type].append(handler)
        self.logger.info(f"Handler subscribed to {event_type.value}")
    
    async def unsubscribe(self, event_type: EventType, handler: Callable) -> None:
        """Unsubscribe a handler from an event type."""
        if event_type in self.handlers:
            self.handlers[event_type] = [h for h in self.handlers[event_type] if h != handler]
            self.logger.info(f"Handler unsubscribed from {event_type.value}")
    
    async def _process_event(self, event: Event) -> None:
        """Process an event with handlers."""
        handlers = self.handlers.get(event.type, [])
        
        if not handlers:
            self.logger.warning(f"No handlers found for event type {event.type.value}")
            return
        
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event)
                else:
                    handler(event)
                
                self.logger.info(f"Event {event.id} handled successfully")
                
            except Exception as e:
                self.logger.error(f"Error handling event {event.id} with handler: {e}")
                
                # Handle retry logic
                if event.retry_count < event.max_retries:
                    event.retry_count += 1
                    self.logger.info(f"Retrying event {event.id} (attempt {event.retry_count})")
                    
                    # Re-publish with updated retry count
                    await self.publish_event(event)
                else:
                    self.logger.error(f"Event {event.id} failed after {event.max_retries} retries")
    
    async def start_consuming(self, queue_name: Optional[str] = None) -> None:
        """Mock consuming from RabbitMQ."""
        queue = queue_name or "events.normal"
        self.logger.info(f"Started mock consuming from queue: {queue}")
    
    async def get_queue_stats(self) -> Dict[str, Any]:
        """Get statistics for all queues."""
        stats = {}
        
        for priority, queue_name in self.priority_queues.items():
            stats[queue_name] = {
                'priority': priority.name,
                'message_count': len([e for e in self.events if e.priority == priority]),
                'consumer_count': 1
            }
        
        # Get dead letter queue stats
        stats[self.dead_letter_queue] = {
            'priority': 'DEAD_LETTER',
            'message_count': 0,
            'consumer_count': 0
        }
        
        return stats


class RabbitMQEventBusManager:
    """Manager for RabbitMQ event bus."""
    
    def __init__(self, config: RabbitMQConfig, enable_file_logging: bool = True):
        self.config = config
        # Use mock implementation for now
        self.event_bus = MockRabbitMQEventBus(config, enable_file_logging=enable_file_logging)
        
        self.consumer_task: Optional[asyncio.Task] = None
        self.logger = logger.bind(component="RabbitMQEventBusManager")
    
    async def start(self) -> None:
        """Start the RabbitMQ event bus."""
        await self.event_bus.connect()
        
        # Start consuming from all priority queues
        for priority, queue_name in self.event_bus.priority_queues.items():
            self.consumer_task = asyncio.create_task(
                self.event_bus.start_consuming(queue_name)
            )
        
        self.logger.info("RabbitMQ event bus manager started")
    
    async def stop(self) -> None:
        """Stop the RabbitMQ event bus."""
        if self.consumer_task:
            self.consumer_task.cancel()
            try:
                await self.consumer_task
            except asyncio.CancelledError:
                pass
        
        await self.event_bus.disconnect()
        self.logger.info("RabbitMQ event bus manager stopped")
    
    def get_event_bus(self):
        """Get the event bus instance."""
        return self.event_bus


# Global RabbitMQ event bus manager instance
_rabbitmq_manager: Optional[RabbitMQEventBusManager] = None


def initialize_rabbitmq(config: RabbitMQConfig, enable_file_logging: bool = True) -> None:
    """Initialize the global RabbitMQ event bus manager."""
    global _rabbitmq_manager
    _rabbitmq_manager = RabbitMQEventBusManager(config, enable_file_logging=enable_file_logging)


async def get_rabbitmq_event_bus():
    """Get the global RabbitMQ event bus instance."""
    if _rabbitmq_manager is None:
        raise RuntimeError("RabbitMQ not initialized. Call initialize_rabbitmq() first.")
    return _rabbitmq_manager.get_event_bus()


async def publish_event_rabbitmq(event_type: EventType, data: Dict[str, Any],
                                priority: EventPriority = EventPriority.NORMAL,
                                source: str = "scheduler",
                                correlation_id: Optional[str] = None) -> Event:
    """Publish an event to RabbitMQ."""
    event_bus = await get_rabbitmq_event_bus()
    return await event_bus.publish_event_sync(event_type, data, priority, source, correlation_id)


@asynccontextmanager
async def rabbitmq_connection(config: RabbitMQConfig, enable_file_logging: bool = True):
    """Context manager for RabbitMQ connection."""
    manager = RabbitMQEventBusManager(config, enable_file_logging=enable_file_logging)
    try:
        await manager.start()
        yield manager.get_event_bus()
    finally:
        await manager.stop() 