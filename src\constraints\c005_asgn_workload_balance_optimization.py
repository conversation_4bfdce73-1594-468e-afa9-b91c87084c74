"""
Workload Balance Optimization Constraint (C005)

This constraint balances workload across providers to prevent overloading.
This is a SOFT constraint for optimization preferences.
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint, Joiners
from typing import Optional

from model.domain import Provider
from model.planning_models import AppointmentAssignment


def workload_balancing(constraint_factory: ConstraintFactory, service_type: Optional[str] = None) -> Constraint:
    """Balance workload across providers to prevent overloading."""
    # Handle Timefold validation calls where factory is None
    if constraint_factory is None:
        return constraint_factory.for_each(Provider).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: (assignment.provider is not None and 
                                       assignment.provider.capacity is not None and
                                       assignment.assigned_date is not None))
            .group_by(lambda assignment: assignment.provider,
                      lambda assignment: assignment.assigned_date,
                      lambda assignment: 1)
            .filter(lambda provider, date, count: (provider is not None and 
                                                  provider.capacity is not None and
                                                  count > provider.capacity.max_tasks_count_in_day))
            .penalize(HardSoftScore.ONE_SOFT, lambda provider, date, count: 
                     count - (provider.capacity.max_tasks_count_in_day if provider and provider.capacity else 0))
            .as_constraint("C005_workload_balancing"))


def workload_distribution_balance(constraint_factory: ConstraintFactory, service_type: Optional[str] = None) -> Constraint:
    """Balance workload distribution across all providers to ensure fair distribution."""
    # Handle Timefold validation calls where factory is None
    if constraint_factory is None:
        return constraint_factory.for_each(Provider).filter(lambda x: False).as_constraint("no_op_constraint")
    
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: assignment.provider is not None and assignment.assigned_date is not None)
            .group_by(lambda assignment: assignment.assigned_date,
                      lambda assignment: assignment.provider,
                      lambda assignment: 1)
            .filter(lambda date, provider, count: count > 5)  # Penalize providers with more than 5 appointments per day
            .penalize(HardSoftScore.ONE_SOFT, lambda date, provider, count: count - 5)
            .as_constraint("C005_workload_distribution"))


def workload_balance_optimization_constraints(factory: ConstraintFactory):
    """Return all workload balance optimization constraints."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return []
        
    return [
        workload_balancing(factory),
        workload_distribution_balance(factory),
    ]
