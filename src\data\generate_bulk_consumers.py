import uuid
import random
import yaml
from datetime import datetime, timedelta, time, date

# Name pools for random generation
FIRST_NAMES = [
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Kyle", "Karen",
    "Walter", "Betty", "Ethan", "Helen", "Jeremy", "Sandra", "Harold", "Donna",
    "Carl", "Carol", "Keith", "Ruth", "Roger", "Sharon", "Gerald", "Michelle",
    "Christian", "Laura", "Terry", "Emily", "Sean", "Kimberly", "Gavin", "Deborah",
    "Austin", "Dorothy", "Arthur", "Lisa", "Noah", "Nancy", "Lawrence", "Karen"
]
LAST_NAMES = [
    "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
    "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson",
    "Thomas", "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson",
    "White", "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson", "Walker",
    "Young", "Allen", "King", "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores",
    "Green", "Adams", "Nelson", "Baker", "Hall", "Rivera", "Campbell", "Mitchell",
    "Carter", "Roberts", "Gomez", "Phillips", "Evans", "Turner", "Diaz", "Parker",
    "Cruz", "Edwards", "Collins", "Reyes", "Stewart", "Morris", "Morales", "Murphy",
    "Cook", "Rogers", "Gutierrez", "Ortiz", "Morgan", "Cooper", "Peterson", "Bailey",
    "Reed", "Kelly", "Howard", "Ramos", "Kim", "Cox", "Ward", "Richardson", "Watson",
    "Brooks", "Chavez", "Wood", "James", "Bennett", "Gray", "Mendoza", "Ruiz",
    "Hughes", "Price", "Alvarez", "Castillo", "Sanders", "Patel", "Myers", "Long",
    "Ross", "Foster", "Jimenez", "Powell", "Jenkins", "Perry", "Russell", "Sullivan",
    "Bell", "Coleman", "Butler", "Henderson", "Barnes", "Gonzales", "Fisher", "Vasquez",
    "Simmons", "Romero", "Jordan", "Patterson", "Alexander", "Hamilton", "Graham",
    "Reynolds", "Griffin", "Wallace", "Moreno", "West", "Cole", "Hayes", "Bryant",
    "Herrera", "Gibson", "Ellis", "Tran", "Medina", "Aguilar", "Stevens", "Murray",
    "Ford", "Castro", "Marshall", "Owens", "Harrison", "Fernandez", "Mcdonald", "Woods",
    "Washington", "Kennedy", "Wells", "Vargas", "Henry", "Chen", "Freeman", "Webb",
    "Tucker", "Guzman", "Burns", "Crawford", "Olson", "Simpson", "Porter", "Hunter",
    "Gordon", "Mendez", "Silva", "Shaw", "Snyder", "Mason", "Dixon", "Muñoz",
    "Hunt", "Hicks", "Holmes", "Palmer", "Wagner", "Black", "Robertson", "Boyd",
    "Rose", "Stone", "Salazar", "Fox", "Warren", "Mills", "Meyer", "Rice", "Schmidt",
    "Garza", "Daniels", "Ferguson", "Nichols", "Stephens", "Soto", "Weaver", "Ryan",
    "Gardner", "Payne", "Grant", "Dunn", "Kelley", "Spencer", "Hawkins", "Arnold",
    "Pierce", "Vazquez", "Hansen", "Peters", "Santos", "Hart", "Bradley", "Knight",
    "Elliott", "Cunningham", "Duncan", "Armstrong", "Hudson", "Carroll", "Lane",
    "Riley", "Andrews", "Alvarado", "Ray", "Delgado", "Berry", "Perkins", "Hoffman",
    "Johnston", "Matthews", "Pena", "Richards", "Contreras", "Willis", "Carpenter",
    "Lawrence", "Sandoval", "Guerrero", "George", "Chapman", "Rios", "Estrada",
    "Ortega", "Watkins", "Greene", "Nunez", "Wheeler", "Valdez", "Harper", "Burke",
    "Larson", "Santiago", "Maldonado", "Morrison", "Franklin", "Erickson", "Schultz",
    "Fuller", "Williamson", "Ray", "Montgomery", "Harvey", "Oliver", "Howell", "Dean",
    "Meyer", "Garrett", "Romero", "Jacobs", "McDonald", "Gilbert", "Hansen", "Wells",
    "McDaniel", "Vargas", "Terry", "Herrera", "Wade", "Curtis", "Reed", "Navarro",
    "Coleman", "Mangum", "Townsend", "Potts", "Goodwin", "Walton", "Rocha", "Clements",
    "Koch", "Shepherd", "Bates", "Strickland", "Jensen", "Gardner", "Payne", "Vaughn",
    "Byrd", "Blackburn", "Doty", "Frey", "Cleveland", "Booker", "Hester", "Moon",
    "Cochran", "McKenna", "Dunlap", "Talley", "Childers", "Burch", "Leach", "Moss",
    "Vang", "Dorsey", "Mcconnell", "Gay", "Brinkley", "Newell", "Noel", "Meyer"
]

# Generate a random name
random_name = lambda: f"{random.choice(FIRST_NAMES)} {random.choice(LAST_NAMES)}"

# Generate a random address
random_address = lambda i: f"{300 + i} Consumer Ave, New York, NY 10001"

# Generate preferences
def generate_preferences():
    preferences = {
        "preferred_days": [],
        "preferred_hours": None,
        "unavailable_days": [],
        "unavailable_hours": [],
        "cultural_considerations": [],
        "language": None,
        "gender_preference": None,
        "preferred_providers": [],
        "properties": {}
    }
    
    # 30% chance of having preferred days
    if random.random() < 0.3:
        num_days = random.randint(1, 3)
        days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
        preferences["preferred_days"] = random.sample(days, num_days)
    
    # 40% chance of having preferred hours
    if random.random() < 0.4:
        start_hour = random.randint(8, 12)
        end_hour = start_hour + random.randint(2, 6)
        preferences["preferred_hours"] = [f"{start_hour:02d}:00", f"{end_hour:02d}:00"]
    
    # 20% chance of having cultural considerations
    if random.random() < 0.2:
        cultural_reqs = [
            "wheelchair_accessible", "quiet_environment", "pet_friendly", 
            "parking_required", "elevator_access", "ground_floor_only"
        ]
        num_reqs = random.randint(1, 2)
        preferences["cultural_considerations"] = random.sample(cultural_reqs, num_reqs)
    
    # 25% chance of having language preference
    if random.random() < 0.25:
        preferences["language"] = random.choice(["English", "Spanish", "French", "Mandarin", "Russian"])
    
    # 15% chance of having gender preference
    if random.random() < 0.15:
        preferences["gender_preference"] = random.choice(["male", "female", "no_preference"])
    
    return preferences

# Generate 100 consumers (matching the number of appointments)
bulk_consumers = []
for i in range(1, 101):
    consumer = {
        "id": str(uuid.uuid4()),  # UUID as required by domain model
        "name": random_name(),
        "location": {
            "latitude": round(random.uniform(40.70, 40.80), 5),
            "longitude": round(random.uniform(-74.02, -73.95), 5),
            "address": random_address(i),
            "city": "New York",
            "state": "NY",
            "country": "USA",
            "zip_code": "10001"
        },
        "care_episode_id": f"episode-{random.randint(1000, 9999)}" if random.random() < 0.3 else None,
        "consumer_preferences": generate_preferences(),
        "properties": {}
    }
    bulk_consumers.append(consumer)

# Load the existing YAML
try:
    with open("consumers.yml", "r", encoding="utf-8") as f:
        data = yaml.safe_load(f)
    # Handle empty file or None
    if data is None:
        data = {"consumers": []}
except FileNotFoundError:
    data = {"consumers": []}

# Append the new consumers
if "consumers" in data:
    data["consumers"].extend(bulk_consumers)
else:
    data["consumers"] = bulk_consumers

# Write back to the YAML file
with open("consumers.yml", "w", encoding="utf-8") as f:
    yaml.dump(data, f, sort_keys=False, allow_unicode=True)

print("Appended 100 bulk consumers to consumers.yml") 