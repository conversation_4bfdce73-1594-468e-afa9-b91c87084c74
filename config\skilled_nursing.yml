service_type: skilled_nursing
required_skills:
  - "medication_administration"
  - "wound_care"
  - "vital_signs"
  - "catheter_care"
  - "iv_therapy"
  - "assessment"

geographic_radius_miles: 25.0
max_daily_appointments_per_provider: 8
max_weekly_hours_per_provider: 40

# Constraint weights (0.0 to 1.0)
continuity_weight: 0.8
workload_balance_weight: 0.7
geographic_clustering_weight: 0.5
patient_preference_weight: 0.6
capacity_threshold_percentage: 0.9

# Role hierarchy configuration
enable_role_hierarchy: true
role_hierarchy:
  "RN": ["LPN", "CNA"]
  "LPN": ["CNA"]
  "CNA": []  # No lower-level roles can perform CNA work

role_hierarchy_penalties:
  "RN->LPN": 10
  "RN->CNA": 20
  "LPN->CNA": 15

# Service-specific settings
visit_duration_minutes: 60
requires_initial_assessment: true
allows_weekend_visits: true
emergency_response_time_hours: 4

# Geographic clustering settings
cluster_radius_miles: 15.0
max_cluster_size: 8
prefer_same_day_clustering: true

# Continuity of care settings
continuity_priority_bonus: 100
continuity_threshold_days: 30
existing_relationship_bonus: 60

# Workload balancing settings
target_daily_appointments: 6
workload_variance_tolerance: 2
overtime_penalty_multiplier: 1.3

# Patient preference settings
preferred_provider_bonus: 40
preferred_time_bonus: 25
preferred_location_bonus: 20

# =============================================================================
# SERVICE-SPECIFIC AVAILABILITY CONFIGURATION
# =============================================================================

# Service-specific availability settings
service_availability:
  # Service-specific blackout periods
  blackout_periods: []
  
  # Service-specific holidays (no additional state holidays for skilled nursing)
  holidays: []
  
  # Service-specific weekend coverage (7 days a week)
  weekend_coverage:
    enabled: true
    saturday_coverage: true
    sunday_coverage: true
    weekend_penalty: 3 