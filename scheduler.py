#!/usr/bin/env python3
"""
Scheduler for running appointment scheduling jobs.

This module provides a scheduler that can run both:
1. AssignAppointment job (nightly) - assigns date and provider
2. DayPlan job (daily) - assigns time slots to daily appointments
"""

import argparse
import sys
import time
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))
from datetime import date, datetime, timedelta
from typing import Optional, Union

import schedule
from assign_appointments import AssignAppointmentJob
from day_plan import DayPlanJob
from model.domain import BatchAssignmentResult
from constraints.config_registry import ConfigRegistry
from loguru import logger

log_dir = os.path.join(os.path.dirname(__file__), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"scheduler_{datetime.now().strftime('%Y-%m-%d')}.log")
logger.add(log_file, rotation="1 day", retention="7 days", level="INFO")


class AppointmentScheduler:
    """Main scheduler for appointment scheduling jobs."""

    def __init__(self, config_folder: str = "config", daemon_mode: bool = False):
        # Load configurations using the new registry
        ConfigRegistry.load_configurations(config_folder)
        self.scheduler_config = ConfigRegistry.get_core_config('scheduler')
        self.assign_job = AssignAppointmentJob(config_folder, daemon_mode=daemon_mode)
        self.day_plan_job = DayPlanJob(config_folder, daemon_mode=daemon_mode)
        self.daemon_mode = daemon_mode
        self._running = False

    def run_assign_appointments(self) -> dict:
        """Run the AssignAppointment job."""
        try:
            logger.info("Starting AssignAppointment job...")
            result = self.assign_job.run()
            summary = result.get('summary', {})
            logger.info(
                f"AssignAppointment job completed: {summary.get('assigned_appointments', 0)}/{summary.get('total_appointments', 0)} assigned")
            return result
        except Exception as e:
            logger.error(f"AssignAppointment job failed: {e}")
            raise

    def run_day_plan(self, target_date: Optional[date] = None) -> BatchAssignmentResult:
        """Run the DayPlan job for a specific date."""
        try:
            logger.info(f"Starting DayPlan job for {target_date or date.today()}...")
            result = self.day_plan_job.run(target_date)
            logger.info(
                f"DayPlan job completed: {result.assigned_appointments}/{result.total_appointments} time slots assigned")
            return result
        except Exception as e:
            logger.error(f"DayPlan job failed: {e}")
            raise

    def run_today_day_plan(self) -> BatchAssignmentResult:
        """Run the DayPlan job for today."""
        return self.run_day_plan(date.today())

    def run_tomorrow_day_plan(self) -> BatchAssignmentResult:
        """Run the DayPlan job for tomorrow."""
        return self.run_day_plan(date.today() + timedelta(days=1))

    def setup_schedule(self):
        """Setup the job schedule."""
        # AssignAppointment job runs nightly at 2 AM
        schedule.every().day.at("02:00").do(self.run_assign_appointments)

        # DayPlan job runs daily at 6 AM for the current day
        schedule.every().day.at("06:00").do(self.run_today_day_plan)

        logger.info("Schedule setup complete:")
        logger.info("  - AssignAppointment job: Daily at 02:00")
        logger.info("  - DayPlan job: Daily at 06:00")

    def run_daemon(self):
        """Run the scheduler as a daemon."""
        self._running = True
        self.setup_schedule()

        logger.info("Starting scheduler daemon...")
        logger.info("Press Ctrl+C to stop")

        try:
            while self._running:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        except KeyboardInterrupt:
            logger.info("Scheduler daemon stopped by user")
        except Exception as e:
            logger.error(f"Scheduler daemon error: {e}")
            raise
        finally:
            self._running = False

    def run_once(self, job_type: str = "assign", target_date: Optional[date] = None) -> Union[
        dict, BatchAssignmentResult]:
        """
        Run a single job once.
        
        Args:
            job_type: "assign" for AssignAppointment, "dayplan" for DayPlan
            target_date: Target date for DayPlan job (optional)
        """
        if job_type.lower() == "assign":
            return self.run_assign_appointments()
        elif job_type.lower() == "dayplan":
            return self.run_day_plan(target_date)
        else:
            raise ValueError(f"Unknown job type: {job_type}. Use 'assign' or 'dayplan'")

    def stop(self):
        """Stop the scheduler daemon."""
        self._running = False
        logger.info("Scheduler stopped")


def main():
    """Main entry point for the scheduler."""
    parser = argparse.ArgumentParser(description='Appointment Scheduler')
    parser.add_argument('--config-folder', default='config', help='Configuration folder path')
    parser.add_argument('--mode', choices=['daemon', 'once'], default='daemon',
                        help='Run mode: daemon (continuous) or once (single execution)')
    parser.add_argument('--job', choices=['assign', 'dayplan'],
                        help='Job type to run (for once mode)')
    parser.add_argument('--date', help='Target date for dayplan job (YYYY-MM-DD format)')

    args = parser.parse_args()

    try:
        # Determine daemon mode based on scheduler mode
        daemon_mode = args.mode == 'daemon'
        scheduler = AppointmentScheduler(args.config_folder, daemon_mode=daemon_mode)

        if args.mode == 'daemon':
            scheduler.run_daemon()
        elif args.mode == 'once':
            if not args.job:
                print("Error: --job is required for once mode")
                sys.exit(1)

            target_date = None
            if args.date:
                target_date = datetime.strptime(args.date, '%Y-%m-%d').date()

            result = scheduler.run_once(args.job, target_date)

            print(f"\n=== Job Results ===")
            print(f"Job Type: {args.job}")

            if isinstance(result, dict):
                # AssignAppointment job returns a dict
                summary = result.get('summary', {})
                print(f"Total: {summary.get('total_appointments', 0)}")
                print(f"Assigned: {summary.get('assigned_appointments', 0)}")
                print(f"Unassigned: {summary.get('unassigned_appointments', 0)}")
                
                # Handle processing time safely
                processing_time = result.get('processing_time', 0)
                if processing_time is not None:
                    try:
                        processing_time_float = float(processing_time)
                        print(f"Processing Time: {processing_time_float:.2f}s")
                    except (ValueError, TypeError):
                        print(f"Processing Time: {processing_time}")
                else:
                    print("Processing Time: N/A")
            else:
                # DayPlan job returns BatchAssignmentResult
                print(f"Total: {result.total_appointments}")
                print(f"Assigned: {result.assigned_appointments}")
                print(f"Unassigned: {result.unassigned_appointments}")
                print(f"Processing Time: {result.processing_time_seconds:.2f}s")

            # Exit after completion in once mode
            logger.info("Job completed in once mode. Exiting...")
            sys.exit(0)

    except Exception as e:
        logger.error(f"Scheduler failed: {e}")
        logger.error(f"Exception type: {type(e)}")
        logger.error(f"Exception details: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        if args.mode == 'once':
            sys.exit(1)
        raise


if __name__ == "__main__":
    main()
