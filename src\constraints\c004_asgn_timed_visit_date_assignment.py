"""
Timed Visit Date Assignment Constraint (C004)

This constraint handles appointment date assignment within the rolling window:
- HARD CONSTRAINT: Timed visits must be assigned to their requested date
- SOFT CONSTRAINT: Flexible visits can be pushed to later dates with penalties

The rolling window is configured at the scheduler level and allows flexible
appointments to be distributed across multiple days for better workload balancing.
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint
from loguru import logger
from datetime import date, timedelta
from typing import Optional

from model.planning_models import AppointmentAssignment
from .base_constraints import with_config

# Constraint identifiers
TIMED_VISIT_DATE_ASSIGNMENT = "C004_timed_visit_date_assignment"
FLEXIBLE_VISIT_ROLLING_WINDOW = "C004_flexible_visit_rolling_window"


def _get_rolling_window_days():
    """Get rolling window days from scheduler config."""
    try:
        # Use ConfigRegistry to get scheduler config
        from .base_constraints import ConfigRegistry
        config = ConfigRegistry.get_core_config('scheduler')
        return config.get('rolling_window_days', 7) if config else 7
    except Exception as e:
        logger.warning(f"Could not load scheduler config, using default rolling window: {e}")
        return 7


@with_config('scheduler')
def timed_visit_date_assignment(constraint_factory: ConstraintFactory, core_config=None, service_config=None, combined_config=None, **kwargs) -> Constraint:
    """
    HARD CONSTRAINT: Timed visits must be assigned to their requested date.
    
    Real-world scenario: A patient needs insulin injection at exactly 8 AM on a specific date.
    This constraint ensures that timed appointments cannot be moved to different dates,
    preventing health risks from missed medication timing.
    """
    # Handle Timefold validation calls where factory is None
    if constraint_factory is None:
        return constraint_factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: (
                assignment.provider is not None and
                assignment.assigned_date is not None and
                assignment.appointment_data.timing.is_timed_visit and
                assignment.assigned_date != assignment.appointment_data.appointment_date))
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: _log_timed_violation(assignment))
            .as_constraint(TIMED_VISIT_DATE_ASSIGNMENT))


@with_config('scheduler')
def flexible_visit_rolling_window(constraint_factory: ConstraintFactory, core_config=None, service_config=None, combined_config=None, **kwargs) -> Constraint:
    """
    SOFT CONSTRAINT: Penalize pushing flexible visits beyond their requested date.
    
    Real-world scenario: A patient requests a home health visit on Monday,
    but due to workload balancing, it gets pushed to Wednesday. This constraint
    applies a soft penalty for each day pushed, encouraging the system to
    schedule visits as close to the requested date as possible while still
    allowing flexibility for workload optimization.
    """
    # Handle Timefold validation calls where factory is None
    if constraint_factory is None:
        return constraint_factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
    
    rolling_window_days = _get_rolling_window_days()
    
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: (
                assignment.provider is not None and
                assignment.assigned_date is not None and
                not assignment.appointment_data.timing.is_timed_visit and
                not assignment.appointment_data.urgent))
            .penalize(HardSoftScore.ONE_SOFT, 
                     lambda assignment: _calculate_rolling_window_penalty(assignment, rolling_window_days))
            .as_constraint(FLEXIBLE_VISIT_ROLLING_WINDOW))


def _log_timed_violation(assignment: AppointmentAssignment) -> int:
    """Log timed visit violation and return penalty."""
    logger.debug("HARD CONSTRAINT VIOLATION: Timed visit for {} moved from {} to {}", 
                  assignment.appointment_data.id,
                  assignment.appointment_data.appointment_date,
                  assignment.assigned_date)
    return 1


def _calculate_rolling_window_penalty(assignment: AppointmentAssignment, rolling_window_days: int) -> int:
    """
    Calculate penalty for pushing flexible appointments beyond their requested date.
    
    Args:
        assignment: The appointment assignment
        rolling_window_days: Number of days in the rolling window
        
    Returns:
        Penalty points (0 if no delay, 1 per day delayed up to rolling window limit)
    """
    if assignment.assigned_date is None:
        return 0
    
    requested_date = assignment.appointment_data.appointment_date
    days_difference = (assignment.assigned_date - requested_date).days
    
    # No penalty if assigned on or before requested date
    if days_difference <= 0:
        return 0
    
    # Penalty for each day pushed, but only within rolling window
    # Beyond rolling window would be handled by other constraints
    penalty = min(days_difference, rolling_window_days - 1)
    
    if penalty > 0:
        logger.debug("Flexible visit {} pushed {} days from {} to {} (penalty: {})", 
                    assignment.appointment_data.id,
                    days_difference,
                    requested_date,
                    assignment.assigned_date,
                    penalty)
    
    return penalty


def timed_visit_date_assignment_constraints(factory: ConstraintFactory):
    """Return all timed visit date assignment constraints."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return []
        
    return [
        timed_visit_date_assignment(factory),      # Hard constraint for timed visits
        flexible_visit_rolling_window(factory),    # Soft constraint for flexible visits
    ]
