from typing import Dict, Any
import threading

class ConfigRegistry: 
    _core_configs = {} 
    _service_configs = {} 
    _current_service_context = threading.local()  # Thread-local storage for current service context
 
    @classmethod 
    def get_combined_config(cls, service_name): 
        scheduler_config = cls._core_configs.get('scheduler', {}) 
        service_config = cls._service_configs.get(service_name, {}) 
        return {**scheduler_config, **service_config} 
 
    @classmethod 
    def register_core_config(cls, config_name, config_data): 
        cls._core_configs[config_name] = config_data 
 
    @classmethod
    def register_service_config(cls, service_name, config_data):
        cls._service_configs[service_name] = config_data

    @classmethod
    def get_core_config(cls, config_name):
        return cls._core_configs.get(config_name)

    @classmethod
    def get_service_config(cls, service_name):
        return cls._service_configs.get(service_name)

    @classmethod
    def get_current_service_config(cls):
        """Get the current service config from the decorator context."""
        if hasattr(cls._current_service_context, 'current_service'):
            return cls.get_service_config(cls._current_service_context.current_service)
        return None

    @classmethod
    def set_current_service_context(cls, service_name):
        """Set the current service context for decorators."""
        cls._current_service_context.current_service = service_name

    @classmethod
    def clear_current_service_context(cls):
        """Clear the current service context."""
        if hasattr(cls._current_service_context, 'current_service'):
            delattr(cls._current_service_context, 'current_service')

    @classmethod
    def clear_configs(cls):
        cls._core_configs.clear()
        cls._service_configs.clear()

    @classmethod
    def load_configurations(cls, config_dir='config'):
        import yaml
        from pathlib import Path
        config_path = Path(config_dir)
        # Load core configs
        for config_file in ['scheduler.yml', 'logger.yaml']:
            file_path = config_path / config_file
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                    config_name = config_file.replace('.yml', '').replace('.yaml', '')
                    cls.register_core_config(config_name, config_data)
        # Load service configs
        for service_file in ['behavioral_care.yml', 'hospital_at_home.yml', 'pcs.yml', 'skilled_nursing.yml']:
            file_path = config_path / service_file
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                    service_name = service_file.replace('.yml', '')
                    cls.register_service_config(service_name, config_data)

    @classmethod
    def get_available_services(cls) -> list:
        """Get list of available service names"""
        return list(cls._service_configs.keys())
    
    @classmethod
    def get_all_service_configs(cls) -> Dict[str, Any]:
        """Get all service-specific configurations"""
        return cls._service_configs.copy()
