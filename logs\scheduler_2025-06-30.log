2025-06-30 23:44:47 | INFO     | assign_appointments:setup_logging:67 - Logging configured - console (INFO+) and file (DEBUG+): logs\scheduler_2025-06-30.log
2025-06-30 23:44:47 | WARNING  | events.rabbitmq_integration:<module>:26 - aio_pika not available. Using mock RabbitMQ implementation.
2025-06-30 23:44:47.845 | WARNING  | events.rabbitmq_integration:<module>:26 - aio_pika not available. Using mock RabbitMQ implementation.
2025-06-30 23:44:47 | INFO     | data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:44:47.847 | INFO     | data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:44:47 | INFO     | data.data_loader:load_all_data:153 - Starting data loading process...
2025-06-30 23:44:47.848 | INFO     | data.data_loader:load_all_data:153 - Starting data loading process...
2025-06-30 23:44:47 | INFO     | data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:44:47.848 | INFO     | data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:44:47 | INFO     | data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:44:47.849 | INFO     | data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:44:47 | INFO     | data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:44:47.985 | INFO     | data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Daniel Brown (LPN) - Skills: wound_care, assessment
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Ryan Rodriguez (LPN) - Skills: personal_care, mobility_assistance
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Nicole Lopez (PT) - Skills: assessment, meal_assistance, personal_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michael Martin (OT) - Skills: assessment, meal_assistance, wound_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Garcia (LPN) - Skills: housekeeping, mobility_assistance
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Jessica Davis (RN) - Skills: housekeeping, wound_care, meal_assistance
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Kevin Rodriguez (OT) - Skills: iv_therapy, mobility_assistance, wound_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Kevin Thompson (LPN) - Skills: wound_care, medication_management, meal_assistance
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: vital_signs, meal_assistance, mobility_assistance
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Stephanie Jones (RN) - Skills: medication_management, meal_assistance, housekeeping
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Christopher Johnson (OT) - Skills: wound_care, assessment, vital_signs
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. James Martinez (OT) - Skills: wound_care, meal_assistance
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. James Hernandez (OT) - Skills: assessment, vital_signs, wound_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Nicole Thompson (CNA) - Skills: meal_assistance, assessment
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Stephanie Hernandez (CNA) - Skills: meal_assistance, vital_signs, assessment
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Miller (RN) - Skills: iv_therapy, vital_signs, medication_management
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Stephanie Anderson (CNA) - Skills: iv_therapy, wound_care, mobility_assistance
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: meal_assistance, assessment, mobility_assistance
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Moore (LPN) - Skills: housekeeping, vital_signs, wound_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: medication_management, assessment, wound_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lauren Miller (OT) - Skills: medication_management, vital_signs, mobility_assistance
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Amanda Lee (PT) - Skills: iv_therapy, mobility_assistance, medication_management
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lisa Martin (OT) - Skills: mobility_assistance, housekeeping, assessment
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Andrew Martin (CNA) - Skills: mobility_assistance, meal_assistance, personal_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lisa Rodriguez (RN) - Skills: iv_therapy, wound_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. David Moore (LPN) - Skills: vital_signs, housekeeping, personal_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Christopher Lee (PT) - Skills: assessment, iv_therapy
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. William Johnson (LPN) - Skills: housekeeping, meal_assistance, wound_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Emily Thompson (LPN) - Skills: medication_management, mobility_assistance, housekeeping
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Andrew Garcia (RN) - Skills: mobility_assistance, wound_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michael Wilson (RN) - Skills: medication_management, housekeeping, wound_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Rachel Rodriguez (OT) - Skills: vital_signs, wound_care, mobility_assistance
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Lopez (OT) - Skills: vital_signs, mobility_assistance, assessment
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lauren Brown (RN) - Skills: medication_management, meal_assistance, wound_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: medication_management, assessment, personal_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Lee (OT) - Skills: meal_assistance, assessment, housekeeping
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Rachel Jackson (PT) - Skills: meal_assistance, assessment, iv_therapy
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Daniel Davis (RN) - Skills: meal_assistance, mobility_assistance, personal_care
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Nicole Martin (RN) - Skills: personal_care, mobility_assistance, iv_therapy
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Jessica Perez (LPN) - Skills: medication_management, personal_care, iv_therapy
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Ashley Wilson (PT) - Skills: iv_therapy, personal_care, meal_assistance
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Kevin Jones (PT) - Skills: vital_signs, housekeeping, medication_management
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Ryan Martin (RN) - Skills: assessment, meal_assistance, housekeeping
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lauren Martinez (CNA) - Skills: medication_management, iv_therapy
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Emily Johnson (OT) - Skills: medication_management, assessment
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Moore (RN) - Skills: mobility_assistance, medication_management, assessment
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michelle Garcia (LPN) - Skills: medication_management, meal_assistance, assessment
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michelle Anderson (CNA) - Skills: mobility_assistance, medication_management, vital_signs
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Jennifer Moore (LPN) - Skills: housekeeping, medication_management, mobility_assistance
2025-06-30 23:44:47 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. James Moore (PT) - Skills: personal_care, assessment, iv_therapy
2025-06-30 23:44:47 | INFO     | data.data_loader:load_consumers:87 - === STAGE 2: Loading Consumer Data ===
2025-06-30 23:44:47.987 | INFO     | data.data_loader:load_consumers:87 - === STAGE 2: Loading Consumer Data ===
2025-06-30 23:44:47 | INFO     | data.data_loader:load_consumers:101 - Reading consumer data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\consumers.yml
2025-06-30 23:44:47.987 | INFO     | data.data_loader:load_consumers:101 - Reading consumer data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\consumers.yml
2025-06-30 23:44:48 | INFO     | data.data_loader:load_consumers:111 - ✅ Consumer data loaded: 100 consumers
2025-06-30 23:44:48.093 | INFO     | data.data_loader:load_consumers:111 - ✅ Consumer data loaded: 100 consumers
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Collins - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Garrett - Episode: episode-2440
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Mendoza - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Matthew Rose - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Burke - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Edward Williams - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Linda Palmer - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Cole - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - George Moreno - Episode: episode-2280
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Paul Olson - Episode: episode-6025
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Laura Cox - Episode: episode-8358
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Aguilar - Episode: episode-4641
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Kimberly Gardner - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Guerrero - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Nguyen - Episode: episode-5132
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Carl Rios - Episode: episode-5178
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Helen Townsend - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Daniel Wheeler - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Jonathan Kennedy - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Betty Harrison - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Patricia Gordon - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Lawrence Gonzalez - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Douglas Mcdonald - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Davis - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Evans - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Dennis Romero - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth McKenna - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Terry - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Terry Alexander - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Joshua Black - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Powell - Episode: episode-6559
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Wilson - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Patricia Hoffman - Episode: episode-7856
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Donald Aguilar - Episode: episode-7031
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Jacobs - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - George Henry - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Gregory Perry - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Jacob McDonald - Episode: episode-5471
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Peter Hicks - Episode: episode-3136
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Karen Alexander - Episode: episode-8466
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Rose - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Russell - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Sarah Carroll - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Barbara Reyes - Episode: episode-6626
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Weaver - Episode: episode-7890
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Laura McKenna - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Vazquez - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy McDaniel - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Justin Cleveland - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Carl Fernandez - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Laura Franklin - Episode: episode-2981
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Mason - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Karen Soto - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Christian Kim - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Betty Johnston - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Paul Herrera - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Kyle White - Episode: episode-2605
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Jerry Spencer - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Perry - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Charles Griffin - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - William Diaz - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Tyler Gonzalez - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Emily Cooper - Episode: episode-1979
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Jacob Murray - Episode: episode-5269
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Ray - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Nancy Wheeler - Episode: episode-3169
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Deborah Stephens - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Anthony Daniels - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Gregory Wilson - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Donald Herrera - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Flores - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Deborah Warren - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Brandon Morgan - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Rogers - Episode: episode-3059
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Wheeler - Episode: episode-2116
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Douglas Taylor - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Mark Pierce - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Emily Jensen - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Paul Webb - Episode: episode-9774
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Thompson - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Terry Dorsey - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Michael Hart - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Sarah Morgan - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Sean Thomas - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Gutierrez - Episode: episode-4864
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Johnson - Episode: episode-9681
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Nancy Andrews - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Arthur George - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Riley - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Linda Chavez - Episode: episode-4538
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Lane - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Kimberly Alvarez - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Hughes - Episode: episode-2864
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Richards - Episode: episode-4001
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Shaw - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Joshua Gray - Episode: episode-2319
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Sharon Mendoza - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Matthew Ruiz - Episode: episode-4651
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Betty Arnold - Episode: None
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_consumers:113 -    - Gregory Soto - Episode: None
2025-06-30 23:44:48 | INFO     | data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:44:48.095 | INFO     | data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:44:48 | INFO     | data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:44:48.095 | INFO     | data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:44:48 | INFO     | data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:44:48.239 | INFO     | data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-03
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 120min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-03
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-03
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 60min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 75min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 75min - 2025-07-03
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 75min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-07-22
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 60min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-15
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 75min - 2025-07-03
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 90min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 45min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 45min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 90min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 30min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-22
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 75min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-03
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 75min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-07-03
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-22
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-22
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 75min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 75min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-15
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-22
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-22
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 30min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 120min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 45min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-03
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 90min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 30min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-03
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-15
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-06-30
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 30min - 2025-07-22
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-03
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-07-22
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 60min - 2025-07-15
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-08
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-22
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-03
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-07-02
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-01
2025-06-30 23:44:48 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 90min - 2025-07-02
2025-06-30 23:44:48 | INFO     | data.data_loader:load_all_data:159 - === DATA LOADING SUMMARY ===
2025-06-30 23:44:48.243 | INFO     | data.data_loader:load_all_data:159 - === DATA LOADING SUMMARY ===
2025-06-30 23:44:48 | INFO     | data.data_loader:load_all_data:160 - Total records loaded:
2025-06-30 23:44:48.244 | INFO     | data.data_loader:load_all_data:160 - Total records loaded:
2025-06-30 23:44:48 | INFO     | data.data_loader:load_all_data:161 -    - Providers: 50
2025-06-30 23:44:48.244 | INFO     | data.data_loader:load_all_data:161 -    - Providers: 50
2025-06-30 23:44:48 | INFO     | data.data_loader:load_all_data:162 -    - Consumers: 100
2025-06-30 23:44:48.244 | INFO     | data.data_loader:load_all_data:162 -    - Consumers: 100
2025-06-30 23:44:48 | INFO     | data.data_loader:load_all_data:163 -    - Appointments: 100
2025-06-30 23:44:48.244 | INFO     | data.data_loader:load_all_data:163 -    - Appointments: 100
2025-06-30 23:44:48 | INFO     | data.data_loader:load_all_data:164 - All data loaded successfully!
2025-06-30 23:44:48.244 | INFO     | data.data_loader:load_all_data:164 - All data loaded successfully!
2025-06-30 23:44:48 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:44:48.245 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:44:48 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:44:48.245 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:44:48 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:44:48.245 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:44:48 | INFO     | services.scheduler_service:_init_mock_services:388 - Mock service clients initialized
2025-06-30 23:44:48.245 | INFO     | services.scheduler_service:_init_mock_services:388 - Mock service clients initialized
2025-06-30 23:44:48 | INFO     | services.scheduler_service:_init_rabbitmq:377 - RabbitMQ initialized successfully
2025-06-30 23:44:48.246 | INFO     | services.scheduler_service:_init_rabbitmq:377 - RabbitMQ initialized successfully
2025-06-30 23:44:48 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:44:48.246 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:44:48 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:44:48.247 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:44:48 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:44:48.270 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:44:48 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:44:48.270 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:44:48 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:44:48.271 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:44:48 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:44:48.272 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:44:53.577 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:44:53.577 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:44:53.584 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:44:53.584 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:44:53 | INFO     | services.scheduler_service:_register_event_handlers:435 - Event handlers would be registered in production RabbitMQ
2025-06-30 23:44:53.619 | INFO     | services.scheduler_service:_register_event_handlers:435 - Event handlers would be registered in production RabbitMQ
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:44:53.619 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:44:53.619 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:44:53.652 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:44:53.652 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:44:53.653 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:44:53.653 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:44:53.734 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:44:53.735 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:44:53.735 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:44:53 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:44:53.735 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:44:53 | INFO     | services.scheduler_service:__init__:368 - SchedulerService initialized successfully
2025-06-30 23:44:53.768 | INFO     | services.scheduler_service:__init__:368 - SchedulerService initialized successfully
2025-06-30 23:44:53 | INFO     | __main__:run_complete_example:47 - 🏥 CareAXL Hybrid Architecture - End-to-End Example
2025-06-30 23:44:53.769 | INFO     | __main__:run_complete_example:47 - 🏥 CareAXL Hybrid Architecture - End-to-End Example
2025-06-30 23:44:53 | INFO     | __main__:run_complete_example:48 - ================================================================================
2025-06-30 23:44:53.769 | INFO     | __main__:run_complete_example:48 - ================================================================================
2025-06-30 23:44:53 | INFO     | __main__:_phase_1_system_setup:76 - 🔧 Phase 1: System Setup and Validation
2025-06-30 23:44:53.770 | INFO     | __main__:_phase_1_system_setup:76 - 🔧 Phase 1: System Setup and Validation
2025-06-30 23:44:53 | INFO     | __main__:_phase_1_system_setup:77 - --------------------------------------------------
2025-06-30 23:44:53.770 | INFO     | __main__:_phase_1_system_setup:77 - --------------------------------------------------
2025-06-30 23:44:53 | INFO     | __main__:_phase_1_system_setup:80 - 1. Validating service clients...
2025-06-30 23:44:53.771 | INFO     | __main__:_phase_1_system_setup:80 - 1. Validating service clients...
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:44:53.771 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53.771 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53 | INFO     | __main__:_phase_1_system_setup:88 -    ✅ Staff Service: 50 providers available
2025-06-30 23:44:53.772 | INFO     | __main__:_phase_1_system_setup:88 -    ✅ Staff Service: 50 providers available
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:44:53.772 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53.772 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53 | INFO     | __main__:_phase_1_system_setup:98 -    ✅ Patient Service: 0 patients available
2025-06-30 23:44:53.773 | INFO     | __main__:_phase_1_system_setup:98 -    ✅ Patient Service: 0 patients available
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:44:53.773 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53.773 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53 | INFO     | __main__:_phase_1_system_setup:108 -    ✅ Appointment Service: 100 appointments available
2025-06-30 23:44:53.773 | INFO     | __main__:_phase_1_system_setup:108 -    ✅ Appointment Service: 100 appointments available
2025-06-30 23:44:53 | INFO     | __main__:_phase_1_system_setup:113 - 2. Validating event bus...
2025-06-30 23:44:53.774 | INFO     | __main__:_phase_1_system_setup:113 - 2. Validating event bus...
2025-06-30 23:44:53 | INFO     | __main__:_phase_1_system_setup:114 -    ✅ Event bus initialized
2025-06-30 23:44:53.774 | INFO     | __main__:_phase_1_system_setup:114 -    ✅ Event bus initialized
2025-06-30 23:44:53 | INFO     | __main__:_phase_1_system_setup:115 -    ✅ Event handlers registered
2025-06-30 23:44:53.774 | INFO     | __main__:_phase_1_system_setup:115 -    ✅ Event handlers registered
2025-06-30 23:44:53 | INFO     | __main__:_phase_1_system_setup:116 -    ✅ Event processing ready
2025-06-30 23:44:53.774 | INFO     | __main__:_phase_1_system_setup:116 -    ✅ Event processing ready
2025-06-30 23:44:53 | INFO     | __main__:_phase_1_system_setup:118 - 
2025-06-30 23:44:53.775 | INFO     | __main__:_phase_1_system_setup:118 - 
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:122 - ⚡ Phase 2: Synchronous Operations
2025-06-30 23:44:53.775 | INFO     | __main__:_phase_2_synchronous_operations:122 - ⚡ Phase 2: Synchronous Operations
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:123 - --------------------------------------------------
2025-06-30 23:44:53.775 | INFO     | __main__:_phase_2_synchronous_operations:123 - --------------------------------------------------
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:167 - 1. Get Skilled Nursing Providers...
2025-06-30 23:44:53.775 | INFO     | __main__:_phase_2_synchronous_operations:167 - 1. Get Skilled Nursing Providers...
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:44:53.776 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53.776 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:175 -    ✅ Success: 50 items retrieved
2025-06-30 23:44:53.776 | INFO     | __main__:_phase_2_synchronous_operations:175 -    ✅ Success: 50 items retrieved
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:176 -    ⏱️  Response time: 0.000s
2025-06-30 23:44:53.777 | INFO     | __main__:_phase_2_synchronous_operations:176 -    ⏱️  Response time: 0.000s
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:167 - 2. Get Behavioral Care Providers...
2025-06-30 23:44:53.777 | INFO     | __main__:_phase_2_synchronous_operations:167 - 2. Get Behavioral Care Providers...
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:44:53.778 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53.778 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:175 -    ✅ Success: 50 items retrieved
2025-06-30 23:44:53.778 | INFO     | __main__:_phase_2_synchronous_operations:175 -    ✅ Success: 50 items retrieved
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:176 -    ⏱️  Response time: 0.000s
2025-06-30 23:44:53.779 | INFO     | __main__:_phase_2_synchronous_operations:176 -    ⏱️  Response time: 0.000s
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:167 - 3. Get Patients for Care Episode...
2025-06-30 23:44:53.779 | INFO     | __main__:_phase_2_synchronous_operations:167 - 3. Get Patients for Care Episode...
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:44:53.780 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53.780 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:175 -    ✅ Success: 0 items retrieved
2025-06-30 23:44:53.780 | INFO     | __main__:_phase_2_synchronous_operations:175 -    ✅ Success: 0 items retrieved
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:176 -    ⏱️  Response time: 0.000s
2025-06-30 23:44:53.780 | INFO     | __main__:_phase_2_synchronous_operations:176 -    ⏱️  Response time: 0.000s
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:167 - 4. Get Pending Appointments...
2025-06-30 23:44:53.780 | INFO     | __main__:_phase_2_synchronous_operations:167 - 4. Get Pending Appointments...
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:44:53.781 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53.781 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:175 -    ✅ Success: 100 items retrieved
2025-06-30 23:44:53.781 | INFO     | __main__:_phase_2_synchronous_operations:175 -    ✅ Success: 100 items retrieved
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:176 -    ⏱️  Response time: 0.000s
2025-06-30 23:44:53.781 | INFO     | __main__:_phase_2_synchronous_operations:176 -    ⏱️  Response time: 0.000s
2025-06-30 23:44:53 | INFO     | __main__:_phase_2_synchronous_operations:167 - 5. Get Today's Schedule...
2025-06-30 23:44:53.782 | INFO     | __main__:_phase_2_synchronous_operations:167 - 5. Get Today's Schedule...
2025-06-30 23:44:53 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_schedule
2025-06-30 23:44:53.782 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_schedule
2025-06-30 23:44:53 | INFO     | day_plan:run:62 - DAY PLAN JOB STARTED
2025-06-30 23:44:53.782 | INFO     | day_plan:run:62 - DAY PLAN JOB STARTED
2025-06-30 23:44:53 | INFO     | day_plan:run:63 - Target Date: 2025-06-30
2025-06-30 23:44:53.782 | INFO     | day_plan:run:63 - Target Date: 2025-06-30
2025-06-30 23:44:53 | INFO     | day_plan:run:64 - Batch ID: dayplan_20250630_234453
2025-06-30 23:44:53.782 | INFO     | day_plan:run:64 - Batch ID: dayplan_20250630_234453
2025-06-30 23:44:53 | INFO     | day_plan:run:65 - Log file: logs\day_plan_2025-06-30.log
2025-06-30 23:44:53.783 | INFO     | day_plan:run:65 - Log file: logs\day_plan_2025-06-30.log
2025-06-30 23:44:53 | INFO     | day_plan:run:69 - === STAGE 1: Loading Scheduled Appointments ===
2025-06-30 23:44:53.783 | INFO     | day_plan:run:69 - === STAGE 1: Loading Scheduled Appointments ===
2025-06-30 23:44:53 | INFO     | src.data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:44:53.783 | INFO     | src.data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:44:53 | INFO     | src.data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:44:53.784 | INFO     | src.data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:44:53 | INFO     | src.data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:44:53.784 | INFO     | src.data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:44:54 | INFO     | src.data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:44:54.034 | INFO     | src.data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Daniel Brown (LPN) - Skills: wound_care, assessment
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Ryan Rodriguez (LPN) - Skills: personal_care, mobility_assistance
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Nicole Lopez (PT) - Skills: assessment, meal_assistance, personal_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Michael Martin (OT) - Skills: assessment, meal_assistance, wound_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Garcia (LPN) - Skills: housekeeping, mobility_assistance
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Jessica Davis (RN) - Skills: housekeeping, wound_care, meal_assistance
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Kevin Rodriguez (OT) - Skills: iv_therapy, mobility_assistance, wound_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Kevin Thompson (LPN) - Skills: wound_care, medication_management, meal_assistance
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: vital_signs, meal_assistance, mobility_assistance
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Stephanie Jones (RN) - Skills: medication_management, meal_assistance, housekeeping
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Christopher Johnson (OT) - Skills: wound_care, assessment, vital_signs
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. James Martinez (OT) - Skills: wound_care, meal_assistance
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. James Hernandez (OT) - Skills: assessment, vital_signs, wound_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Nicole Thompson (CNA) - Skills: meal_assistance, assessment
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Stephanie Hernandez (CNA) - Skills: meal_assistance, vital_signs, assessment
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Miller (RN) - Skills: iv_therapy, vital_signs, medication_management
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Stephanie Anderson (CNA) - Skills: iv_therapy, wound_care, mobility_assistance
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: meal_assistance, assessment, mobility_assistance
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Moore (LPN) - Skills: housekeeping, vital_signs, wound_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: medication_management, assessment, wound_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lauren Miller (OT) - Skills: medication_management, vital_signs, mobility_assistance
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Amanda Lee (PT) - Skills: iv_therapy, mobility_assistance, medication_management
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lisa Martin (OT) - Skills: mobility_assistance, housekeeping, assessment
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Andrew Martin (CNA) - Skills: mobility_assistance, meal_assistance, personal_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lisa Rodriguez (RN) - Skills: iv_therapy, wound_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. David Moore (LPN) - Skills: vital_signs, housekeeping, personal_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Christopher Lee (PT) - Skills: assessment, iv_therapy
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. William Johnson (LPN) - Skills: housekeeping, meal_assistance, wound_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Emily Thompson (LPN) - Skills: medication_management, mobility_assistance, housekeeping
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Andrew Garcia (RN) - Skills: mobility_assistance, wound_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Michael Wilson (RN) - Skills: medication_management, housekeeping, wound_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Rachel Rodriguez (OT) - Skills: vital_signs, wound_care, mobility_assistance
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Lopez (OT) - Skills: vital_signs, mobility_assistance, assessment
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lauren Brown (RN) - Skills: medication_management, meal_assistance, wound_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: medication_management, assessment, personal_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Lee (OT) - Skills: meal_assistance, assessment, housekeeping
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Rachel Jackson (PT) - Skills: meal_assistance, assessment, iv_therapy
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Daniel Davis (RN) - Skills: meal_assistance, mobility_assistance, personal_care
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Nicole Martin (RN) - Skills: personal_care, mobility_assistance, iv_therapy
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Jessica Perez (LPN) - Skills: medication_management, personal_care, iv_therapy
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Ashley Wilson (PT) - Skills: iv_therapy, personal_care, meal_assistance
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Kevin Jones (PT) - Skills: vital_signs, housekeeping, medication_management
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Ryan Martin (RN) - Skills: assessment, meal_assistance, housekeeping
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lauren Martinez (CNA) - Skills: medication_management, iv_therapy
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Emily Johnson (OT) - Skills: medication_management, assessment
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Moore (RN) - Skills: mobility_assistance, medication_management, assessment
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Michelle Garcia (LPN) - Skills: medication_management, meal_assistance, assessment
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Michelle Anderson (CNA) - Skills: mobility_assistance, medication_management, vital_signs
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Jennifer Moore (LPN) - Skills: housekeeping, medication_management, mobility_assistance
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. James Moore (PT) - Skills: personal_care, assessment, iv_therapy
2025-06-30 23:44:54 | INFO     | src.data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:44:54.035 | INFO     | src.data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:44:54 | INFO     | src.data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:44:54.036 | INFO     | src.data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:44:54 | INFO     | src.data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:44:54.195 | INFO     | src.data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-03
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 120min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-03
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-03
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 60min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular OT - 75min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 75min - 2025-07-03
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 75min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-07-22
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular OT - 60min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-15
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 75min - 2025-07-03
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 90min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 45min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular OT - 45min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 90min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 30min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-22
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 75min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-03
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 75min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-07-03
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-22
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-22
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 75min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 75min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-15
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-22
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-22
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 30min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 120min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 45min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-03
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular OT - 90min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 30min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-03
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-15
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-06-30
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 30min - 2025-07-22
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-03
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-07-22
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 60min - 2025-07-15
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-08
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-22
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-03
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-07-02
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-01
2025-06-30 23:44:54 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 90min - 2025-07-02
2025-06-30 23:44:54 | INFO     | day_plan:run:71 - Loaded 6 scheduled appointments for 2025-06-30
2025-06-30 23:44:54.198 | INFO     | day_plan:run:71 - Loaded 6 scheduled appointments for 2025-06-30
2025-06-30 23:44:54 | INFO     | day_plan:run:73 - === STAGE 2: Loading Available Time Slots ===
2025-06-30 23:44:54.199 | INFO     | day_plan:run:73 - === STAGE 2: Loading Available Time Slots ===
2025-06-30 23:44:54 | INFO     | day_plan:run:75 - Loaded 20 time slots for 2025-06-30
2025-06-30 23:44:54.199 | INFO     | day_plan:run:75 - Loaded 20 time slots for 2025-06-30
2025-06-30 23:44:54 | INFO     | day_plan:run:87 - === STAGE 3: Creating Time Slot Assignments ===
2025-06-30 23:44:54.199 | INFO     | day_plan:run:87 - === STAGE 3: Creating Time Slot Assignments ===
2025-06-30 23:44:54 | INFO     | day_plan:run:89 - Created 6 time slot assignments
2025-06-30 23:44:54.199 | INFO     | day_plan:run:89 - Created 6 time slot assignments
2025-06-30 23:44:54 | INFO     | day_plan:run:101 - === STAGE 4: Solving Time Assignment Problem ===
2025-06-30 23:44:54.200 | INFO     | day_plan:run:101 - === STAGE 4: Solving Time Assignment Problem ===
2025-06-30 23:44:54 | INFO     | day_plan:_solve_time_assignment_problem:178 - Solving time assignment problem with 6 appointments
2025-06-30 23:44:54.200 | INFO     | day_plan:_solve_time_assignment_problem:178 - Solving time assignment problem with 6 appointments
2025-06-30 23:44:54 | INFO     | day_plan:_solve_time_assignment_problem:179 - Route optimization constraints enabled
2025-06-30 23:44:54.200 | INFO     | day_plan:_solve_time_assignment_problem:179 - Route optimization constraints enabled
2025-06-30 23:44:54 | INFO     | day_plan:_solve_time_assignment_problem:194 - Created solver config with 60s timeout
2025-06-30 23:44:54.200 | INFO     | day_plan:_solve_time_assignment_problem:194 - Created solver config with 60s timeout
2025-06-30 23:44:54 | INFO     | day_plan:_solve_time_assignment_problem:197 - Building solver...
2025-06-30 23:44:54.200 | INFO     | day_plan:_solve_time_assignment_problem:197 - Building solver...
2025-06-30 23:44:54 | INFO     | day_plan:_solve_time_assignment_problem:200 - Solver built successfully
2025-06-30 23:44:54.357 | INFO     | day_plan:_solve_time_assignment_problem:200 - Solver built successfully
2025-06-30 23:44:54 | INFO     | day_plan:_solve_time_assignment_problem:203 - Starting solver...
2025-06-30 23:44:54.357 | INFO     | day_plan:_solve_time_assignment_problem:203 - Starting solver...
2025-06-30 23:45:54 | INFO     | day_plan:_solve_time_assignment_problem:208 - Solving completed in 60.25 seconds
2025-06-30 23:45:54.610 | INFO     | day_plan:_solve_time_assignment_problem:208 - Solving completed in 60.25 seconds
2025-06-30 23:45:54 | INFO     | day_plan:_solve_time_assignment_problem:210 - Solution score: -3hard/-4soft
2025-06-30 23:45:54.610 | INFO     | day_plan:_solve_time_assignment_problem:210 - Solution score: -3hard/-4soft
2025-06-30 23:45:54 | INFO     | day_plan:run:105 - === STAGE 5: Processing Results ===
2025-06-30 23:45:54.611 | INFO     | day_plan:run:105 - === STAGE 5: Processing Results ===
2025-06-30 23:45:54 | INFO     | src.data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:45:54.612 | INFO     | src.data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:45:54 | INFO     | src.data.data_loader:load_all_data:153 - Starting data loading process...
2025-06-30 23:45:54.612 | INFO     | src.data.data_loader:load_all_data:153 - Starting data loading process...
2025-06-30 23:45:54 | INFO     | src.data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:45:54.613 | INFO     | src.data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:45:54 | INFO     | src.data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:45:54.613 | INFO     | src.data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:45:54 | INFO     | src.data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:45:54.770 | INFO     | src.data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Daniel Brown (LPN) - Skills: wound_care, assessment
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Ryan Rodriguez (LPN) - Skills: personal_care, mobility_assistance
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Nicole Lopez (PT) - Skills: assessment, meal_assistance, personal_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Michael Martin (OT) - Skills: assessment, meal_assistance, wound_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Garcia (LPN) - Skills: housekeeping, mobility_assistance
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Jessica Davis (RN) - Skills: housekeeping, wound_care, meal_assistance
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Kevin Rodriguez (OT) - Skills: iv_therapy, mobility_assistance, wound_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Kevin Thompson (LPN) - Skills: wound_care, medication_management, meal_assistance
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: vital_signs, meal_assistance, mobility_assistance
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Stephanie Jones (RN) - Skills: medication_management, meal_assistance, housekeeping
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Christopher Johnson (OT) - Skills: wound_care, assessment, vital_signs
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. James Martinez (OT) - Skills: wound_care, meal_assistance
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. James Hernandez (OT) - Skills: assessment, vital_signs, wound_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Nicole Thompson (CNA) - Skills: meal_assistance, assessment
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Stephanie Hernandez (CNA) - Skills: meal_assistance, vital_signs, assessment
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Miller (RN) - Skills: iv_therapy, vital_signs, medication_management
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Stephanie Anderson (CNA) - Skills: iv_therapy, wound_care, mobility_assistance
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: meal_assistance, assessment, mobility_assistance
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Moore (LPN) - Skills: housekeeping, vital_signs, wound_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: medication_management, assessment, wound_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lauren Miller (OT) - Skills: medication_management, vital_signs, mobility_assistance
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Amanda Lee (PT) - Skills: iv_therapy, mobility_assistance, medication_management
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lisa Martin (OT) - Skills: mobility_assistance, housekeeping, assessment
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Andrew Martin (CNA) - Skills: mobility_assistance, meal_assistance, personal_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lisa Rodriguez (RN) - Skills: iv_therapy, wound_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. David Moore (LPN) - Skills: vital_signs, housekeeping, personal_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Christopher Lee (PT) - Skills: assessment, iv_therapy
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. William Johnson (LPN) - Skills: housekeeping, meal_assistance, wound_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Emily Thompson (LPN) - Skills: medication_management, mobility_assistance, housekeeping
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Andrew Garcia (RN) - Skills: mobility_assistance, wound_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Michael Wilson (RN) - Skills: medication_management, housekeeping, wound_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Rachel Rodriguez (OT) - Skills: vital_signs, wound_care, mobility_assistance
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Lopez (OT) - Skills: vital_signs, mobility_assistance, assessment
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lauren Brown (RN) - Skills: medication_management, meal_assistance, wound_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: medication_management, assessment, personal_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Lee (OT) - Skills: meal_assistance, assessment, housekeeping
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Rachel Jackson (PT) - Skills: meal_assistance, assessment, iv_therapy
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Daniel Davis (RN) - Skills: meal_assistance, mobility_assistance, personal_care
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Nicole Martin (RN) - Skills: personal_care, mobility_assistance, iv_therapy
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Jessica Perez (LPN) - Skills: medication_management, personal_care, iv_therapy
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Ashley Wilson (PT) - Skills: iv_therapy, personal_care, meal_assistance
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Kevin Jones (PT) - Skills: vital_signs, housekeeping, medication_management
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Ryan Martin (RN) - Skills: assessment, meal_assistance, housekeeping
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lauren Martinez (CNA) - Skills: medication_management, iv_therapy
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Emily Johnson (OT) - Skills: medication_management, assessment
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Moore (RN) - Skills: mobility_assistance, medication_management, assessment
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Michelle Garcia (LPN) - Skills: medication_management, meal_assistance, assessment
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Michelle Anderson (CNA) - Skills: mobility_assistance, medication_management, vital_signs
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Jennifer Moore (LPN) - Skills: housekeeping, medication_management, mobility_assistance
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. James Moore (PT) - Skills: personal_care, assessment, iv_therapy
2025-06-30 23:45:54 | INFO     | src.data.data_loader:load_consumers:87 - === STAGE 2: Loading Consumer Data ===
2025-06-30 23:45:54.772 | INFO     | src.data.data_loader:load_consumers:87 - === STAGE 2: Loading Consumer Data ===
2025-06-30 23:45:54 | INFO     | src.data.data_loader:load_consumers:101 - Reading consumer data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\consumers.yml
2025-06-30 23:45:54.772 | INFO     | src.data.data_loader:load_consumers:101 - Reading consumer data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\consumers.yml
2025-06-30 23:45:54 | INFO     | src.data.data_loader:load_consumers:111 - ✅ Consumer data loaded: 100 consumers
2025-06-30 23:45:54.873 | INFO     | src.data.data_loader:load_consumers:111 - ✅ Consumer data loaded: 100 consumers
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Donna Collins - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth Garrett - Episode: episode-2440
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Michelle Mendoza - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Matthew Rose - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy Burke - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Edward Williams - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Linda Palmer - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Cole - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - George Moreno - Episode: episode-2280
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Paul Olson - Episode: episode-6025
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Laura Cox - Episode: episode-8358
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Aguilar - Episode: episode-4641
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Kimberly Gardner - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Michelle Guerrero - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Donna Nguyen - Episode: episode-5132
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Carl Rios - Episode: episode-5178
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Helen Townsend - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Daniel Wheeler - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Jonathan Kennedy - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Betty Harrison - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Patricia Gordon - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lawrence Gonzalez - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Douglas Mcdonald - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sandra Davis - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Evans - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dennis Romero - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth McKenna - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Terry - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Terry Alexander - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Joshua Black - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy Powell - Episode: episode-6559
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Donna Wilson - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Patricia Hoffman - Episode: episode-7856
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Donald Aguilar - Episode: episode-7031
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy Jacobs - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - George Henry - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Gregory Perry - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Jacob McDonald - Episode: episode-5471
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Peter Hicks - Episode: episode-3136
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Karen Alexander - Episode: episode-8466
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Michelle Rose - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sandra Russell - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sarah Carroll - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Barbara Reyes - Episode: episode-6626
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Weaver - Episode: episode-7890
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Laura McKenna - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Vazquez - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy McDaniel - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Justin Cleveland - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Carl Fernandez - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Laura Franklin - Episode: episode-2981
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth Mason - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Karen Soto - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Christian Kim - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Betty Johnston - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Paul Herrera - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Kyle White - Episode: episode-2605
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Jerry Spencer - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Donna Perry - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Charles Griffin - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - William Diaz - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Tyler Gonzalez - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Emily Cooper - Episode: episode-1979
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Jacob Murray - Episode: episode-5269
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy Ray - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Nancy Wheeler - Episode: episode-3169
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Deborah Stephens - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Anthony Daniels - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Gregory Wilson - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Donald Herrera - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth Flores - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Deborah Warren - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Brandon Morgan - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth Rogers - Episode: episode-3059
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sandra Wheeler - Episode: episode-2116
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Douglas Taylor - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Mark Pierce - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Emily Jensen - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Paul Webb - Episode: episode-9774
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth Thompson - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Terry Dorsey - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Michael Hart - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sarah Morgan - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sean Thomas - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Michelle Gutierrez - Episode: episode-4864
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy Johnson - Episode: episode-9681
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Nancy Andrews - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Arthur George - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sandra Riley - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Linda Chavez - Episode: episode-4538
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Michelle Lane - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Kimberly Alvarez - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy Hughes - Episode: episode-2864
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Richards - Episode: episode-4001
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth Shaw - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Joshua Gray - Episode: episode-2319
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sharon Mendoza - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Matthew Ruiz - Episode: episode-4651
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Betty Arnold - Episode: None
2025-06-30 23:45:54 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Gregory Soto - Episode: None
2025-06-30 23:45:54 | INFO     | src.data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:45:54.876 | INFO     | src.data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:45:54 | INFO     | src.data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:45:54.876 | INFO     | src.data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:45:55.071 | INFO     | src.data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 120min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 60min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular OT - 75min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 75min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 75min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular OT - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-15
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 75min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 90min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 45min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular OT - 45min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 90min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 30min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 75min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 75min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 75min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 75min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-15
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 30min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 120min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 45min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular OT - 90min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 30min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-15
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 30min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 60min - 2025-07-15
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 90min - 2025-07-02
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_all_data:159 - === DATA LOADING SUMMARY ===
2025-06-30 23:45:55.074 | INFO     | src.data.data_loader:load_all_data:159 - === DATA LOADING SUMMARY ===
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_all_data:160 - Total records loaded:
2025-06-30 23:45:55.074 | INFO     | src.data.data_loader:load_all_data:160 - Total records loaded:
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_all_data:161 -    - Providers: 50
2025-06-30 23:45:55.075 | INFO     | src.data.data_loader:load_all_data:161 -    - Providers: 50
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_all_data:162 -    - Consumers: 100
2025-06-30 23:45:55.075 | INFO     | src.data.data_loader:load_all_data:162 -    - Consumers: 100
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_all_data:163 -    - Appointments: 100
2025-06-30 23:45:55.075 | INFO     | src.data.data_loader:load_all_data:163 -    - Appointments: 100
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_all_data:164 - All data loaded successfully!
2025-06-30 23:45:55.076 | INFO     | src.data.data_loader:load_all_data:164 - All data loaded successfully!
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:338 - 
=== Detailed Visit Order ===
2025-06-30 23:45:55.078 | INFO     | day_plan:_display_visit_order:338 - 
=== Detailed Visit Order ===
2025-06-30 23:45:55 | INFO     | src.data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:45:55.079 | INFO     | src.data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_all_data:153 - Starting data loading process...
2025-06-30 23:45:55.079 | INFO     | src.data.data_loader:load_all_data:153 - Starting data loading process...
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:45:55.079 | INFO     | src.data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:45:55.080 | INFO     | src.data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:45:55.239 | INFO     | src.data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Daniel Brown (LPN) - Skills: wound_care, assessment
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Ryan Rodriguez (LPN) - Skills: personal_care, mobility_assistance
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Nicole Lopez (PT) - Skills: assessment, meal_assistance, personal_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Michael Martin (OT) - Skills: assessment, meal_assistance, wound_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Garcia (LPN) - Skills: housekeeping, mobility_assistance
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Jessica Davis (RN) - Skills: housekeeping, wound_care, meal_assistance
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Kevin Rodriguez (OT) - Skills: iv_therapy, mobility_assistance, wound_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Kevin Thompson (LPN) - Skills: wound_care, medication_management, meal_assistance
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: vital_signs, meal_assistance, mobility_assistance
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Stephanie Jones (RN) - Skills: medication_management, meal_assistance, housekeeping
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Christopher Johnson (OT) - Skills: wound_care, assessment, vital_signs
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. James Martinez (OT) - Skills: wound_care, meal_assistance
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. James Hernandez (OT) - Skills: assessment, vital_signs, wound_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Nicole Thompson (CNA) - Skills: meal_assistance, assessment
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Stephanie Hernandez (CNA) - Skills: meal_assistance, vital_signs, assessment
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Miller (RN) - Skills: iv_therapy, vital_signs, medication_management
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Stephanie Anderson (CNA) - Skills: iv_therapy, wound_care, mobility_assistance
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: meal_assistance, assessment, mobility_assistance
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Moore (LPN) - Skills: housekeeping, vital_signs, wound_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: medication_management, assessment, wound_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lauren Miller (OT) - Skills: medication_management, vital_signs, mobility_assistance
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Amanda Lee (PT) - Skills: iv_therapy, mobility_assistance, medication_management
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lisa Martin (OT) - Skills: mobility_assistance, housekeeping, assessment
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Andrew Martin (CNA) - Skills: mobility_assistance, meal_assistance, personal_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lisa Rodriguez (RN) - Skills: iv_therapy, wound_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. David Moore (LPN) - Skills: vital_signs, housekeeping, personal_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Christopher Lee (PT) - Skills: assessment, iv_therapy
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. William Johnson (LPN) - Skills: housekeeping, meal_assistance, wound_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Emily Thompson (LPN) - Skills: medication_management, mobility_assistance, housekeeping
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Andrew Garcia (RN) - Skills: mobility_assistance, wound_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Michael Wilson (RN) - Skills: medication_management, housekeeping, wound_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Rachel Rodriguez (OT) - Skills: vital_signs, wound_care, mobility_assistance
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Lopez (OT) - Skills: vital_signs, mobility_assistance, assessment
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lauren Brown (RN) - Skills: medication_management, meal_assistance, wound_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: medication_management, assessment, personal_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Lee (OT) - Skills: meal_assistance, assessment, housekeeping
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Rachel Jackson (PT) - Skills: meal_assistance, assessment, iv_therapy
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Daniel Davis (RN) - Skills: meal_assistance, mobility_assistance, personal_care
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Nicole Martin (RN) - Skills: personal_care, mobility_assistance, iv_therapy
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Jessica Perez (LPN) - Skills: medication_management, personal_care, iv_therapy
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Ashley Wilson (PT) - Skills: iv_therapy, personal_care, meal_assistance
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Kevin Jones (PT) - Skills: vital_signs, housekeeping, medication_management
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Ryan Martin (RN) - Skills: assessment, meal_assistance, housekeeping
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Lauren Martinez (CNA) - Skills: medication_management, iv_therapy
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Emily Johnson (OT) - Skills: medication_management, assessment
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Sarah Moore (RN) - Skills: mobility_assistance, medication_management, assessment
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Michelle Garcia (LPN) - Skills: medication_management, meal_assistance, assessment
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Michelle Anderson (CNA) - Skills: mobility_assistance, medication_management, vital_signs
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. Jennifer Moore (LPN) - Skills: housekeeping, medication_management, mobility_assistance
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_providers:81 -    - Dr. James Moore (PT) - Skills: personal_care, assessment, iv_therapy
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_consumers:87 - === STAGE 2: Loading Consumer Data ===
2025-06-30 23:45:55.241 | INFO     | src.data.data_loader:load_consumers:87 - === STAGE 2: Loading Consumer Data ===
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_consumers:101 - Reading consumer data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\consumers.yml
2025-06-30 23:45:55.242 | INFO     | src.data.data_loader:load_consumers:101 - Reading consumer data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\consumers.yml
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_consumers:111 - ✅ Consumer data loaded: 100 consumers
2025-06-30 23:45:55.340 | INFO     | src.data.data_loader:load_consumers:111 - ✅ Consumer data loaded: 100 consumers
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Donna Collins - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth Garrett - Episode: episode-2440
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Michelle Mendoza - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Matthew Rose - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy Burke - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Edward Williams - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Linda Palmer - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Cole - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - George Moreno - Episode: episode-2280
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Paul Olson - Episode: episode-6025
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Laura Cox - Episode: episode-8358
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Aguilar - Episode: episode-4641
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Kimberly Gardner - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Michelle Guerrero - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Donna Nguyen - Episode: episode-5132
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Carl Rios - Episode: episode-5178
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Helen Townsend - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Daniel Wheeler - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Jonathan Kennedy - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Betty Harrison - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Patricia Gordon - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lawrence Gonzalez - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Douglas Mcdonald - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sandra Davis - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Evans - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dennis Romero - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth McKenna - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Terry - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Terry Alexander - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Joshua Black - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy Powell - Episode: episode-6559
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Donna Wilson - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Patricia Hoffman - Episode: episode-7856
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Donald Aguilar - Episode: episode-7031
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy Jacobs - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - George Henry - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Gregory Perry - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Jacob McDonald - Episode: episode-5471
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Peter Hicks - Episode: episode-3136
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Karen Alexander - Episode: episode-8466
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Michelle Rose - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sandra Russell - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sarah Carroll - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Barbara Reyes - Episode: episode-6626
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Weaver - Episode: episode-7890
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Laura McKenna - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Vazquez - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy McDaniel - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Justin Cleveland - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Carl Fernandez - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Laura Franklin - Episode: episode-2981
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth Mason - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Karen Soto - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Christian Kim - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Betty Johnston - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Paul Herrera - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Kyle White - Episode: episode-2605
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Jerry Spencer - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Donna Perry - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Charles Griffin - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - William Diaz - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Tyler Gonzalez - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Emily Cooper - Episode: episode-1979
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Jacob Murray - Episode: episode-5269
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy Ray - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Nancy Wheeler - Episode: episode-3169
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Deborah Stephens - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Anthony Daniels - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Gregory Wilson - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Donald Herrera - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth Flores - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Deborah Warren - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Brandon Morgan - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth Rogers - Episode: episode-3059
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sandra Wheeler - Episode: episode-2116
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Douglas Taylor - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Mark Pierce - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Emily Jensen - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Paul Webb - Episode: episode-9774
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth Thompson - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Terry Dorsey - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Michael Hart - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sarah Morgan - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sean Thomas - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Michelle Gutierrez - Episode: episode-4864
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy Johnson - Episode: episode-9681
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Nancy Andrews - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Arthur George - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sandra Riley - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Linda Chavez - Episode: episode-4538
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Michelle Lane - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Kimberly Alvarez - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Dorothy Hughes - Episode: episode-2864
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Lisa Richards - Episode: episode-4001
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Ruth Shaw - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Joshua Gray - Episode: episode-2319
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Sharon Mendoza - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Matthew Ruiz - Episode: episode-4651
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Betty Arnold - Episode: None
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_consumers:113 -    - Gregory Soto - Episode: None
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:45:55.343 | INFO     | src.data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:45:55.344 | INFO     | src.data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:45:55.499 | INFO     | src.data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 120min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 60min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular OT - 75min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 75min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 75min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular OT - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-15
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 75min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 90min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 45min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular OT - 45min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 90min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 30min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 75min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 75min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 75min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 75min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-15
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular CNA - 30min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 120min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 45min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular OT - 90min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 30min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-15
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-06-30
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular RN - 30min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 60min - 2025-07-15
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-08
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-03
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-07-02
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-01
2025-06-30 23:45:55 | DEBUG    | src.data.data_loader:load_appointments:146 -    - URGENT CNA - 90min - 2025-07-02
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_all_data:159 - === DATA LOADING SUMMARY ===
2025-06-30 23:45:55.502 | INFO     | src.data.data_loader:load_all_data:159 - === DATA LOADING SUMMARY ===
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_all_data:160 - Total records loaded:
2025-06-30 23:45:55.502 | INFO     | src.data.data_loader:load_all_data:160 - Total records loaded:
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_all_data:161 -    - Providers: 50
2025-06-30 23:45:55.502 | INFO     | src.data.data_loader:load_all_data:161 -    - Providers: 50
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_all_data:162 -    - Consumers: 100
2025-06-30 23:45:55.502 | INFO     | src.data.data_loader:load_all_data:162 -    - Consumers: 100
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_all_data:163 -    - Appointments: 100
2025-06-30 23:45:55.502 | INFO     | src.data.data_loader:load_all_data:163 -    - Appointments: 100
2025-06-30 23:45:55 | INFO     | src.data.data_loader:load_all_data:164 - All data loaded successfully!
2025-06-30 23:45:55.502 | INFO     | src.data.data_loader:load_all_data:164 - All data loaded successfully!
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:412 - 
Provider: Dr. Daniel Brown (ID: 69c70a63-2d3f-4d6d-9e1c-1d36bdcda377)
2025-06-30 23:45:55.503 | INFO     | day_plan:_display_visit_order:412 - 
Provider: Dr. Daniel Brown (ID: 69c70a63-2d3f-4d6d-9e1c-1d36bdcda377)
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:413 - ============================================================
2025-06-30 23:45:55.503 | INFO     | day_plan:_display_visit_order:413 - ============================================================
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:420 -     1. 08:00-09:30 | Patient: Betty Arnold | Date: 2025-06-30
2025-06-30 23:45:55.503 | INFO     | day_plan:_display_visit_order:420 -     1. 08:00-09:30 | Patient: Betty Arnold | Date: 2025-06-30
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:421 -        Location: New York
2025-06-30 23:45:55.503 | INFO     | day_plan:_display_visit_order:421 -        Location: New York
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:422 -        Address: 101 Example Ave, New York, NY 10001
2025-06-30 23:45:55.504 | INFO     | day_plan:_display_visit_order:422 -        Address: 101 Example Ave, New York, NY 10001
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:423 - 
2025-06-30 23:45:55.504 | INFO     | day_plan:_display_visit_order:423 - 
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:420 -     2. 09:30-11:30 | Patient: Dorothy Jacobs | Date: 2025-06-30
2025-06-30 23:45:55.504 | INFO     | day_plan:_display_visit_order:420 -     2. 09:30-11:30 | Patient: Dorothy Jacobs | Date: 2025-06-30
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:421 -        Location: New York
2025-06-30 23:45:55.504 | INFO     | day_plan:_display_visit_order:421 -        Location: New York
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:422 -        Address: 103 Example Ave, New York, NY 10001
2025-06-30 23:45:55.505 | INFO     | day_plan:_display_visit_order:422 -        Address: 103 Example Ave, New York, NY 10001
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:423 - 
2025-06-30 23:45:55.505 | INFO     | day_plan:_display_visit_order:423 - 
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:420 -     3. 11:00-11:30 | Patient: Michelle Rose | Date: 2025-06-30
2025-06-30 23:45:55.505 | INFO     | day_plan:_display_visit_order:420 -     3. 11:00-11:30 | Patient: Michelle Rose | Date: 2025-06-30
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:421 -        Location: New York
2025-06-30 23:45:55.505 | INFO     | day_plan:_display_visit_order:421 -        Location: New York
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:422 -        Address: 105 Example Ave, New York, NY 10001
2025-06-30 23:45:55.505 | INFO     | day_plan:_display_visit_order:422 -        Address: 105 Example Ave, New York, NY 10001
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:423 - 
2025-06-30 23:45:55.506 | INFO     | day_plan:_display_visit_order:423 - 
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:412 - 
Provider: Dr. Sarah Garcia (ID: 227b10b7-0cc9-4a0f-9442-bcd9fb997ce8)
2025-06-30 23:45:55.506 | INFO     | day_plan:_display_visit_order:412 - 
Provider: Dr. Sarah Garcia (ID: 227b10b7-0cc9-4a0f-9442-bcd9fb997ce8)
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:413 - ============================================================
2025-06-30 23:45:55.506 | INFO     | day_plan:_display_visit_order:413 - ============================================================
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:420 -     1. 08:00-08:30 | Patient: Donna Perry | Date: 2025-06-30
2025-06-30 23:45:55.506 | INFO     | day_plan:_display_visit_order:420 -     1. 08:00-08:30 | Patient: Donna Perry | Date: 2025-06-30
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:421 -        Location: New York
2025-06-30 23:45:55.507 | INFO     | day_plan:_display_visit_order:421 -        Location: New York
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:422 -        Address: 102 Example Ave, New York, NY 10001
2025-06-30 23:45:55.507 | INFO     | day_plan:_display_visit_order:422 -        Address: 102 Example Ave, New York, NY 10001
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:423 - 
2025-06-30 23:45:55.507 | INFO     | day_plan:_display_visit_order:423 - 
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:412 - 
Provider: Dr. Ryan Rodriguez (ID: 63ff8914-ccb4-49cb-acfb-bb2c585bd0fe)
2025-06-30 23:45:55.507 | INFO     | day_plan:_display_visit_order:412 - 
Provider: Dr. Ryan Rodriguez (ID: 63ff8914-ccb4-49cb-acfb-bb2c585bd0fe)
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:413 - ============================================================
2025-06-30 23:45:55.507 | INFO     | day_plan:_display_visit_order:413 - ============================================================
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:420 -     1. 08:00-09:30 | Patient: Deborah Warren | Date: 2025-06-30
2025-06-30 23:45:55.507 | INFO     | day_plan:_display_visit_order:420 -     1. 08:00-09:30 | Patient: Deborah Warren | Date: 2025-06-30
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:421 -        Location: New York
2025-06-30 23:45:55.508 | INFO     | day_plan:_display_visit_order:421 -        Location: New York
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:422 -        Address: 104 Example Ave, New York, NY 10001
2025-06-30 23:45:55.508 | INFO     | day_plan:_display_visit_order:422 -        Address: 104 Example Ave, New York, NY 10001
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:423 - 
2025-06-30 23:45:55.508 | INFO     | day_plan:_display_visit_order:423 - 
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:420 -     2. 09:30-10:30 | Patient: Laura Franklin | Date: 2025-06-30
2025-06-30 23:45:55.508 | INFO     | day_plan:_display_visit_order:420 -     2. 09:30-10:30 | Patient: Laura Franklin | Date: 2025-06-30
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:421 -        Location: New York
2025-06-30 23:45:55.510 | INFO     | day_plan:_display_visit_order:421 -        Location: New York
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:422 -        Address: 106 Example Ave, New York, NY 10001
2025-06-30 23:45:55.512 | INFO     | day_plan:_display_visit_order:422 -        Address: 106 Example Ave, New York, NY 10001
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:423 - 
2025-06-30 23:45:55.513 | INFO     | day_plan:_display_visit_order:423 - 
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:427 - 
Visit Order Summary:
2025-06-30 23:45:55.514 | INFO     | day_plan:_display_visit_order:427 - 
Visit Order Summary:
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:428 -    Total Providers: 3
2025-06-30 23:45:55.514 | INFO     | day_plan:_display_visit_order:428 -    Total Providers: 3
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:429 -    Total Visits: 6
2025-06-30 23:45:55.515 | INFO     | day_plan:_display_visit_order:429 -    Total Visits: 6
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:430 -    Average Visits per Provider: 2.0
2025-06-30 23:45:55.515 | INFO     | day_plan:_display_visit_order:430 -    Average Visits per Provider: 2.0
2025-06-30 23:45:55 | INFO     | day_plan:_display_visit_order:432 - ============================================================
2025-06-30 23:45:55.515 | INFO     | day_plan:_display_visit_order:432 - ============================================================
2025-06-30 23:45:55 | INFO     | day_plan:run:118 - Time assignment completed: 6/6 assigned in 61.73s
2025-06-30 23:45:55.516 | INFO     | day_plan:run:118 - Time assignment completed: 6/6 assigned in 61.73s
2025-06-30 23:45:55 | INFO     | day_plan:run:120 - Average Score: 0.00
2025-06-30 23:45:55.516 | INFO     | day_plan:run:120 - Average Score: 0.00
2025-06-30 23:45:55 | INFO     | day_plan:run:121 - DAY PLAN JOB COMPLETED
2025-06-30 23:45:55.516 | INFO     | day_plan:run:121 - DAY PLAN JOB COMPLETED
2025-06-30 23:45:55 | INFO     | day_plan:_handle_completion:437 - Job completed.
2025-06-30 23:45:55.517 | INFO     | day_plan:_handle_completion:437 - Job completed.
2025-06-30 23:45:55 | INFO     | services.scheduler_service:process_request:485 - Request completed in 61.735s: True
2025-06-30 23:45:55.517 | INFO     | services.scheduler_service:process_request:485 - Request completed in 61.735s: True
2025-06-30 23:47:05 | INFO     | assign_appointments:setup_logging:67 - Logging configured - console (INFO+) and file (DEBUG+): logs\scheduler_2025-06-30.log
2025-06-30 23:47:05 | WARNING  | events.rabbitmq_integration:<module>:26 - aio_pika not available. Using mock RabbitMQ implementation.
2025-06-30 23:47:05.826 | WARNING  | events.rabbitmq_integration:<module>:26 - aio_pika not available. Using mock RabbitMQ implementation.
2025-06-30 23:47:05 | INFO     | data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:47:05.829 | INFO     | data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:47:05 | INFO     | data.data_loader:load_all_data:153 - Starting data loading process...
2025-06-30 23:47:05.830 | INFO     | data.data_loader:load_all_data:153 - Starting data loading process...
2025-06-30 23:47:05 | INFO     | data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:47:05.830 | INFO     | data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:47:05 | INFO     | data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:47:05.830 | INFO     | data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:47:06 | INFO     | data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:47:06.012 | INFO     | data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Daniel Brown (LPN) - Skills: wound_care, assessment
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Ryan Rodriguez (LPN) - Skills: personal_care, mobility_assistance
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Nicole Lopez (PT) - Skills: assessment, meal_assistance, personal_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michael Martin (OT) - Skills: assessment, meal_assistance, wound_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Garcia (LPN) - Skills: housekeeping, mobility_assistance
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Jessica Davis (RN) - Skills: housekeeping, wound_care, meal_assistance
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Kevin Rodriguez (OT) - Skills: iv_therapy, mobility_assistance, wound_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Kevin Thompson (LPN) - Skills: wound_care, medication_management, meal_assistance
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: vital_signs, meal_assistance, mobility_assistance
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Stephanie Jones (RN) - Skills: medication_management, meal_assistance, housekeeping
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Christopher Johnson (OT) - Skills: wound_care, assessment, vital_signs
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. James Martinez (OT) - Skills: wound_care, meal_assistance
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. James Hernandez (OT) - Skills: assessment, vital_signs, wound_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Nicole Thompson (CNA) - Skills: meal_assistance, assessment
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Stephanie Hernandez (CNA) - Skills: meal_assistance, vital_signs, assessment
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Miller (RN) - Skills: iv_therapy, vital_signs, medication_management
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Stephanie Anderson (CNA) - Skills: iv_therapy, wound_care, mobility_assistance
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: meal_assistance, assessment, mobility_assistance
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Moore (LPN) - Skills: housekeeping, vital_signs, wound_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: medication_management, assessment, wound_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lauren Miller (OT) - Skills: medication_management, vital_signs, mobility_assistance
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Amanda Lee (PT) - Skills: iv_therapy, mobility_assistance, medication_management
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lisa Martin (OT) - Skills: mobility_assistance, housekeeping, assessment
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Andrew Martin (CNA) - Skills: mobility_assistance, meal_assistance, personal_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lisa Rodriguez (RN) - Skills: iv_therapy, wound_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. David Moore (LPN) - Skills: vital_signs, housekeeping, personal_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Christopher Lee (PT) - Skills: assessment, iv_therapy
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. William Johnson (LPN) - Skills: housekeeping, meal_assistance, wound_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Emily Thompson (LPN) - Skills: medication_management, mobility_assistance, housekeeping
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Andrew Garcia (RN) - Skills: mobility_assistance, wound_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michael Wilson (RN) - Skills: medication_management, housekeeping, wound_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Rachel Rodriguez (OT) - Skills: vital_signs, wound_care, mobility_assistance
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Lopez (OT) - Skills: vital_signs, mobility_assistance, assessment
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lauren Brown (RN) - Skills: medication_management, meal_assistance, wound_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: medication_management, assessment, personal_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Lee (OT) - Skills: meal_assistance, assessment, housekeeping
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Rachel Jackson (PT) - Skills: meal_assistance, assessment, iv_therapy
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Daniel Davis (RN) - Skills: meal_assistance, mobility_assistance, personal_care
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Nicole Martin (RN) - Skills: personal_care, mobility_assistance, iv_therapy
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Jessica Perez (LPN) - Skills: medication_management, personal_care, iv_therapy
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Ashley Wilson (PT) - Skills: iv_therapy, personal_care, meal_assistance
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Kevin Jones (PT) - Skills: vital_signs, housekeeping, medication_management
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Ryan Martin (RN) - Skills: assessment, meal_assistance, housekeeping
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lauren Martinez (CNA) - Skills: medication_management, iv_therapy
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Emily Johnson (OT) - Skills: medication_management, assessment
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Moore (RN) - Skills: mobility_assistance, medication_management, assessment
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michelle Garcia (LPN) - Skills: medication_management, meal_assistance, assessment
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michelle Anderson (CNA) - Skills: mobility_assistance, medication_management, vital_signs
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Jennifer Moore (LPN) - Skills: housekeeping, medication_management, mobility_assistance
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. James Moore (PT) - Skills: personal_care, assessment, iv_therapy
2025-06-30 23:47:06 | INFO     | data.data_loader:load_consumers:87 - === STAGE 2: Loading Consumer Data ===
2025-06-30 23:47:06.014 | INFO     | data.data_loader:load_consumers:87 - === STAGE 2: Loading Consumer Data ===
2025-06-30 23:47:06 | INFO     | data.data_loader:load_consumers:101 - Reading consumer data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\consumers.yml
2025-06-30 23:47:06.014 | INFO     | data.data_loader:load_consumers:101 - Reading consumer data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\consumers.yml
2025-06-30 23:47:06 | INFO     | data.data_loader:load_consumers:111 - ✅ Consumer data loaded: 100 consumers
2025-06-30 23:47:06.164 | INFO     | data.data_loader:load_consumers:111 - ✅ Consumer data loaded: 100 consumers
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Collins - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Garrett - Episode: episode-2440
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Mendoza - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Matthew Rose - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Burke - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Edward Williams - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Linda Palmer - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Cole - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - George Moreno - Episode: episode-2280
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Paul Olson - Episode: episode-6025
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Laura Cox - Episode: episode-8358
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Aguilar - Episode: episode-4641
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Kimberly Gardner - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Guerrero - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Nguyen - Episode: episode-5132
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Carl Rios - Episode: episode-5178
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Helen Townsend - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Daniel Wheeler - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Jonathan Kennedy - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Betty Harrison - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Patricia Gordon - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Lawrence Gonzalez - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Douglas Mcdonald - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Davis - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Evans - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Dennis Romero - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth McKenna - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Terry - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Terry Alexander - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Joshua Black - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Powell - Episode: episode-6559
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Wilson - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Patricia Hoffman - Episode: episode-7856
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Donald Aguilar - Episode: episode-7031
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Jacobs - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - George Henry - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Gregory Perry - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Jacob McDonald - Episode: episode-5471
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Peter Hicks - Episode: episode-3136
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Karen Alexander - Episode: episode-8466
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Rose - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Russell - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Sarah Carroll - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Barbara Reyes - Episode: episode-6626
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Weaver - Episode: episode-7890
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Laura McKenna - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Vazquez - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy McDaniel - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Justin Cleveland - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Carl Fernandez - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Laura Franklin - Episode: episode-2981
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Mason - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Karen Soto - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Christian Kim - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Betty Johnston - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Paul Herrera - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Kyle White - Episode: episode-2605
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Jerry Spencer - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Perry - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Charles Griffin - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - William Diaz - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Tyler Gonzalez - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Emily Cooper - Episode: episode-1979
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Jacob Murray - Episode: episode-5269
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Ray - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Nancy Wheeler - Episode: episode-3169
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Deborah Stephens - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Anthony Daniels - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Gregory Wilson - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Donald Herrera - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Flores - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Deborah Warren - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Brandon Morgan - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Rogers - Episode: episode-3059
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Wheeler - Episode: episode-2116
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Douglas Taylor - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Mark Pierce - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Emily Jensen - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Paul Webb - Episode: episode-9774
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Thompson - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Terry Dorsey - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Michael Hart - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Sarah Morgan - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Sean Thomas - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Gutierrez - Episode: episode-4864
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Johnson - Episode: episode-9681
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Nancy Andrews - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Arthur George - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Riley - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Linda Chavez - Episode: episode-4538
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Lane - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Kimberly Alvarez - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Hughes - Episode: episode-2864
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Richards - Episode: episode-4001
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Shaw - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Joshua Gray - Episode: episode-2319
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Sharon Mendoza - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Matthew Ruiz - Episode: episode-4651
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Betty Arnold - Episode: None
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_consumers:113 -    - Gregory Soto - Episode: None
2025-06-30 23:47:06 | INFO     | data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:47:06.168 | INFO     | data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:47:06 | INFO     | data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:47:06.168 | INFO     | data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:47:06 | INFO     | data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:47:06.359 | INFO     | data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-03
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 120min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-03
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-03
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 60min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 75min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 75min - 2025-07-03
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 75min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-07-22
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 60min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-15
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 75min - 2025-07-03
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 90min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 45min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 45min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 90min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 30min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-22
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 75min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-03
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 75min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-07-03
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-22
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-22
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 75min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 75min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-15
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-22
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-22
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 30min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 120min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 45min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-03
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 90min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 30min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-03
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-15
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-06-30
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 30min - 2025-07-22
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-03
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-07-22
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 60min - 2025-07-15
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-08
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-22
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-03
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-07-02
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-01
2025-06-30 23:47:06 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 90min - 2025-07-02
2025-06-30 23:47:06 | INFO     | data.data_loader:load_all_data:159 - === DATA LOADING SUMMARY ===
2025-06-30 23:47:06.362 | INFO     | data.data_loader:load_all_data:159 - === DATA LOADING SUMMARY ===
2025-06-30 23:47:06 | INFO     | data.data_loader:load_all_data:160 - Total records loaded:
2025-06-30 23:47:06.362 | INFO     | data.data_loader:load_all_data:160 - Total records loaded:
2025-06-30 23:47:06 | INFO     | data.data_loader:load_all_data:161 -    - Providers: 50
2025-06-30 23:47:06.362 | INFO     | data.data_loader:load_all_data:161 -    - Providers: 50
2025-06-30 23:47:06 | INFO     | data.data_loader:load_all_data:162 -    - Consumers: 100
2025-06-30 23:47:06.363 | INFO     | data.data_loader:load_all_data:162 -    - Consumers: 100
2025-06-30 23:47:06 | INFO     | data.data_loader:load_all_data:163 -    - Appointments: 100
2025-06-30 23:47:06.363 | INFO     | data.data_loader:load_all_data:163 -    - Appointments: 100
2025-06-30 23:47:06 | INFO     | data.data_loader:load_all_data:164 - All data loaded successfully!
2025-06-30 23:47:06.363 | INFO     | data.data_loader:load_all_data:164 - All data loaded successfully!
2025-06-30 23:47:06 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:47:06.363 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:47:06 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:47:06.363 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:47:06 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:47:06.364 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:47:06 | INFO     | services.scheduler_service:_init_mock_services:388 - Mock service clients initialized
2025-06-30 23:47:06.364 | INFO     | services.scheduler_service:_init_mock_services:388 - Mock service clients initialized
2025-06-30 23:47:06 | INFO     | services.scheduler_service:_init_rabbitmq:377 - RabbitMQ initialized successfully
2025-06-30 23:47:06.366 | INFO     | services.scheduler_service:_init_rabbitmq:377 - RabbitMQ initialized successfully
2025-06-30 23:47:06 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:47:06.366 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:47:06 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:47:06.366 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:47:06 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:47:06.399 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:47:06 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:47:06.399 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:47:06 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:47:06.400 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:47:06 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:47:06.400 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:47:13.223 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:47:13.224 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:47:13.231 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:47:13.232 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:47:13 | INFO     | services.scheduler_service:_register_event_handlers:435 - Event handlers would be registered in production RabbitMQ
2025-06-30 23:47:13.341 | INFO     | services.scheduler_service:_register_event_handlers:435 - Event handlers would be registered in production RabbitMQ
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:47:13.342 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:47:13.342 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:47:13.374 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:47:13.374 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:47:13.375 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:47:13.376 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:47:13.469 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:47:13.470 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:47:13.470 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:47:13 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:47:13.471 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:47:13 | INFO     | services.scheduler_service:__init__:368 - SchedulerService initialized successfully
2025-06-30 23:47:13.502 | INFO     | services.scheduler_service:__init__:368 - SchedulerService initialized successfully
2025-06-30 23:47:13 | INFO     | __main__:run_complete_example:47 - 🏥 CareAXL Hybrid Architecture - End-to-End Example
2025-06-30 23:47:13.502 | INFO     | __main__:run_complete_example:47 - 🏥 CareAXL Hybrid Architecture - End-to-End Example
2025-06-30 23:47:13 | INFO     | __main__:run_complete_example:48 - ================================================================================
2025-06-30 23:47:13.503 | INFO     | __main__:run_complete_example:48 - ================================================================================
2025-06-30 23:47:13 | INFO     | __main__:_phase_1_system_setup:76 - 🔧 Phase 1: System Setup and Validation
2025-06-30 23:47:13.503 | INFO     | __main__:_phase_1_system_setup:76 - 🔧 Phase 1: System Setup and Validation
2025-06-30 23:47:13 | INFO     | __main__:_phase_1_system_setup:77 - --------------------------------------------------
2025-06-30 23:47:13.504 | INFO     | __main__:_phase_1_system_setup:77 - --------------------------------------------------
2025-06-30 23:47:13 | INFO     | __main__:_phase_1_system_setup:80 - 1. Validating service clients...
2025-06-30 23:47:13.504 | INFO     | __main__:_phase_1_system_setup:80 - 1. Validating service clients...
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:47:13.504 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:47:13.505 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:47:13 | INFO     | __main__:_phase_1_system_setup:88 -    ✅ Staff Service: 50 providers available
2025-06-30 23:47:13.505 | INFO     | __main__:_phase_1_system_setup:88 -    ✅ Staff Service: 50 providers available
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:47:13.505 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:47:13.505 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:47:13 | INFO     | __main__:_phase_1_system_setup:98 -    ✅ Patient Service: 0 patients available
2025-06-30 23:47:13.506 | INFO     | __main__:_phase_1_system_setup:98 -    ✅ Patient Service: 0 patients available
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:47:13.506 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:47:13.507 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:47:13 | INFO     | __main__:_phase_1_system_setup:108 -    ✅ Appointment Service: 100 appointments available
2025-06-30 23:47:13.507 | INFO     | __main__:_phase_1_system_setup:108 -    ✅ Appointment Service: 100 appointments available
2025-06-30 23:47:13 | INFO     | __main__:_phase_1_system_setup:113 - 2. Validating event bus...
2025-06-30 23:47:13.508 | INFO     | __main__:_phase_1_system_setup:113 - 2. Validating event bus...
2025-06-30 23:47:13 | INFO     | __main__:_phase_1_system_setup:114 -    ✅ Event bus initialized
2025-06-30 23:47:13.508 | INFO     | __main__:_phase_1_system_setup:114 -    ✅ Event bus initialized
2025-06-30 23:47:13 | INFO     | __main__:_phase_1_system_setup:115 -    ✅ Event handlers registered
2025-06-30 23:47:13.508 | INFO     | __main__:_phase_1_system_setup:115 -    ✅ Event handlers registered
2025-06-30 23:47:13 | INFO     | __main__:_phase_1_system_setup:116 -    ✅ Event processing ready
2025-06-30 23:47:13.508 | INFO     | __main__:_phase_1_system_setup:116 -    ✅ Event processing ready
2025-06-30 23:47:13 | INFO     | __main__:_phase_1_system_setup:118 - 
2025-06-30 23:47:13.509 | INFO     | __main__:_phase_1_system_setup:118 - 
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:122 - ⚡ Phase 2: Synchronous Operations
2025-06-30 23:47:13.509 | INFO     | __main__:_phase_2_synchronous_operations:122 - ⚡ Phase 2: Synchronous Operations
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:123 - --------------------------------------------------
2025-06-30 23:47:13.509 | INFO     | __main__:_phase_2_synchronous_operations:123 - --------------------------------------------------
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:159 - 1. Get Skilled Nursing Providers...
2025-06-30 23:47:13.510 | INFO     | __main__:_phase_2_synchronous_operations:159 - 1. Get Skilled Nursing Providers...
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:47:13.510 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:47:13.510 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ✅ Success: 50 items retrieved
2025-06-30 23:47:13.510 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ✅ Success: 50 items retrieved
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:168 -    ⏱️  Response time: 0.000s
2025-06-30 23:47:13.511 | INFO     | __main__:_phase_2_synchronous_operations:168 -    ⏱️  Response time: 0.000s
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:159 - 2. Get Behavioral Care Providers...
2025-06-30 23:47:13.511 | INFO     | __main__:_phase_2_synchronous_operations:159 - 2. Get Behavioral Care Providers...
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:47:13.511 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:47:13.512 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ✅ Success: 50 items retrieved
2025-06-30 23:47:13.512 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ✅ Success: 50 items retrieved
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:168 -    ⏱️  Response time: 0.001s
2025-06-30 23:47:13.513 | INFO     | __main__:_phase_2_synchronous_operations:168 -    ⏱️  Response time: 0.001s
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:159 - 3. Get Patients for Care Episode...
2025-06-30 23:47:13.513 | INFO     | __main__:_phase_2_synchronous_operations:159 - 3. Get Patients for Care Episode...
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:47:13.513 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:47:13.514 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ✅ Success: 0 items retrieved
2025-06-30 23:47:13.514 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ✅ Success: 0 items retrieved
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:168 -    ⏱️  Response time: 0.001s
2025-06-30 23:47:13.514 | INFO     | __main__:_phase_2_synchronous_operations:168 -    ⏱️  Response time: 0.001s
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:159 - 4. Get Pending Appointments...
2025-06-30 23:47:13.515 | INFO     | __main__:_phase_2_synchronous_operations:159 - 4. Get Pending Appointments...
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:47:13.515 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:47:13 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:47:13.516 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ✅ Success: 100 items retrieved
2025-06-30 23:47:13.516 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ✅ Success: 100 items retrieved
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:168 -    ⏱️  Response time: 0.001s
2025-06-30 23:47:13.516 | INFO     | __main__:_phase_2_synchronous_operations:168 -    ⏱️  Response time: 0.001s
2025-06-30 23:47:13 | INFO     | __main__:_phase_2_synchronous_operations:185 - 
2025-06-30 23:47:13.517 | INFO     | __main__:_phase_2_synchronous_operations:185 - 
2025-06-30 23:47:13 | INFO     | __main__:_phase_3_asynchronous_events:189 - 🔄 Phase 3: Asynchronous Event Processing
2025-06-30 23:47:13.517 | INFO     | __main__:_phase_3_asynchronous_events:189 - 🔄 Phase 3: Asynchronous Event Processing
2025-06-30 23:47:13 | INFO     | __main__:_phase_3_asynchronous_events:190 - --------------------------------------------------
2025-06-30 23:47:13.517 | INFO     | __main__:_phase_3_asynchronous_events:190 - --------------------------------------------------
2025-06-30 23:47:13 | INFO     | __main__:_phase_3_asynchronous_events:251 - 1. Publishing Schedule Optimization Request...
2025-06-30 23:47:13.518 | INFO     | __main__:_phase_3_asynchronous_events:251 - 1. Publishing Schedule Optimization Request...
2025-06-30 23:48:33 | INFO     | assign_appointments:setup_logging:67 - Logging configured - console (INFO+) and file (DEBUG+): logs\scheduler_2025-06-30.log
2025-06-30 23:48:33 | WARNING  | events.rabbitmq_integration:<module>:26 - aio_pika not available. Using mock RabbitMQ implementation.
2025-06-30 23:48:33.477 | WARNING  | events.rabbitmq_integration:<module>:26 - aio_pika not available. Using mock RabbitMQ implementation.
2025-06-30 23:48:33 | INFO     | data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:48:33.479 | INFO     | data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:48:33 | INFO     | data.data_loader:load_all_data:153 - Starting data loading process...
2025-06-30 23:48:33.481 | INFO     | data.data_loader:load_all_data:153 - Starting data loading process...
2025-06-30 23:48:33 | INFO     | data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:48:33.481 | INFO     | data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:48:33 | INFO     | data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:48:33.482 | INFO     | data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:48:33 | INFO     | data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:48:33.655 | INFO     | data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Daniel Brown (LPN) - Skills: wound_care, assessment
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Ryan Rodriguez (LPN) - Skills: personal_care, mobility_assistance
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Nicole Lopez (PT) - Skills: assessment, meal_assistance, personal_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michael Martin (OT) - Skills: assessment, meal_assistance, wound_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Garcia (LPN) - Skills: housekeeping, mobility_assistance
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Jessica Davis (RN) - Skills: housekeeping, wound_care, meal_assistance
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Kevin Rodriguez (OT) - Skills: iv_therapy, mobility_assistance, wound_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Kevin Thompson (LPN) - Skills: wound_care, medication_management, meal_assistance
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: vital_signs, meal_assistance, mobility_assistance
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Stephanie Jones (RN) - Skills: medication_management, meal_assistance, housekeeping
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Christopher Johnson (OT) - Skills: wound_care, assessment, vital_signs
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. James Martinez (OT) - Skills: wound_care, meal_assistance
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. James Hernandez (OT) - Skills: assessment, vital_signs, wound_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Nicole Thompson (CNA) - Skills: meal_assistance, assessment
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Stephanie Hernandez (CNA) - Skills: meal_assistance, vital_signs, assessment
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Miller (RN) - Skills: iv_therapy, vital_signs, medication_management
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Stephanie Anderson (CNA) - Skills: iv_therapy, wound_care, mobility_assistance
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: meal_assistance, assessment, mobility_assistance
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Moore (LPN) - Skills: housekeeping, vital_signs, wound_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: medication_management, assessment, wound_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lauren Miller (OT) - Skills: medication_management, vital_signs, mobility_assistance
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Amanda Lee (PT) - Skills: iv_therapy, mobility_assistance, medication_management
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lisa Martin (OT) - Skills: mobility_assistance, housekeeping, assessment
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Andrew Martin (CNA) - Skills: mobility_assistance, meal_assistance, personal_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lisa Rodriguez (RN) - Skills: iv_therapy, wound_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. David Moore (LPN) - Skills: vital_signs, housekeeping, personal_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Christopher Lee (PT) - Skills: assessment, iv_therapy
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. William Johnson (LPN) - Skills: housekeeping, meal_assistance, wound_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Emily Thompson (LPN) - Skills: medication_management, mobility_assistance, housekeeping
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Andrew Garcia (RN) - Skills: mobility_assistance, wound_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michael Wilson (RN) - Skills: medication_management, housekeeping, wound_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Rachel Rodriguez (OT) - Skills: vital_signs, wound_care, mobility_assistance
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Lopez (OT) - Skills: vital_signs, mobility_assistance, assessment
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lauren Brown (RN) - Skills: medication_management, meal_assistance, wound_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: medication_management, assessment, personal_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Lee (OT) - Skills: meal_assistance, assessment, housekeeping
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Rachel Jackson (PT) - Skills: meal_assistance, assessment, iv_therapy
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Daniel Davis (RN) - Skills: meal_assistance, mobility_assistance, personal_care
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Nicole Martin (RN) - Skills: personal_care, mobility_assistance, iv_therapy
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Jessica Perez (LPN) - Skills: medication_management, personal_care, iv_therapy
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Ashley Wilson (PT) - Skills: iv_therapy, personal_care, meal_assistance
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Kevin Jones (PT) - Skills: vital_signs, housekeeping, medication_management
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Ryan Martin (RN) - Skills: assessment, meal_assistance, housekeeping
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lauren Martinez (CNA) - Skills: medication_management, iv_therapy
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Emily Johnson (OT) - Skills: medication_management, assessment
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Moore (RN) - Skills: mobility_assistance, medication_management, assessment
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michelle Garcia (LPN) - Skills: medication_management, meal_assistance, assessment
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michelle Anderson (CNA) - Skills: mobility_assistance, medication_management, vital_signs
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Jennifer Moore (LPN) - Skills: housekeeping, medication_management, mobility_assistance
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. James Moore (PT) - Skills: personal_care, assessment, iv_therapy
2025-06-30 23:48:33 | INFO     | data.data_loader:load_consumers:87 - === STAGE 2: Loading Consumer Data ===
2025-06-30 23:48:33.657 | INFO     | data.data_loader:load_consumers:87 - === STAGE 2: Loading Consumer Data ===
2025-06-30 23:48:33 | INFO     | data.data_loader:load_consumers:101 - Reading consumer data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\consumers.yml
2025-06-30 23:48:33.657 | INFO     | data.data_loader:load_consumers:101 - Reading consumer data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\consumers.yml
2025-06-30 23:48:33 | INFO     | data.data_loader:load_consumers:111 - ✅ Consumer data loaded: 100 consumers
2025-06-30 23:48:33.781 | INFO     | data.data_loader:load_consumers:111 - ✅ Consumer data loaded: 100 consumers
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Collins - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Garrett - Episode: episode-2440
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Mendoza - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Matthew Rose - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Burke - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Edward Williams - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Linda Palmer - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Cole - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - George Moreno - Episode: episode-2280
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Paul Olson - Episode: episode-6025
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Laura Cox - Episode: episode-8358
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Aguilar - Episode: episode-4641
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Kimberly Gardner - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Guerrero - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Nguyen - Episode: episode-5132
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Carl Rios - Episode: episode-5178
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Helen Townsend - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Daniel Wheeler - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Jonathan Kennedy - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Betty Harrison - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Patricia Gordon - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Lawrence Gonzalez - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Douglas Mcdonald - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Davis - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Evans - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Dennis Romero - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth McKenna - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Terry - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Terry Alexander - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Joshua Black - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Powell - Episode: episode-6559
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Wilson - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Patricia Hoffman - Episode: episode-7856
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Donald Aguilar - Episode: episode-7031
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Jacobs - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - George Henry - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Gregory Perry - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Jacob McDonald - Episode: episode-5471
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Peter Hicks - Episode: episode-3136
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Karen Alexander - Episode: episode-8466
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Rose - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Russell - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Sarah Carroll - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Barbara Reyes - Episode: episode-6626
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Weaver - Episode: episode-7890
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Laura McKenna - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Vazquez - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy McDaniel - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Justin Cleveland - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Carl Fernandez - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Laura Franklin - Episode: episode-2981
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Mason - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Karen Soto - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Christian Kim - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Betty Johnston - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Paul Herrera - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Kyle White - Episode: episode-2605
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Jerry Spencer - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Perry - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Charles Griffin - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - William Diaz - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Tyler Gonzalez - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Emily Cooper - Episode: episode-1979
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Jacob Murray - Episode: episode-5269
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Ray - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Nancy Wheeler - Episode: episode-3169
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Deborah Stephens - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Anthony Daniels - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Gregory Wilson - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Donald Herrera - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Flores - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Deborah Warren - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Brandon Morgan - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Rogers - Episode: episode-3059
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Wheeler - Episode: episode-2116
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Douglas Taylor - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Mark Pierce - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Emily Jensen - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Paul Webb - Episode: episode-9774
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Thompson - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Terry Dorsey - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Michael Hart - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Sarah Morgan - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Sean Thomas - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Gutierrez - Episode: episode-4864
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Johnson - Episode: episode-9681
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Nancy Andrews - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Arthur George - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Riley - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Linda Chavez - Episode: episode-4538
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Lane - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Kimberly Alvarez - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Hughes - Episode: episode-2864
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Richards - Episode: episode-4001
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Shaw - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Joshua Gray - Episode: episode-2319
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Sharon Mendoza - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Matthew Ruiz - Episode: episode-4651
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Betty Arnold - Episode: None
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_consumers:113 -    - Gregory Soto - Episode: None
2025-06-30 23:48:33 | INFO     | data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:48:33.784 | INFO     | data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:48:33 | INFO     | data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:48:33.784 | INFO     | data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:48:33 | INFO     | data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:48:33.942 | INFO     | data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-03
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 120min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-03
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-03
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 60min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 75min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 75min - 2025-07-03
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 75min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-07-22
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 60min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-15
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 75min - 2025-07-03
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 90min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 45min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 45min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 90min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 30min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-22
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 75min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-03
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 75min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-07-03
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-22
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-22
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 75min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 75min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-15
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-22
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-22
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 30min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 120min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 45min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-03
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 90min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 30min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-03
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-15
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-06-30
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 30min - 2025-07-22
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-03
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-07-22
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 60min - 2025-07-15
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-08
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-22
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-03
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-07-02
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-01
2025-06-30 23:48:33 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 90min - 2025-07-02
2025-06-30 23:48:33 | INFO     | data.data_loader:load_all_data:159 - === DATA LOADING SUMMARY ===
2025-06-30 23:48:33.944 | INFO     | data.data_loader:load_all_data:159 - === DATA LOADING SUMMARY ===
2025-06-30 23:48:33 | INFO     | data.data_loader:load_all_data:160 - Total records loaded:
2025-06-30 23:48:33.944 | INFO     | data.data_loader:load_all_data:160 - Total records loaded:
2025-06-30 23:48:33 | INFO     | data.data_loader:load_all_data:161 -    - Providers: 50
2025-06-30 23:48:33.944 | INFO     | data.data_loader:load_all_data:161 -    - Providers: 50
2025-06-30 23:48:33 | INFO     | data.data_loader:load_all_data:162 -    - Consumers: 100
2025-06-30 23:48:33.945 | INFO     | data.data_loader:load_all_data:162 -    - Consumers: 100
2025-06-30 23:48:33 | INFO     | data.data_loader:load_all_data:163 -    - Appointments: 100
2025-06-30 23:48:33.945 | INFO     | data.data_loader:load_all_data:163 -    - Appointments: 100
2025-06-30 23:48:33 | INFO     | data.data_loader:load_all_data:164 - All data loaded successfully!
2025-06-30 23:48:33.946 | INFO     | data.data_loader:load_all_data:164 - All data loaded successfully!
2025-06-30 23:48:33 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:48:33.946 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:48:33 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:48:33.947 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:48:33 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:48:33.947 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:48:33 | INFO     | services.scheduler_service:_init_mock_services:388 - Mock service clients initialized
2025-06-30 23:48:33.948 | INFO     | services.scheduler_service:_init_mock_services:388 - Mock service clients initialized
2025-06-30 23:48:33 | INFO     | services.scheduler_service:_init_rabbitmq:377 - RabbitMQ initialized successfully
2025-06-30 23:48:33.949 | INFO     | services.scheduler_service:_init_rabbitmq:377 - RabbitMQ initialized successfully
2025-06-30 23:48:33 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:48:33.950 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:48:33 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:48:33.950 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:48:33 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:48:33.977 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:48:33 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:48:33.978 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:48:33 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:48:33.978 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:48:33 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:48:33.979 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:48:40.306 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:48:40.307 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:48:40.315 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:48:40.315 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:48:40 | INFO     | services.scheduler_service:_register_event_handlers:435 - Event handlers would be registered in production RabbitMQ
2025-06-30 23:48:40.344 | INFO     | services.scheduler_service:_register_event_handlers:435 - Event handlers would be registered in production RabbitMQ
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:48:40.346 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:48:40.346 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:48:40.375 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:48:40.377 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:48:40.377 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:48:40.378 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:48:40.452 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:48:40.453 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:48:40.453 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:48:40 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:48:40.454 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:48:40 | INFO     | services.scheduler_service:__init__:368 - SchedulerService initialized successfully
2025-06-30 23:48:40.484 | INFO     | services.scheduler_service:__init__:368 - SchedulerService initialized successfully
2025-06-30 23:48:40 | INFO     | __main__:run_complete_example:46 - 🏥 CareAXL Hybrid Architecture - End-to-End Example
2025-06-30 23:48:40.485 | INFO     | __main__:run_complete_example:46 - 🏥 CareAXL Hybrid Architecture - End-to-End Example
2025-06-30 23:48:40 | INFO     | __main__:run_complete_example:47 - ================================================================================
2025-06-30 23:48:40.485 | INFO     | __main__:run_complete_example:47 - ================================================================================
2025-06-30 23:48:40 | INFO     | __main__:_phase_1_system_setup:75 - 🔧 Phase 1: System Setup and Validation
2025-06-30 23:48:40.486 | INFO     | __main__:_phase_1_system_setup:75 - 🔧 Phase 1: System Setup and Validation
2025-06-30 23:48:40 | INFO     | __main__:_phase_1_system_setup:76 - --------------------------------------------------
2025-06-30 23:48:40.486 | INFO     | __main__:_phase_1_system_setup:76 - --------------------------------------------------
2025-06-30 23:48:40 | INFO     | __main__:_phase_1_system_setup:79 - 1. Validating service clients...
2025-06-30 23:48:40.486 | INFO     | __main__:_phase_1_system_setup:79 - 1. Validating service clients...
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:48:40.487 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:48:40.487 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:48:40 | INFO     | __main__:_phase_1_system_setup:87 -    ✅ Staff Service: 50 providers available
2025-06-30 23:48:40.487 | INFO     | __main__:_phase_1_system_setup:87 -    ✅ Staff Service: 50 providers available
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:48:40.487 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:48:40.488 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:48:40 | INFO     | __main__:_phase_1_system_setup:97 -    ✅ Patient Service: 0 patients available
2025-06-30 23:48:40.488 | INFO     | __main__:_phase_1_system_setup:97 -    ✅ Patient Service: 0 patients available
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:48:40.488 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:48:40.488 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:48:40 | INFO     | __main__:_phase_1_system_setup:107 -    ✅ Appointment Service: 100 appointments available
2025-06-30 23:48:40.489 | INFO     | __main__:_phase_1_system_setup:107 -    ✅ Appointment Service: 100 appointments available
2025-06-30 23:48:40 | INFO     | __main__:_phase_1_system_setup:112 - 2. Validating event bus...
2025-06-30 23:48:40.489 | INFO     | __main__:_phase_1_system_setup:112 - 2. Validating event bus...
2025-06-30 23:48:40 | INFO     | __main__:_phase_1_system_setup:113 -    ✅ Event bus initialized
2025-06-30 23:48:40.489 | INFO     | __main__:_phase_1_system_setup:113 -    ✅ Event bus initialized
2025-06-30 23:48:40 | INFO     | __main__:_phase_1_system_setup:114 -    ✅ Event handlers registered
2025-06-30 23:48:40.489 | INFO     | __main__:_phase_1_system_setup:114 -    ✅ Event handlers registered
2025-06-30 23:48:40 | INFO     | __main__:_phase_1_system_setup:115 -    ✅ Event processing ready
2025-06-30 23:48:40.490 | INFO     | __main__:_phase_1_system_setup:115 -    ✅ Event processing ready
2025-06-30 23:48:40 | INFO     | __main__:_phase_1_system_setup:117 - 
2025-06-30 23:48:40.490 | INFO     | __main__:_phase_1_system_setup:117 - 
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:121 - ⚡ Phase 2: Synchronous Operations
2025-06-30 23:48:40.490 | INFO     | __main__:_phase_2_synchronous_operations:121 - ⚡ Phase 2: Synchronous Operations
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:122 - --------------------------------------------------
2025-06-30 23:48:40.491 | INFO     | __main__:_phase_2_synchronous_operations:122 - --------------------------------------------------
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:158 - 1. Get Skilled Nursing Providers...
2025-06-30 23:48:40.491 | INFO     | __main__:_phase_2_synchronous_operations:158 - 1. Get Skilled Nursing Providers...
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:48:40.492 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:48:40.492 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 50 items retrieved
2025-06-30 23:48:40.492 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 50 items retrieved
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.000s
2025-06-30 23:48:40.493 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.000s
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:158 - 2. Get Behavioral Care Providers...
2025-06-30 23:48:40.493 | INFO     | __main__:_phase_2_synchronous_operations:158 - 2. Get Behavioral Care Providers...
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:48:40.494 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:48:40.494 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 50 items retrieved
2025-06-30 23:48:40.495 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 50 items retrieved
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.001s
2025-06-30 23:48:40.495 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.001s
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:158 - 3. Get Patients for Care Episode...
2025-06-30 23:48:40.495 | INFO     | __main__:_phase_2_synchronous_operations:158 - 3. Get Patients for Care Episode...
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:48:40.496 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:48:40.496 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 0 items retrieved
2025-06-30 23:48:40.497 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 0 items retrieved
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.001s
2025-06-30 23:48:40.497 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.001s
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:158 - 4. Get Pending Appointments...
2025-06-30 23:48:40.498 | INFO     | __main__:_phase_2_synchronous_operations:158 - 4. Get Pending Appointments...
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:48:40.498 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:48:40.498 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 100 items retrieved
2025-06-30 23:48:40.498 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 100 items retrieved
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.000s
2025-06-30 23:48:40.499 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.000s
2025-06-30 23:48:40 | INFO     | __main__:_phase_2_synchronous_operations:184 - 
2025-06-30 23:48:40.499 | INFO     | __main__:_phase_2_synchronous_operations:184 - 
2025-06-30 23:48:40 | INFO     | __main__:_phase_3_asynchronous_events:188 - 🔄 Phase 3: Asynchronous Event Processing
2025-06-30 23:48:40.499 | INFO     | __main__:_phase_3_asynchronous_events:188 - 🔄 Phase 3: Asynchronous Event Processing
2025-06-30 23:48:40 | INFO     | __main__:_phase_3_asynchronous_events:189 - --------------------------------------------------
2025-06-30 23:48:40.499 | INFO     | __main__:_phase_3_asynchronous_events:189 - --------------------------------------------------
2025-06-30 23:48:40 | INFO     | __main__:_phase_3_asynchronous_events:250 - 1. Publishing Schedule Optimization Request...
2025-06-30 23:48:40.500 | INFO     | __main__:_phase_3_asynchronous_events:250 - 1. Publishing Schedule Optimization Request...
2025-06-30 23:48:40 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40.501 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:48:40.501 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:48:40 | INFO     | __main__:_phase_3_asynchronous_events:250 - 2. Publishing Appointment Created...
2025-06-30 23:48:40.502 | INFO     | __main__:_phase_3_asynchronous_events:250 - 2. Publishing Appointment Created...
2025-06-30 23:48:40 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40.503 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:48:40.503 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:48:40 | INFO     | __main__:_phase_3_asynchronous_events:250 - 3. Publishing Provider Availability Changed...
2025-06-30 23:48:40.503 | INFO     | __main__:_phase_3_asynchronous_events:250 - 3. Publishing Provider Availability Changed...
2025-06-30 23:48:40 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40.504 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:48:40.504 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:48:40 | INFO     | __main__:_phase_3_asynchronous_events:250 - 4. Publishing Patient Preferences Changed...
2025-06-30 23:48:40.504 | INFO     | __main__:_phase_3_asynchronous_events:250 - 4. Publishing Patient Preferences Changed...
2025-06-30 23:48:40 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40.505 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:48:40.506 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:48:40 | INFO     | __main__:_phase_3_asynchronous_events:250 - 5. Publishing External System Sync Request...
2025-06-30 23:48:40.506 | INFO     | __main__:_phase_3_asynchronous_events:250 - 5. Publishing External System Sync Request...
2025-06-30 23:48:40 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40.507 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:48:40.507 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:48:40 | INFO     | __main__:_phase_3_asynchronous_events:277 - 
2025-06-30 23:48:40.508 | INFO     | __main__:_phase_3_asynchronous_events:277 - 
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:281 - 🔄 Phase 4: Hybrid Scenarios (Sync + Async)
2025-06-30 23:48:40.508 | INFO     | __main__:_phase_4_hybrid_scenarios:281 - 🔄 Phase 4: Hybrid Scenarios (Sync + Async)
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:282 - --------------------------------------------------
2025-06-30 23:48:40.509 | INFO     | __main__:_phase_4_hybrid_scenarios:282 - --------------------------------------------------
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:350 - 1. Patient Books Regular Appointment
2025-06-30 23:48:40.509 | INFO     | __main__:_phase_4_hybrid_scenarios:350 - 1. Patient Books Regular Appointment
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:351 -    📝 Immediate booking with background optimization
2025-06-30 23:48:40.510 | INFO     | __main__:_phase_4_hybrid_scenarios:351 -    📝 Immediate booking with background optimization
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:359 -    🔄 Create Appointment...
2025-06-30 23:48:40.510 | INFO     | __main__:_phase_4_hybrid_scenarios:359 -    🔄 Create Appointment...
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:48:40.510 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:48:40 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: 28a34fbb-bbde-40ac-84c4-06034b4829df
2025-06-30 23:48:40.511 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: 28a34fbb-bbde-40ac-84c4-06034b4829df
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.010s: False
2025-06-30 23:48:40.521 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.010s: False
2025-06-30 23:48:40 | ERROR    | __main__:_phase_4_hybrid_scenarios:379 -       ❌ Failed: Failed to create appointment: no running event loop
2025-06-30 23:48:40.521 | ERROR    | __main__:_phase_4_hybrid_scenarios:379 -       ❌ Failed: Failed to create appointment: no running event loop
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:388 - 
2025-06-30 23:48:40.521 | INFO     | __main__:_phase_4_hybrid_scenarios:388 - 
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:350 - 2. Urgent Appointment Booking
2025-06-30 23:48:40.522 | INFO     | __main__:_phase_4_hybrid_scenarios:350 - 2. Urgent Appointment Booking
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:351 -    📝 Immediate booking with high-priority background processing
2025-06-30 23:48:40.522 | INFO     | __main__:_phase_4_hybrid_scenarios:351 -    📝 Immediate booking with high-priority background processing
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:359 -    🔄 Create Urgent Appointment...
2025-06-30 23:48:40.522 | INFO     | __main__:_phase_4_hybrid_scenarios:359 -    🔄 Create Urgent Appointment...
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:48:40.522 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:48:40 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: bf1e5928-60a4-4090-8fcc-5477015f6db9
2025-06-30 23:48:40.522 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: bf1e5928-60a4-4090-8fcc-5477015f6db9
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: False
2025-06-30 23:48:40.523 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: False
2025-06-30 23:48:40 | ERROR    | __main__:_phase_4_hybrid_scenarios:379 -       ❌ Failed: Failed to create appointment: no running event loop
2025-06-30 23:48:40.523 | ERROR    | __main__:_phase_4_hybrid_scenarios:379 -       ❌ Failed: Failed to create appointment: no running event loop
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:388 - 
2025-06-30 23:48:40.524 | INFO     | __main__:_phase_4_hybrid_scenarios:388 - 
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:350 - 3. Schedule Optimization Request
2025-06-30 23:48:40.524 | INFO     | __main__:_phase_4_hybrid_scenarios:350 - 3. Schedule Optimization Request
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:351 -    📝 Immediate acceptance with background optimization
2025-06-30 23:48:40.524 | INFO     | __main__:_phase_4_hybrid_scenarios:351 -    📝 Immediate acceptance with background optimization
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:359 -    🔄 Get Providers for Service...
2025-06-30 23:48:40.525 | INFO     | __main__:_phase_4_hybrid_scenarios:359 -    🔄 Get Providers for Service...
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:48:40.526 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:48:40.527 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:366 -       ✅ Success: Providers retrieved successfully
2025-06-30 23:48:40.528 | INFO     | __main__:_phase_4_hybrid_scenarios:366 -       ✅ Success: Providers retrieved successfully
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:367 -       ⏱️  Response time: 0.002s
2025-06-30 23:48:40.528 | INFO     | __main__:_phase_4_hybrid_scenarios:367 -       ⏱️  Response time: 0.002s
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:370 -       🔄 Background events triggered
2025-06-30 23:48:40.529 | INFO     | __main__:_phase_4_hybrid_scenarios:370 -       🔄 Background events triggered
2025-06-30 23:48:40 | INFO     | __main__:_phase_4_hybrid_scenarios:388 - 
2025-06-30 23:48:40.529 | INFO     | __main__:_phase_4_hybrid_scenarios:388 - 
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:392 - 🏥 Phase 5: Real Healthcare Workflows
2025-06-30 23:48:40.529 | INFO     | __main__:_phase_5_healthcare_workflows:392 - 🏥 Phase 5: Real Healthcare Workflows
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:393 - --------------------------------------------------
2025-06-30 23:48:40.529 | INFO     | __main__:_phase_5_healthcare_workflows:393 - --------------------------------------------------
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:429 - 1. New Patient Admission Workflow
2025-06-30 23:48:40.530 | INFO     | __main__:_phase_5_healthcare_workflows:429 - 1. New Patient Admission Workflow
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    1. Patient admitted to care episode
2025-06-30 23:48:40.530 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    1. Patient admitted to care episode
2025-06-30 23:48:40 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40.531 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40 | ERROR    | __main__:_phase_5_healthcare_workflows:450 -       ❌ Failed to publish event
2025-06-30 23:48:40.531 | ERROR    | __main__:_phase_5_healthcare_workflows:450 -       ❌ Failed to publish event
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    2. Initial assessment scheduled
2025-06-30 23:48:40.531 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    2. Initial assessment scheduled
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:48:40.532 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:48:40 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: 2246ef92-7bc4-45ea-ae60-70b8618c146f
2025-06-30 23:48:40.532 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: 2246ef92-7bc4-45ea-ae60-70b8618c146f
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: False
2025-06-30 23:48:40.532 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: False
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    3. Provider assigned
2025-06-30 23:48:40.532 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    3. Provider assigned
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    4. Schedule optimized
2025-06-30 23:48:40.532 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    4. Schedule optimized
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    5. Notifications sent
2025-06-30 23:48:40.533 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    5. Notifications sent
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:506 - 
2025-06-30 23:48:40.533 | INFO     | __main__:_phase_5_healthcare_workflows:506 - 
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:429 - 2. Provider Unavailability Workflow
2025-06-30 23:48:40.533 | INFO     | __main__:_phase_5_healthcare_workflows:429 - 2. Provider Unavailability Workflow
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    1. Provider reports unavailability
2025-06-30 23:48:40.533 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    1. Provider reports unavailability
2025-06-30 23:48:40 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40.534 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:48:40 | ERROR    | __main__:_phase_5_healthcare_workflows:485 -       ❌ Failed to publish event
2025-06-30 23:48:40.534 | ERROR    | __main__:_phase_5_healthcare_workflows:485 -       ❌ Failed to publish event
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    2. Affected appointments identified
2025-06-30 23:48:40.535 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    2. Affected appointments identified
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    3. Alternative providers found
2025-06-30 23:48:40.535 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    3. Alternative providers found
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    4. Schedule re-optimized
2025-06-30 23:48:40.535 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    4. Schedule re-optimized
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    5. Patients and providers notified
2025-06-30 23:48:40.535 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    5. Patients and providers notified
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:506 - 
2025-06-30 23:48:40.535 | INFO     | __main__:_phase_5_healthcare_workflows:506 - 
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:429 - 3. Emergency Appointment Workflow
2025-06-30 23:48:40.536 | INFO     | __main__:_phase_5_healthcare_workflows:429 - 3. Emergency Appointment Workflow
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    1. Emergency appointment created
2025-06-30 23:48:40.536 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    1. Emergency appointment created
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:48:40.537 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:48:40 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: 320710ca-d9ff-41fb-9cb1-8a15dacee3d3
2025-06-30 23:48:40.537 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: 320710ca-d9ff-41fb-9cb1-8a15dacee3d3
2025-06-30 23:48:40 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: False
2025-06-30 23:48:40.537 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: False
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    2. Immediate provider assignment
2025-06-30 23:48:40.538 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    2. Immediate provider assignment
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    3. Schedule adjusted
2025-06-30 23:48:40.538 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    3. Schedule adjusted
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    4. High-priority notifications
2025-06-30 23:48:40.538 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    4. High-priority notifications
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    5. External systems updated
2025-06-30 23:48:40.538 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    5. External systems updated
2025-06-30 23:48:40 | INFO     | __main__:_phase_5_healthcare_workflows:506 - 
2025-06-30 23:48:40.538 | INFO     | __main__:_phase_5_healthcare_workflows:506 - 
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:510 - 📊 Phase 6: Performance Analysis
2025-06-30 23:48:40.539 | INFO     | __main__:_phase_6_performance_analysis:510 - 📊 Phase 6: Performance Analysis
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:511 - --------------------------------------------------
2025-06-30 23:48:40.539 | INFO     | __main__:_phase_6_performance_analysis:511 - --------------------------------------------------
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:522 - Synchronous Operations Performance:
2025-06-30 23:48:40.539 | INFO     | __main__:_phase_6_performance_analysis:522 - Synchronous Operations Performance:
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:523 -    📈 Success rate: 4/4 (100.0%)
2025-06-30 23:48:40.539 | INFO     | __main__:_phase_6_performance_analysis:523 -    📈 Success rate: 4/4 (100.0%)
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:524 -    ⏱️  Average response time: 0.000s
2025-06-30 23:48:40.539 | INFO     | __main__:_phase_6_performance_analysis:524 -    ⏱️  Average response time: 0.000s
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:525 -    🏃 Fastest response: 0.000s
2025-06-30 23:48:40.541 | INFO     | __main__:_phase_6_performance_analysis:525 -    🏃 Fastest response: 0.000s
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:526 -    🐌 Slowest response: 0.001s
2025-06-30 23:48:40.541 | INFO     | __main__:_phase_6_performance_analysis:526 -    🐌 Slowest response: 0.001s
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:533 - Asynchronous Events Performance:
2025-06-30 23:48:40.542 | INFO     | __main__:_phase_6_performance_analysis:533 - Asynchronous Events Performance:
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:534 -    📤 Events published: 5
2025-06-30 23:48:40.542 | INFO     | __main__:_phase_6_performance_analysis:534 -    📤 Events published: 5
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:535 -    ⏱️  Average publish time: 0.001s
2025-06-30 23:48:40.543 | INFO     | __main__:_phase_6_performance_analysis:535 -    ⏱️  Average publish time: 0.001s
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:543 -    🎯 Events by priority:
2025-06-30 23:48:40.544 | INFO     | __main__:_phase_6_performance_analysis:543 -    🎯 Events by priority:
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:545 -       3: 1
2025-06-30 23:48:40.544 | INFO     | __main__:_phase_6_performance_analysis:545 -       3: 1
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:545 -       2: 2
2025-06-30 23:48:40.545 | INFO     | __main__:_phase_6_performance_analysis:545 -       2: 2
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:545 -       4: 1
2025-06-30 23:48:40.545 | INFO     | __main__:_phase_6_performance_analysis:545 -       4: 1
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:545 -       1: 1
2025-06-30 23:48:40.545 | INFO     | __main__:_phase_6_performance_analysis:545 -       1: 1
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:551 - Hybrid Scenarios Performance:
2025-06-30 23:48:40.546 | INFO     | __main__:_phase_6_performance_analysis:551 - Hybrid Scenarios Performance:
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:552 -    📊 Scenarios completed: 1/3
2025-06-30 23:48:40.546 | INFO     | __main__:_phase_6_performance_analysis:552 -    📊 Scenarios completed: 1/3
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:553 -    ✅ Success rate: 33.3%
2025-06-30 23:48:40.546 | INFO     | __main__:_phase_6_performance_analysis:553 -    ✅ Success rate: 33.3%
2025-06-30 23:48:40 | INFO     | __main__:_phase_6_performance_analysis:563 - 
2025-06-30 23:48:40.547 | INFO     | __main__:_phase_6_performance_analysis:563 - 
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:567 - 📋 Phase 7: Results Summary
2025-06-30 23:48:40.547 | INFO     | __main__:_phase_7_results_summary:567 - 📋 Phase 7: Results Summary
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:568 - --------------------------------------------------
2025-06-30 23:48:40.547 | INFO     | __main__:_phase_7_results_summary:568 - --------------------------------------------------
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:572 - 🎯 Architecture Performance Summary:
2025-06-30 23:48:40.547 | INFO     | __main__:_phase_7_results_summary:572 - 🎯 Architecture Performance Summary:
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:573 -    ✅ Synchronous Operations: 100.0% success rate
2025-06-30 23:48:40.547 | INFO     | __main__:_phase_7_results_summary:573 -    ✅ Synchronous Operations: 100.0% success rate
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:574 -    ⚡ Average Response Time: 0.000s
2025-06-30 23:48:40.547 | INFO     | __main__:_phase_7_results_summary:574 -    ⚡ Average Response Time: 0.000s
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:575 -    📤 Events Published: 5
2025-06-30 23:48:40.547 | INFO     | __main__:_phase_7_results_summary:575 -    📤 Events Published: 5
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:576 -    🔄 Hybrid Scenarios: 33.3% success rate
2025-06-30 23:48:40.548 | INFO     | __main__:_phase_7_results_summary:576 -    🔄 Hybrid Scenarios: 33.3% success rate
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:578 - 
2025-06-30 23:48:40.548 | INFO     | __main__:_phase_7_results_summary:578 - 
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:579 - 🏆 Key Achievements:
2025-06-30 23:48:40.548 | INFO     | __main__:_phase_7_results_summary:579 - 🏆 Key Achievements:
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:580 -    🎯 Immediate user responses for critical operations
2025-06-30 23:48:40.548 | INFO     | __main__:_phase_7_results_summary:580 -    🎯 Immediate user responses for critical operations
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:581 -    🔄 Scalable background processing for optimization
2025-06-30 23:48:40.548 | INFO     | __main__:_phase_7_results_summary:581 -    🔄 Scalable background processing for optimization
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:582 -    🏥 Seamless healthcare workflow integration
2025-06-30 23:48:40.549 | INFO     | __main__:_phase_7_results_summary:582 -    🏥 Seamless healthcare workflow integration
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:583 -    📊 Comprehensive performance monitoring
2025-06-30 23:48:40.549 | INFO     | __main__:_phase_7_results_summary:583 -    📊 Comprehensive performance monitoring
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:584 -    🔧 Flexible and maintainable architecture
2025-06-30 23:48:40.549 | INFO     | __main__:_phase_7_results_summary:584 -    🔧 Flexible and maintainable architecture
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:586 - 
2025-06-30 23:48:40.549 | INFO     | __main__:_phase_7_results_summary:586 - 
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:587 - 💡 Architecture Benefits Demonstrated:
2025-06-30 23:48:40.549 | INFO     | __main__:_phase_7_results_summary:587 - 💡 Architecture Benefits Demonstrated:
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:588 -    • Synchronous services provide immediate user satisfaction
2025-06-30 23:48:40.550 | INFO     | __main__:_phase_7_results_summary:588 -    • Synchronous services provide immediate user satisfaction
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:589 -    • Asynchronous events handle heavy background processing
2025-06-30 23:48:40.550 | INFO     | __main__:_phase_7_results_summary:589 -    • Asynchronous events handle heavy background processing
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:590 -    • Hybrid approach ensures optimal resource utilization
2025-06-30 23:48:40.550 | INFO     | __main__:_phase_7_results_summary:590 -    • Hybrid approach ensures optimal resource utilization
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:591 -    • Event-driven integration enables seamless system connectivity
2025-06-30 23:48:40.550 | INFO     | __main__:_phase_7_results_summary:591 -    • Event-driven integration enables seamless system connectivity
2025-06-30 23:48:40 | INFO     | __main__:_phase_7_results_summary:592 -    • Performance monitoring provides operational insights
2025-06-30 23:48:40.550 | INFO     | __main__:_phase_7_results_summary:592 -    • Performance monitoring provides operational insights
2025-06-30 23:48:40 | INFO     | __main__:run_complete_example:70 - ================================================================================
2025-06-30 23:48:40.551 | INFO     | __main__:run_complete_example:70 - ================================================================================
2025-06-30 23:48:40 | INFO     | __main__:run_complete_example:71 - 🎉 End-to-End Example Completed Successfully!
2025-06-30 23:48:40.551 | INFO     | __main__:run_complete_example:71 - 🎉 End-to-End Example Completed Successfully!
2025-06-30 23:53:11 | INFO     | assign_appointments:setup_logging:67 - Logging configured - console (INFO+) and file (DEBUG+): logs\scheduler_2025-06-30.log
2025-06-30 23:53:11 | WARNING  | events.rabbitmq_integration:<module>:26 - aio_pika not available. Using mock RabbitMQ implementation.
2025-06-30 23:53:11.647 | WARNING  | events.rabbitmq_integration:<module>:26 - aio_pika not available. Using mock RabbitMQ implementation.
2025-06-30 23:53:11 | INFO     | data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:53:11.650 | INFO     | data.data_loader:__init__:51 - DataLoader initialized: file-based
2025-06-30 23:53:11 | INFO     | data.data_loader:load_all_data:153 - Starting data loading process...
2025-06-30 23:53:11.651 | INFO     | data.data_loader:load_all_data:153 - Starting data loading process...
2025-06-30 23:53:11 | INFO     | data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:53:11.652 | INFO     | data.data_loader:load_providers:55 - === STAGE 1: Loading Provider Data ===
2025-06-30 23:53:11 | INFO     | data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:53:11.654 | INFO     | data.data_loader:load_providers:69 - Reading provider data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\providers.yml
2025-06-30 23:53:11 | INFO     | data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:53:11.800 | INFO     | data.data_loader:load_providers:79 - ✅ Provider data loaded: 50 providers
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Daniel Brown (LPN) - Skills: wound_care, assessment
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Ryan Rodriguez (LPN) - Skills: personal_care, mobility_assistance
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Nicole Lopez (PT) - Skills: assessment, meal_assistance, personal_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michael Martin (OT) - Skills: assessment, meal_assistance, wound_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Garcia (LPN) - Skills: housekeeping, mobility_assistance
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Jessica Davis (RN) - Skills: housekeeping, wound_care, meal_assistance
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Kevin Rodriguez (OT) - Skills: iv_therapy, mobility_assistance, wound_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Kevin Thompson (LPN) - Skills: wound_care, medication_management, meal_assistance
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: vital_signs, meal_assistance, mobility_assistance
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Stephanie Jones (RN) - Skills: medication_management, meal_assistance, housekeeping
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Christopher Johnson (OT) - Skills: wound_care, assessment, vital_signs
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. James Martinez (OT) - Skills: wound_care, meal_assistance
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. James Hernandez (OT) - Skills: assessment, vital_signs, wound_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Nicole Thompson (CNA) - Skills: meal_assistance, assessment
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Stephanie Hernandez (CNA) - Skills: meal_assistance, vital_signs, assessment
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Miller (RN) - Skills: iv_therapy, vital_signs, medication_management
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Stephanie Anderson (CNA) - Skills: iv_therapy, wound_care, mobility_assistance
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: meal_assistance, assessment, mobility_assistance
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Moore (LPN) - Skills: housekeeping, vital_signs, wound_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Wilson (OT) - Skills: medication_management, assessment, wound_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lauren Miller (OT) - Skills: medication_management, vital_signs, mobility_assistance
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Amanda Lee (PT) - Skills: iv_therapy, mobility_assistance, medication_management
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lisa Martin (OT) - Skills: mobility_assistance, housekeeping, assessment
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Andrew Martin (CNA) - Skills: mobility_assistance, meal_assistance, personal_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lisa Rodriguez (RN) - Skills: iv_therapy, wound_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. David Moore (LPN) - Skills: vital_signs, housekeeping, personal_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Christopher Lee (PT) - Skills: assessment, iv_therapy
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. William Johnson (LPN) - Skills: housekeeping, meal_assistance, wound_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Emily Thompson (LPN) - Skills: medication_management, mobility_assistance, housekeeping
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Andrew Garcia (RN) - Skills: mobility_assistance, wound_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michael Wilson (RN) - Skills: medication_management, housekeeping, wound_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Rachel Rodriguez (OT) - Skills: vital_signs, wound_care, mobility_assistance
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Lopez (OT) - Skills: vital_signs, mobility_assistance, assessment
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lauren Brown (RN) - Skills: medication_management, meal_assistance, wound_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. John Taylor (LPN) - Skills: medication_management, assessment, personal_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Lee (OT) - Skills: meal_assistance, assessment, housekeeping
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Rachel Jackson (PT) - Skills: meal_assistance, assessment, iv_therapy
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Daniel Davis (RN) - Skills: meal_assistance, mobility_assistance, personal_care
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Nicole Martin (RN) - Skills: personal_care, mobility_assistance, iv_therapy
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Jessica Perez (LPN) - Skills: medication_management, personal_care, iv_therapy
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Ashley Wilson (PT) - Skills: iv_therapy, personal_care, meal_assistance
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Kevin Jones (PT) - Skills: vital_signs, housekeeping, medication_management
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Ryan Martin (RN) - Skills: assessment, meal_assistance, housekeeping
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Lauren Martinez (CNA) - Skills: medication_management, iv_therapy
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Emily Johnson (OT) - Skills: medication_management, assessment
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Sarah Moore (RN) - Skills: mobility_assistance, medication_management, assessment
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michelle Garcia (LPN) - Skills: medication_management, meal_assistance, assessment
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Michelle Anderson (CNA) - Skills: mobility_assistance, medication_management, vital_signs
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. Jennifer Moore (LPN) - Skills: housekeeping, medication_management, mobility_assistance
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_providers:81 -    - Dr. James Moore (PT) - Skills: personal_care, assessment, iv_therapy
2025-06-30 23:53:11 | INFO     | data.data_loader:load_consumers:87 - === STAGE 2: Loading Consumer Data ===
2025-06-30 23:53:11.805 | INFO     | data.data_loader:load_consumers:87 - === STAGE 2: Loading Consumer Data ===
2025-06-30 23:53:11 | INFO     | data.data_loader:load_consumers:101 - Reading consumer data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\consumers.yml
2025-06-30 23:53:11.805 | INFO     | data.data_loader:load_consumers:101 - Reading consumer data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\consumers.yml
2025-06-30 23:53:11 | INFO     | data.data_loader:load_consumers:111 - ✅ Consumer data loaded: 100 consumers
2025-06-30 23:53:11.921 | INFO     | data.data_loader:load_consumers:111 - ✅ Consumer data loaded: 100 consumers
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Collins - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Garrett - Episode: episode-2440
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Mendoza - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Matthew Rose - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Burke - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Edward Williams - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Linda Palmer - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Cole - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - George Moreno - Episode: episode-2280
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Paul Olson - Episode: episode-6025
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Laura Cox - Episode: episode-8358
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Aguilar - Episode: episode-4641
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Kimberly Gardner - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Guerrero - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Nguyen - Episode: episode-5132
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Carl Rios - Episode: episode-5178
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Helen Townsend - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Daniel Wheeler - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Jonathan Kennedy - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Betty Harrison - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Patricia Gordon - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Lawrence Gonzalez - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Douglas Mcdonald - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Davis - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Evans - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Dennis Romero - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth McKenna - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Terry - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Terry Alexander - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Joshua Black - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Powell - Episode: episode-6559
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Wilson - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Patricia Hoffman - Episode: episode-7856
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Donald Aguilar - Episode: episode-7031
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Jacobs - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - George Henry - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Gregory Perry - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Jacob McDonald - Episode: episode-5471
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Peter Hicks - Episode: episode-3136
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Karen Alexander - Episode: episode-8466
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Rose - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Russell - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Sarah Carroll - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Barbara Reyes - Episode: episode-6626
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Weaver - Episode: episode-7890
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Laura McKenna - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Vazquez - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy McDaniel - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Justin Cleveland - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Carl Fernandez - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Laura Franklin - Episode: episode-2981
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Mason - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Karen Soto - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Christian Kim - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Betty Johnston - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Paul Herrera - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Kyle White - Episode: episode-2605
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Jerry Spencer - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Donna Perry - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Charles Griffin - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - William Diaz - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Tyler Gonzalez - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Emily Cooper - Episode: episode-1979
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Jacob Murray - Episode: episode-5269
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Ray - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Nancy Wheeler - Episode: episode-3169
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Deborah Stephens - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Anthony Daniels - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Gregory Wilson - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Donald Herrera - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Flores - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Deborah Warren - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Brandon Morgan - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Rogers - Episode: episode-3059
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Wheeler - Episode: episode-2116
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Douglas Taylor - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Mark Pierce - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Emily Jensen - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Paul Webb - Episode: episode-9774
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Thompson - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Terry Dorsey - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Michael Hart - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Sarah Morgan - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Sean Thomas - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Gutierrez - Episode: episode-4864
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Johnson - Episode: episode-9681
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Nancy Andrews - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Arthur George - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Sandra Riley - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Linda Chavez - Episode: episode-4538
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Michelle Lane - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Kimberly Alvarez - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Dorothy Hughes - Episode: episode-2864
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Lisa Richards - Episode: episode-4001
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Ruth Shaw - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Joshua Gray - Episode: episode-2319
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Sharon Mendoza - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Matthew Ruiz - Episode: episode-4651
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Betty Arnold - Episode: None
2025-06-30 23:53:11 | DEBUG    | data.data_loader:load_consumers:113 -    - Gregory Soto - Episode: None
2025-06-30 23:53:11 | INFO     | data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:53:11.924 | INFO     | data.data_loader:load_appointments:119 - === STAGE 3: Loading Appointment Data ===
2025-06-30 23:53:11 | INFO     | data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:53:11.925 | INFO     | data.data_loader:load_appointments:133 - Reading appointment data from: C:\d\Scheduler\caxl-scheduling-engine\src\data\appointments.yml
2025-06-30 23:53:12 | INFO     | data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:53:12.075 | INFO     | data.data_loader:load_appointments:143 - ✅ Appointment data loaded: 100 appointments
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-03
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 120min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-03
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-03
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 60min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 75min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 75min - 2025-07-03
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 75min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 90min - 2025-07-22
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 60min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-15
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 75min - 2025-07-03
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 90min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 45min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 60min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 45min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-15
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 90min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 30min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-22
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 75min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-03
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 75min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-07-03
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 120min - 2025-07-22
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-22
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 75min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 60min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 75min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 45min - 2025-07-15
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 30min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-22
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 60min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-22
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 60min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular CNA - 30min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 120min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 45min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 75min - 2025-07-03
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT OT - 45min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-03
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 45min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT LPN - 30min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 75min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular PT - 90min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular OT - 90min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 30min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 45min - 2025-07-03
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 60min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-15
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 30min - 2025-06-30
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 90min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular RN - 30min - 2025-07-22
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT PT - 120min - 2025-07-03
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 60min - 2025-07-22
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 45min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 60min - 2025-07-15
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular LPN - 90min - 2025-07-08
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 30min - 2025-07-22
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - Regular None - 60min - 2025-07-03
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 120min - 2025-07-02
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT None - 45min - 2025-07-22
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT RN - 120min - 2025-07-01
2025-06-30 23:53:12 | DEBUG    | data.data_loader:load_appointments:146 -    - URGENT CNA - 90min - 2025-07-02
2025-06-30 23:53:12 | INFO     | data.data_loader:load_all_data:159 - === DATA LOADING SUMMARY ===
2025-06-30 23:53:12.078 | INFO     | data.data_loader:load_all_data:159 - === DATA LOADING SUMMARY ===
2025-06-30 23:53:12 | INFO     | data.data_loader:load_all_data:160 - Total records loaded:
2025-06-30 23:53:12.079 | INFO     | data.data_loader:load_all_data:160 - Total records loaded:
2025-06-30 23:53:12 | INFO     | data.data_loader:load_all_data:161 -    - Providers: 50
2025-06-30 23:53:12.079 | INFO     | data.data_loader:load_all_data:161 -    - Providers: 50
2025-06-30 23:53:12 | INFO     | data.data_loader:load_all_data:162 -    - Consumers: 100
2025-06-30 23:53:12.079 | INFO     | data.data_loader:load_all_data:162 -    - Consumers: 100
2025-06-30 23:53:12 | INFO     | data.data_loader:load_all_data:163 -    - Appointments: 100
2025-06-30 23:53:12.079 | INFO     | data.data_loader:load_all_data:163 -    - Appointments: 100
2025-06-30 23:53:12 | INFO     | data.data_loader:load_all_data:164 - All data loaded successfully!
2025-06-30 23:53:12.080 | INFO     | data.data_loader:load_all_data:164 - All data loaded successfully!
2025-06-30 23:53:12 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:53:12.080 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:53:12 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:53:12.080 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:53:12 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:53:12.081 | INFO     | api.service_clients:__init__:197 - Initialized mock service client with 3 data items
2025-06-30 23:53:12 | INFO     | services.scheduler_service:_init_mock_services:388 - Mock service clients initialized
2025-06-30 23:53:12.081 | INFO     | services.scheduler_service:_init_mock_services:388 - Mock service clients initialized
2025-06-30 23:53:12 | INFO     | services.scheduler_service:_init_rabbitmq:377 - RabbitMQ initialized successfully
2025-06-30 23:53:12.082 | INFO     | services.scheduler_service:_init_rabbitmq:377 - RabbitMQ initialized successfully
2025-06-30 23:53:12 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:53:12.083 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:53:12 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:53:12.083 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:53:12 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:53:12.111 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:53:12 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:53:12.112 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:53:12 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:53:12.113 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:53:12 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:53:12.113 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:53:25.487 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:53:25.488 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:53:25.502 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:53:25.503 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:53:25 | INFO     | services.scheduler_service:_register_event_handlers:435 - Event handlers would be registered in production RabbitMQ
2025-06-30 23:53:25.578 | INFO     | services.scheduler_service:_register_event_handlers:435 - Event handlers would be registered in production RabbitMQ
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:53:25.580 | INFO     | assign_appointments:__init__:77 - Initializing AssignAppointmentJob...
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:53:25.580 | INFO     | assign_appointments:__init__:80 - Loading configurations...
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:53:25.670 | INFO     | assign_appointments:__init__:85 - Configurations loaded successfully
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:53:25.671 | INFO     | assign_appointments:__init__:89 - Creating solver configuration...
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:53:25.671 | INFO     | assign_appointments:__init__:101 - Solver configuration created
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:53:25.675 | INFO     | assign_appointments:__init__:103 - Creating solver factory...
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:53:25.897 | INFO     | assign_appointments:__init__:105 - Solver factory created
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:53:25.898 | INFO     | assign_appointments:__init__:107 - Creating solution manager...
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:53:25.899 | INFO     | assign_appointments:__init__:109 - Solution manager created
2025-06-30 23:53:25 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:53:25.899 | INFO     | assign_appointments:__init__:111 - AssignAppointment job initialized with solver config
2025-06-30 23:53:25 | INFO     | services.scheduler_service:__init__:368 - SchedulerService initialized successfully
2025-06-30 23:53:25.981 | INFO     | services.scheduler_service:__init__:368 - SchedulerService initialized successfully
2025-06-30 23:53:25 | INFO     | __main__:run_complete_example:46 - 🏥 CareAXL Hybrid Architecture - End-to-End Example
2025-06-30 23:53:25.982 | INFO     | __main__:run_complete_example:46 - 🏥 CareAXL Hybrid Architecture - End-to-End Example
2025-06-30 23:53:25 | INFO     | __main__:run_complete_example:47 - ================================================================================
2025-06-30 23:53:25.982 | INFO     | __main__:run_complete_example:47 - ================================================================================
2025-06-30 23:53:25 | INFO     | __main__:_phase_1_system_setup:75 - 🔧 Phase 1: System Setup and Validation
2025-06-30 23:53:25.983 | INFO     | __main__:_phase_1_system_setup:75 - 🔧 Phase 1: System Setup and Validation
2025-06-30 23:53:25 | INFO     | __main__:_phase_1_system_setup:76 - --------------------------------------------------
2025-06-30 23:53:25.983 | INFO     | __main__:_phase_1_system_setup:76 - --------------------------------------------------
2025-06-30 23:53:25 | INFO     | __main__:_phase_1_system_setup:79 - 1. Validating service clients...
2025-06-30 23:53:25.984 | INFO     | __main__:_phase_1_system_setup:79 - 1. Validating service clients...
2025-06-30 23:53:25 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:53:25.985 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:53:25 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:25.986 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:25 | INFO     | __main__:_phase_1_system_setup:87 -    ✅ Staff Service: 50 providers available
2025-06-30 23:53:25.987 | INFO     | __main__:_phase_1_system_setup:87 -    ✅ Staff Service: 50 providers available
2025-06-30 23:53:25 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:53:25.988 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:53:25 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:25.989 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:25 | INFO     | __main__:_phase_1_system_setup:97 -    ✅ Patient Service: 0 patients available
2025-06-30 23:53:25.990 | INFO     | __main__:_phase_1_system_setup:97 -    ✅ Patient Service: 0 patients available
2025-06-30 23:53:25 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:53:25.991 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:53:25 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:25.992 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:25 | INFO     | __main__:_phase_1_system_setup:107 -    ✅ Appointment Service: 100 appointments available
2025-06-30 23:53:25.993 | INFO     | __main__:_phase_1_system_setup:107 -    ✅ Appointment Service: 100 appointments available
2025-06-30 23:53:25 | INFO     | __main__:_phase_1_system_setup:112 - 2. Validating event bus...
2025-06-30 23:53:25.994 | INFO     | __main__:_phase_1_system_setup:112 - 2. Validating event bus...
2025-06-30 23:53:25 | INFO     | __main__:_phase_1_system_setup:113 -    ✅ Event bus initialized
2025-06-30 23:53:25.995 | INFO     | __main__:_phase_1_system_setup:113 -    ✅ Event bus initialized
2025-06-30 23:53:25 | INFO     | __main__:_phase_1_system_setup:114 -    ✅ Event handlers registered
2025-06-30 23:53:25.995 | INFO     | __main__:_phase_1_system_setup:114 -    ✅ Event handlers registered
2025-06-30 23:53:25 | INFO     | __main__:_phase_1_system_setup:115 -    ✅ Event processing ready
2025-06-30 23:53:25.996 | INFO     | __main__:_phase_1_system_setup:115 -    ✅ Event processing ready
2025-06-30 23:53:25 | INFO     | __main__:_phase_1_system_setup:117 - 
2025-06-30 23:53:25.996 | INFO     | __main__:_phase_1_system_setup:117 - 
2025-06-30 23:53:25 | INFO     | __main__:_phase_2_synchronous_operations:121 - ⚡ Phase 2: Synchronous Operations
2025-06-30 23:53:25.997 | INFO     | __main__:_phase_2_synchronous_operations:121 - ⚡ Phase 2: Synchronous Operations
2025-06-30 23:53:25 | INFO     | __main__:_phase_2_synchronous_operations:122 - --------------------------------------------------
2025-06-30 23:53:25.997 | INFO     | __main__:_phase_2_synchronous_operations:122 - --------------------------------------------------
2025-06-30 23:53:25 | INFO     | __main__:_phase_2_synchronous_operations:158 - 1. Get Skilled Nursing Providers...
2025-06-30 23:53:25.998 | INFO     | __main__:_phase_2_synchronous_operations:158 - 1. Get Skilled Nursing Providers...
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:53:26.000 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:53:26.002 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.000s: True
2025-06-30 23:53:26 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 50 items retrieved
2025-06-30 23:53:26.003 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 50 items retrieved
2025-06-30 23:53:26 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.001s
2025-06-30 23:53:26.004 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.001s
2025-06-30 23:53:26 | INFO     | __main__:_phase_2_synchronous_operations:158 - 2. Get Behavioral Care Providers...
2025-06-30 23:53:26.005 | INFO     | __main__:_phase_2_synchronous_operations:158 - 2. Get Behavioral Care Providers...
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:53:26.006 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:26.007 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:26 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 50 items retrieved
2025-06-30 23:53:26.007 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 50 items retrieved
2025-06-30 23:53:26 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.001s
2025-06-30 23:53:26.009 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.001s
2025-06-30 23:53:26 | INFO     | __main__:_phase_2_synchronous_operations:158 - 3. Get Patients for Care Episode...
2025-06-30 23:53:26.010 | INFO     | __main__:_phase_2_synchronous_operations:158 - 3. Get Patients for Care Episode...
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:53:26.011 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_patients
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:26.012 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:26 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 0 items retrieved
2025-06-30 23:53:26.013 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 0 items retrieved
2025-06-30 23:53:26 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.002s
2025-06-30 23:53:26.015 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.002s
2025-06-30 23:53:26 | INFO     | __main__:_phase_2_synchronous_operations:158 - 4. Get Pending Appointments...
2025-06-30 23:53:26.015 | INFO     | __main__:_phase_2_synchronous_operations:158 - 4. Get Pending Appointments...
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:53:26.016 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_appointments
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:26.017 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:26 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 100 items retrieved
2025-06-30 23:53:26.018 | INFO     | __main__:_phase_2_synchronous_operations:166 -    ✅ Success: 100 items retrieved
2025-06-30 23:53:26 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.002s
2025-06-30 23:53:26.019 | INFO     | __main__:_phase_2_synchronous_operations:167 -    ⏱️  Response time: 0.002s
2025-06-30 23:53:26 | INFO     | __main__:_phase_2_synchronous_operations:184 - 
2025-06-30 23:53:26.019 | INFO     | __main__:_phase_2_synchronous_operations:184 - 
2025-06-30 23:53:26 | INFO     | __main__:_phase_3_asynchronous_events:188 - 🔄 Phase 3: Asynchronous Event Processing
2025-06-30 23:53:26.019 | INFO     | __main__:_phase_3_asynchronous_events:188 - 🔄 Phase 3: Asynchronous Event Processing
2025-06-30 23:53:26 | INFO     | __main__:_phase_3_asynchronous_events:189 - --------------------------------------------------
2025-06-30 23:53:26.020 | INFO     | __main__:_phase_3_asynchronous_events:189 - --------------------------------------------------
2025-06-30 23:53:26 | INFO     | __main__:_phase_3_asynchronous_events:250 - 1. Publishing Schedule Optimization Request...
2025-06-30 23:53:26.020 | INFO     | __main__:_phase_3_asynchronous_events:250 - 1. Publishing Schedule Optimization Request...
2025-06-30 23:53:26 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26.023 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:53:26.024 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:53:26 | INFO     | __main__:_phase_3_asynchronous_events:250 - 2. Publishing Appointment Created...
2025-06-30 23:53:26.025 | INFO     | __main__:_phase_3_asynchronous_events:250 - 2. Publishing Appointment Created...
2025-06-30 23:53:26 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26.029 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:53:26.029 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:53:26 | INFO     | __main__:_phase_3_asynchronous_events:250 - 3. Publishing Provider Availability Changed...
2025-06-30 23:53:26.030 | INFO     | __main__:_phase_3_asynchronous_events:250 - 3. Publishing Provider Availability Changed...
2025-06-30 23:53:26 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26.034 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:53:26.035 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:53:26 | INFO     | __main__:_phase_3_asynchronous_events:250 - 4. Publishing Patient Preferences Changed...
2025-06-30 23:53:26.036 | INFO     | __main__:_phase_3_asynchronous_events:250 - 4. Publishing Patient Preferences Changed...
2025-06-30 23:53:26 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26.040 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:53:26.041 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:53:26 | INFO     | __main__:_phase_3_asynchronous_events:250 - 5. Publishing External System Sync Request...
2025-06-30 23:53:26.042 | INFO     | __main__:_phase_3_asynchronous_events:250 - 5. Publishing External System Sync Request...
2025-06-30 23:53:26 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26.046 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:53:26.047 | ERROR    | __main__:_phase_3_asynchronous_events:269 -    ❌ Failed to publish event
2025-06-30 23:53:26 | INFO     | __main__:_phase_3_asynchronous_events:277 - 
2025-06-30 23:53:26.048 | INFO     | __main__:_phase_3_asynchronous_events:277 - 
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:281 - 🔄 Phase 4: Hybrid Scenarios (Sync + Async)
2025-06-30 23:53:26.048 | INFO     | __main__:_phase_4_hybrid_scenarios:281 - 🔄 Phase 4: Hybrid Scenarios (Sync + Async)
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:282 - --------------------------------------------------
2025-06-30 23:53:26.048 | INFO     | __main__:_phase_4_hybrid_scenarios:282 - --------------------------------------------------
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:350 - 1. Patient Books Regular Appointment
2025-06-30 23:53:26.050 | INFO     | __main__:_phase_4_hybrid_scenarios:350 - 1. Patient Books Regular Appointment
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:351 -    📝 Immediate booking with background optimization
2025-06-30 23:53:26.050 | INFO     | __main__:_phase_4_hybrid_scenarios:351 -    📝 Immediate booking with background optimization
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:359 -    🔄 Create Appointment...
2025-06-30 23:53:26.051 | INFO     | __main__:_phase_4_hybrid_scenarios:359 -    🔄 Create Appointment...
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:53:26.051 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:53:26 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: fb557bc3-6f5d-4fad-b940-1339c9d69261
2025-06-30 23:53:26.052 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: fb557bc3-6f5d-4fad-b940-1339c9d69261
2025-06-30 23:53:26 | ERROR    | services.scheduler_service:_publish_event:451 - Failed to publish event EventType.APPOINTMENT_CREATED: Not connected to RabbitMQ
2025-06-30 23:53:26.053 | ERROR    | services.scheduler_service:_publish_event:451 - Failed to publish event EventType.APPOINTMENT_CREATED: Not connected to RabbitMQ
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.003s: True
2025-06-30 23:53:26.054 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.003s: True
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:366 -       ✅ Success: Appointment created successfully
2025-06-30 23:53:26.055 | INFO     | __main__:_phase_4_hybrid_scenarios:366 -       ✅ Success: Appointment created successfully
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:367 -       ⏱️  Response time: 0.004s
2025-06-30 23:53:26.056 | INFO     | __main__:_phase_4_hybrid_scenarios:367 -       ⏱️  Response time: 0.004s
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:370 -       🔄 Background events triggered
2025-06-30 23:53:26.057 | INFO     | __main__:_phase_4_hybrid_scenarios:370 -       🔄 Background events triggered
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:388 - 
2025-06-30 23:53:26.057 | INFO     | __main__:_phase_4_hybrid_scenarios:388 - 
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:350 - 2. Urgent Appointment Booking
2025-06-30 23:53:26.058 | INFO     | __main__:_phase_4_hybrid_scenarios:350 - 2. Urgent Appointment Booking
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:351 -    📝 Immediate booking with high-priority background processing
2025-06-30 23:53:26.059 | INFO     | __main__:_phase_4_hybrid_scenarios:351 -    📝 Immediate booking with high-priority background processing
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:359 -    🔄 Create Urgent Appointment...
2025-06-30 23:53:26.060 | INFO     | __main__:_phase_4_hybrid_scenarios:359 -    🔄 Create Urgent Appointment...
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:53:26.061 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:53:26 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: 2f0e6048-c684-498c-89c9-4879e32ebac3
2025-06-30 23:53:26.062 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: 2f0e6048-c684-498c-89c9-4879e32ebac3
2025-06-30 23:53:26 | ERROR    | services.scheduler_service:_publish_event:451 - Failed to publish event EventType.APPOINTMENT_CREATED: Not connected to RabbitMQ
2025-06-30 23:53:26.064 | ERROR    | services.scheduler_service:_publish_event:451 - Failed to publish event EventType.APPOINTMENT_CREATED: Not connected to RabbitMQ
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.004s: True
2025-06-30 23:53:26.066 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.004s: True
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:366 -       ✅ Success: Appointment created successfully
2025-06-30 23:53:26.066 | INFO     | __main__:_phase_4_hybrid_scenarios:366 -       ✅ Success: Appointment created successfully
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:367 -       ⏱️  Response time: 0.005s
2025-06-30 23:53:26.067 | INFO     | __main__:_phase_4_hybrid_scenarios:367 -       ⏱️  Response time: 0.005s
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:370 -       🔄 Background events triggered
2025-06-30 23:53:26.067 | INFO     | __main__:_phase_4_hybrid_scenarios:370 -       🔄 Background events triggered
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:388 - 
2025-06-30 23:53:26.068 | INFO     | __main__:_phase_4_hybrid_scenarios:388 - 
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:350 - 3. Schedule Optimization Request
2025-06-30 23:53:26.069 | INFO     | __main__:_phase_4_hybrid_scenarios:350 - 3. Schedule Optimization Request
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:351 -    📝 Immediate acceptance with background optimization
2025-06-30 23:53:26.069 | INFO     | __main__:_phase_4_hybrid_scenarios:351 -    📝 Immediate acceptance with background optimization
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:359 -    🔄 Get Providers for Service...
2025-06-30 23:53:26.070 | INFO     | __main__:_phase_4_hybrid_scenarios:359 -    🔄 Get Providers for Service...
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:53:26.071 | INFO     | services.scheduler_service:process_request:458 - Processing request: get_providers
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:26.072 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.001s: True
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:366 -       ✅ Success: Providers retrieved successfully
2025-06-30 23:53:26.073 | INFO     | __main__:_phase_4_hybrid_scenarios:366 -       ✅ Success: Providers retrieved successfully
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:367 -       ⏱️  Response time: 0.002s
2025-06-30 23:53:26.073 | INFO     | __main__:_phase_4_hybrid_scenarios:367 -       ⏱️  Response time: 0.002s
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:370 -       🔄 Background events triggered
2025-06-30 23:53:26.074 | INFO     | __main__:_phase_4_hybrid_scenarios:370 -       🔄 Background events triggered
2025-06-30 23:53:26 | INFO     | __main__:_phase_4_hybrid_scenarios:388 - 
2025-06-30 23:53:26.075 | INFO     | __main__:_phase_4_hybrid_scenarios:388 - 
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:392 - 🏥 Phase 5: Real Healthcare Workflows
2025-06-30 23:53:26.075 | INFO     | __main__:_phase_5_healthcare_workflows:392 - 🏥 Phase 5: Real Healthcare Workflows
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:393 - --------------------------------------------------
2025-06-30 23:53:26.077 | INFO     | __main__:_phase_5_healthcare_workflows:393 - --------------------------------------------------
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:429 - 1. New Patient Admission Workflow
2025-06-30 23:53:26.077 | INFO     | __main__:_phase_5_healthcare_workflows:429 - 1. New Patient Admission Workflow
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    1. Patient admitted to care episode
2025-06-30 23:53:26.078 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    1. Patient admitted to care episode
2025-06-30 23:53:26 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26.082 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26 | ERROR    | __main__:_phase_5_healthcare_workflows:450 -       ❌ Failed to publish event
2025-06-30 23:53:26.082 | ERROR    | __main__:_phase_5_healthcare_workflows:450 -       ❌ Failed to publish event
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    2. Initial assessment scheduled
2025-06-30 23:53:26.083 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    2. Initial assessment scheduled
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:53:26.084 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:53:26 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: d387b0b0-b2eb-4af5-92c3-f24f6671787a
2025-06-30 23:53:26.085 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: d387b0b0-b2eb-4af5-92c3-f24f6671787a
2025-06-30 23:53:26 | ERROR    | services.scheduler_service:_publish_event:451 - Failed to publish event EventType.APPOINTMENT_CREATED: Not connected to RabbitMQ
2025-06-30 23:53:26.087 | ERROR    | services.scheduler_service:_publish_event:451 - Failed to publish event EventType.APPOINTMENT_CREATED: Not connected to RabbitMQ
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.004s: True
2025-06-30 23:53:26.087 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.004s: True
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:467 -       ✅ Assessment scheduled: None
2025-06-30 23:53:26.088 | INFO     | __main__:_phase_5_healthcare_workflows:467 -       ✅ Assessment scheduled: None
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    3. Provider assigned
2025-06-30 23:53:26.089 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    3. Provider assigned
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    4. Schedule optimized
2025-06-30 23:53:26.089 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    4. Schedule optimized
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    5. Notifications sent
2025-06-30 23:53:26.090 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    5. Notifications sent
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:506 - 
2025-06-30 23:53:26.090 | INFO     | __main__:_phase_5_healthcare_workflows:506 - 
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:429 - 2. Provider Unavailability Workflow
2025-06-30 23:53:26.090 | INFO     | __main__:_phase_5_healthcare_workflows:429 - 2. Provider Unavailability Workflow
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    1. Provider reports unavailability
2025-06-30 23:53:26.091 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    1. Provider reports unavailability
2025-06-30 23:53:26 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26.095 | ERROR    | __main__:_publish_event_sync:599 - Failed to publish event: Not connected to RabbitMQ
2025-06-30 23:53:26 | ERROR    | __main__:_phase_5_healthcare_workflows:485 -       ❌ Failed to publish event
2025-06-30 23:53:26.096 | ERROR    | __main__:_phase_5_healthcare_workflows:485 -       ❌ Failed to publish event
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    2. Affected appointments identified
2025-06-30 23:53:26.096 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    2. Affected appointments identified
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    3. Alternative providers found
2025-06-30 23:53:26.097 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    3. Alternative providers found
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    4. Schedule re-optimized
2025-06-30 23:53:26.098 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    4. Schedule re-optimized
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    5. Patients and providers notified
2025-06-30 23:53:26.098 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    5. Patients and providers notified
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:506 - 
2025-06-30 23:53:26.099 | INFO     | __main__:_phase_5_healthcare_workflows:506 - 
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:429 - 3. Emergency Appointment Workflow
2025-06-30 23:53:26.100 | INFO     | __main__:_phase_5_healthcare_workflows:429 - 3. Emergency Appointment Workflow
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    1. Emergency appointment created
2025-06-30 23:53:26.100 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    1. Emergency appointment created
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:53:26.101 | INFO     | services.scheduler_service:process_request:458 - Processing request: create_appointment
2025-06-30 23:53:26 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: c5cb1724-c72f-40d8-806f-9ff86cb5fbf1
2025-06-30 23:53:26.102 | INFO     | api.service_clients:create_appointment:251 - Mock appointment created: c5cb1724-c72f-40d8-806f-9ff86cb5fbf1
2025-06-30 23:53:26 | ERROR    | services.scheduler_service:_publish_event:451 - Failed to publish event EventType.APPOINTMENT_CREATED: Not connected to RabbitMQ
2025-06-30 23:53:26.103 | ERROR    | services.scheduler_service:_publish_event:451 - Failed to publish event EventType.APPOINTMENT_CREATED: Not connected to RabbitMQ
2025-06-30 23:53:26 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.005s: True
2025-06-30 23:53:26.106 | INFO     | services.scheduler_service:process_request:485 - Request completed in 0.005s: True
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:504 -       ✅ Emergency appointment created: None
2025-06-30 23:53:26.107 | INFO     | __main__:_phase_5_healthcare_workflows:504 -       ✅ Emergency appointment created: None
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    2. Immediate provider assignment
2025-06-30 23:53:26.109 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    2. Immediate provider assignment
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    3. Schedule adjusted
2025-06-30 23:53:26.110 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    3. Schedule adjusted
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    4. High-priority notifications
2025-06-30 23:53:26.111 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    4. High-priority notifications
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    5. External systems updated
2025-06-30 23:53:26.111 | INFO     | __main__:_phase_5_healthcare_workflows:432 -    5. External systems updated
2025-06-30 23:53:26 | INFO     | __main__:_phase_5_healthcare_workflows:506 - 
2025-06-30 23:53:26.112 | INFO     | __main__:_phase_5_healthcare_workflows:506 - 
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:510 - 📊 Phase 6: Performance Analysis
2025-06-30 23:53:26.113 | INFO     | __main__:_phase_6_performance_analysis:510 - 📊 Phase 6: Performance Analysis
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:511 - --------------------------------------------------
2025-06-30 23:53:26.114 | INFO     | __main__:_phase_6_performance_analysis:511 - --------------------------------------------------
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:522 - Synchronous Operations Performance:
2025-06-30 23:53:26.116 | INFO     | __main__:_phase_6_performance_analysis:522 - Synchronous Operations Performance:
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:523 -    📈 Success rate: 4/4 (100.0%)
2025-06-30 23:53:26.116 | INFO     | __main__:_phase_6_performance_analysis:523 -    📈 Success rate: 4/4 (100.0%)
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:524 -    ⏱️  Average response time: 0.002s
2025-06-30 23:53:26.117 | INFO     | __main__:_phase_6_performance_analysis:524 -    ⏱️  Average response time: 0.002s
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:525 -    🏃 Fastest response: 0.001s
2025-06-30 23:53:26.118 | INFO     | __main__:_phase_6_performance_analysis:525 -    🏃 Fastest response: 0.001s
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:526 -    🐌 Slowest response: 0.002s
2025-06-30 23:53:26.119 | INFO     | __main__:_phase_6_performance_analysis:526 -    🐌 Slowest response: 0.002s
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:533 - Asynchronous Events Performance:
2025-06-30 23:53:26.120 | INFO     | __main__:_phase_6_performance_analysis:533 - Asynchronous Events Performance:
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:534 -    📤 Events published: 5
2025-06-30 23:53:26.120 | INFO     | __main__:_phase_6_performance_analysis:534 -    📤 Events published: 5
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:535 -    ⏱️  Average publish time: 0.004s
2025-06-30 23:53:26.121 | INFO     | __main__:_phase_6_performance_analysis:535 -    ⏱️  Average publish time: 0.004s
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:543 -    🎯 Events by priority:
2025-06-30 23:53:26.122 | INFO     | __main__:_phase_6_performance_analysis:543 -    🎯 Events by priority:
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:545 -       3: 1
2025-06-30 23:53:26.123 | INFO     | __main__:_phase_6_performance_analysis:545 -       3: 1
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:545 -       2: 2
2025-06-30 23:53:26.125 | INFO     | __main__:_phase_6_performance_analysis:545 -       2: 2
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:545 -       4: 1
2025-06-30 23:53:26.125 | INFO     | __main__:_phase_6_performance_analysis:545 -       4: 1
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:545 -       1: 1
2025-06-30 23:53:26.126 | INFO     | __main__:_phase_6_performance_analysis:545 -       1: 1
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:551 - Hybrid Scenarios Performance:
2025-06-30 23:53:26.127 | INFO     | __main__:_phase_6_performance_analysis:551 - Hybrid Scenarios Performance:
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:552 -    📊 Scenarios completed: 3/3
2025-06-30 23:53:26.128 | INFO     | __main__:_phase_6_performance_analysis:552 -    📊 Scenarios completed: 3/3
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:553 -    ✅ Success rate: 100.0%
2025-06-30 23:53:26.129 | INFO     | __main__:_phase_6_performance_analysis:553 -    ✅ Success rate: 100.0%
2025-06-30 23:53:26 | INFO     | __main__:_phase_6_performance_analysis:563 - 
2025-06-30 23:53:26.130 | INFO     | __main__:_phase_6_performance_analysis:563 - 
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:567 - 📋 Phase 7: Results Summary
2025-06-30 23:53:26.131 | INFO     | __main__:_phase_7_results_summary:567 - 📋 Phase 7: Results Summary
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:568 - --------------------------------------------------
2025-06-30 23:53:26.132 | INFO     | __main__:_phase_7_results_summary:568 - --------------------------------------------------
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:572 - 🎯 Architecture Performance Summary:
2025-06-30 23:53:26.132 | INFO     | __main__:_phase_7_results_summary:572 - 🎯 Architecture Performance Summary:
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:573 -    ✅ Synchronous Operations: 100.0% success rate
2025-06-30 23:53:26.133 | INFO     | __main__:_phase_7_results_summary:573 -    ✅ Synchronous Operations: 100.0% success rate
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:574 -    ⚡ Average Response Time: 0.002s
2025-06-30 23:53:26.133 | INFO     | __main__:_phase_7_results_summary:574 -    ⚡ Average Response Time: 0.002s
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:575 -    📤 Events Published: 5
2025-06-30 23:53:26.133 | INFO     | __main__:_phase_7_results_summary:575 -    📤 Events Published: 5
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:576 -    🔄 Hybrid Scenarios: 100.0% success rate
2025-06-30 23:53:26.135 | INFO     | __main__:_phase_7_results_summary:576 -    🔄 Hybrid Scenarios: 100.0% success rate
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:578 - 
2025-06-30 23:53:26.136 | INFO     | __main__:_phase_7_results_summary:578 - 
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:579 - 🏆 Key Achievements:
2025-06-30 23:53:26.137 | INFO     | __main__:_phase_7_results_summary:579 - 🏆 Key Achievements:
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:580 -    🎯 Immediate user responses for critical operations
2025-06-30 23:53:26.138 | INFO     | __main__:_phase_7_results_summary:580 -    🎯 Immediate user responses for critical operations
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:581 -    🔄 Scalable background processing for optimization
2025-06-30 23:53:26.139 | INFO     | __main__:_phase_7_results_summary:581 -    🔄 Scalable background processing for optimization
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:582 -    🏥 Seamless healthcare workflow integration
2025-06-30 23:53:26.140 | INFO     | __main__:_phase_7_results_summary:582 -    🏥 Seamless healthcare workflow integration
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:583 -    📊 Comprehensive performance monitoring
2025-06-30 23:53:26.140 | INFO     | __main__:_phase_7_results_summary:583 -    📊 Comprehensive performance monitoring
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:584 -    🔧 Flexible and maintainable architecture
2025-06-30 23:53:26.141 | INFO     | __main__:_phase_7_results_summary:584 -    🔧 Flexible and maintainable architecture
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:586 - 
2025-06-30 23:53:26.141 | INFO     | __main__:_phase_7_results_summary:586 - 
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:587 - 💡 Architecture Benefits Demonstrated:
2025-06-30 23:53:26.142 | INFO     | __main__:_phase_7_results_summary:587 - 💡 Architecture Benefits Demonstrated:
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:588 -    • Synchronous services provide immediate user satisfaction
2025-06-30 23:53:26.142 | INFO     | __main__:_phase_7_results_summary:588 -    • Synchronous services provide immediate user satisfaction
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:589 -    • Asynchronous events handle heavy background processing
2025-06-30 23:53:26.143 | INFO     | __main__:_phase_7_results_summary:589 -    • Asynchronous events handle heavy background processing
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:590 -    • Hybrid approach ensures optimal resource utilization
2025-06-30 23:53:26.144 | INFO     | __main__:_phase_7_results_summary:590 -    • Hybrid approach ensures optimal resource utilization
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:591 -    • Event-driven integration enables seamless system connectivity
2025-06-30 23:53:26.145 | INFO     | __main__:_phase_7_results_summary:591 -    • Event-driven integration enables seamless system connectivity
2025-06-30 23:53:26 | INFO     | __main__:_phase_7_results_summary:592 -    • Performance monitoring provides operational insights
2025-06-30 23:53:26.146 | INFO     | __main__:_phase_7_results_summary:592 -    • Performance monitoring provides operational insights
2025-06-30 23:53:26 | INFO     | __main__:run_complete_example:70 - ================================================================================
2025-06-30 23:53:26.148 | INFO     | __main__:run_complete_example:70 - ================================================================================
2025-06-30 23:53:26 | INFO     | __main__:run_complete_example:71 - 🎉 End-to-End Example Completed Successfully!
2025-06-30 23:53:26.148 | INFO     | __main__:run_complete_example:71 - 🎉 End-to-End Example Completed Successfully!
