"""
Provider Skill and Role Validation Constraint (C001)

This constraint ensures that providers have all required skills and roles for the appointment type.
This is a HARD constraint that must be satisfied for valid assignments.

Simple logic: Provider must have ALL required skills (exact match only) and match required role.
Additional logic: Soft penalties for role hierarchy mismatches (e.g., <PERSON><PERSON> doing CNA work).
"""

from typing import List, Optional, Dict

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint
from loguru import logger

from model.domain import Provider, AppointmentData, ServiceConfig, Location
from model.planning_models import AppointmentAssignment
from constraints.base_constraints import with_config

# Constraint identifiers
REQUIRED_SKILLS = "Required skills"
PROVIDER_ROLE_MATCH = "Provider role match"
ROLE_HIERARCHY_PENALTY = "Role hierarchy penalty"
PROVIDER_SKILL_VALIDATION = "provider_skill_validation"
SKILL_MISMATCH_PENALTY = "skill_mismatch_penalty"

# --- Public Utility Functions (moved from utils) ---
def has_required_skills(provider: Provider, required_skills: List[str]) -> bool:
    """
    Check if provider has all required skills.
    
    Args:
        provider: Provider object with skills list
        required_skills: List of required skills
    
    Returns:
        bool: True if provider has all required skills, False otherwise
    """
    if required_skills is None or len(required_skills) == 0:
        return True
    
    if provider.skills is None:
        return False
    
    return all(skill in provider.skills for skill in required_skills)


def validate_appointment_constraints(appointment: AppointmentData, provider: Provider,
                                    target_date) -> Dict[str, bool]:
    """
    Validate all constraints for a specific appointment assignment.
    
    Args:
        appointment: AppointmentData object
        provider: Provider object
        target_date: Date of assignment
    
    Returns:
        Dict[str, bool]: Dictionary of constraint validation results
    """
    from .c002_asgn_date_based_availability import is_working_day
    from .c003_asgn_geographic_service_area import is_within_service_radius
    
    return {
        "has_required_skills": has_required_skills(provider, appointment.required_skills),
        "is_working_day": is_working_day(provider, target_date),
        "within_service_radius": is_within_service_radius(
            provider.home_location, appointment.location) if (provider.home_location and appointment.location) else True,
        "role_matches": provider.role == appointment.required_role if appointment.required_role else True,
        "duration_appropriate": appointment.duration_min <= provider.capacity.max_hours_per_day * 60
    }

# --- Original Production Constraints ---
@with_config('behavioral_care', return_type='constraint')
def required_skills(constraint_factory: ConstraintFactory, **kwargs) -> Constraint:
    """Provider must have all required skills for the appointment type."""
    # Handle Timefold validation calls where factory is None
    if constraint_factory is None:
        # Return a dummy constraint that does nothing
        return constraint_factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: (
                assignment.provider is not None and
                len(assignment.appointment_data.required_skills) > 0 and
                not _has_required_skills(assignment.provider, assignment.appointment_data.required_skills)))
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: _log_skill_violation(assignment))
            .as_constraint(REQUIRED_SKILLS))

@with_config('behavioral_care', return_type='constraint')
def provider_role_match(constraint_factory: ConstraintFactory, **kwargs) -> Constraint:
    """Provider role should match appointment requirements when specified."""
    # Handle Timefold validation calls where factory is None
    if constraint_factory is None:
        return constraint_factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: (assignment.provider is not None and
                                        assignment.appointment_data.required_role is not None and
                                        assignment.provider.role != assignment.appointment_data.required_role))
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: 1)
            .as_constraint(PROVIDER_ROLE_MATCH))

@with_config('behavioral_care', return_type='constraint')
def role_hierarchy_penalty(constraint_factory: ConstraintFactory, **kwargs) -> Constraint:
    """Apply soft penalties for role hierarchy mismatches (e.g., RN doing CNA work)."""
    # Handle Timefold validation calls where factory is None
    if constraint_factory is None:
        return constraint_factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
    
    # Temporarily disabled to avoid ServiceConfig creation issues during Timefold solving
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: False)  # Always false - constraint disabled
            .penalize(HardSoftScore.ONE_SOFT, lambda assignment: 0)  # No penalty
            .as_constraint("role_hierarchy_penalty_disabled"))

# --- New Decorator-Based Constraints ---
@with_config('behavioral_care', return_type='constraint')
def provider_skill_validation(factory: ConstraintFactory, **kwargs) -> Constraint:
    """Providers must have all required skills for their assigned appointments."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (
        factory.for_each(AppointmentAssignment)
        .filter(lambda assignment: (
            assignment.provider is not None and
            assignment.appointment_data.required_skills is not None and
            not _has_required_skills(assignment.provider, assignment.appointment_data.required_skills)
        ))
        .penalize(HardSoftScore.ONE_HARD, lambda assignment: 
            _log_skill_mismatch(assignment))
        .as_constraint(PROVIDER_SKILL_VALIDATION)
    )

@with_config('behavioral_care', return_type='constraint')
def skill_mismatch_penalty(factory: ConstraintFactory, **kwargs) -> Constraint:
    """Penalize assignments where provider skills don't perfectly match requirements."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (
        factory.for_each(AppointmentAssignment)
        .filter(lambda assignment: (
            assignment.provider is not None and
            assignment.appointment_data.required_skills is not None and
            not _has_perfect_skill_match(assignment.provider, assignment.appointment_data.required_skills)
        ))
        .penalize(HardSoftScore.ONE_SOFT, lambda assignment: 
            _calculate_skill_mismatch_penalty(assignment))
        .as_constraint(SKILL_MISMATCH_PENALTY)
    )

@with_config('behavioral_care', return_type='constraint')
def advanced_skill_validation(factory: ConstraintFactory, core_config=None, service_config=None, combined_config=None, **kwargs) -> Constraint:
    """
    Advanced skill validation that uses both core and service configurations.
    
    This constraint demonstrates how to access:
    - core_config: Global settings from scheduler.yml
    - service_config: Service-specific settings from behavioral_care.yml  
    - combined_config: Merged config (service overrides core)
    """
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
    
    # Example: Use core config for global settings
    global_skill_validation_enabled = core_config.get('skill_validation', {}).get('enabled', True) if core_config else True
    
    # Example: Use service config for service-specific settings
    service_required_skills = service_config.get('required_skills', []) if service_config else []
    
    # Example: Use combined config for fallback values
    default_penalty = combined_config.get('default_penalty', 1) if combined_config else 1
    
    if not global_skill_validation_enabled:
        # Skip validation if globally disabled
        return factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("skill_validation_disabled")
    
    return (
        factory.for_each(AppointmentAssignment)
        .filter(lambda assignment: (
            assignment.provider is not None and
            assignment.appointment_data.required_skills is not None and
            not has_required_skills(assignment.provider, assignment.appointment_data.required_skills)
        ))
        .penalize(HardSoftScore.ONE_HARD, lambda assignment: default_penalty)
        .as_constraint("advanced_skill_validation")
    )

# --- Helper functions ---
def _has_required_skills(provider: Provider, required_skills: list) -> bool:
    """Check if provider has all required skills."""
    if required_skills is None or len(required_skills) == 0:
        return True
    
    if provider.skills is None:
        return False
    
    return all(skill in provider.skills for skill in required_skills)

def _has_perfect_skill_match(provider: Provider, required_skills: list) -> bool:
    """Check if provider has exactly the required skills (no more, no less)."""
    if required_skills is None or len(required_skills) == 0:
        return True
    
    if provider.skills is None:
        return False
    
    # Check if provider has all required skills
    has_required = all(skill in provider.skills for skill in required_skills)
    
    # Check if provider has no extra skills (perfect match)
    has_only_required = len(provider.skills) == len(required_skills)
    
    return has_required and has_only_required

def _log_skill_mismatch(assignment: AppointmentAssignment) -> int:
    """Log skill mismatch and return penalty."""
    provider_name = assignment.provider.name if assignment.provider is not None else "None"
    required_skills = assignment.appointment_data.required_skills
    provider_skills = assignment.provider.skills if assignment.provider is not None else []
    
    logger.debug("HARD CONSTRAINT VIOLATION: Provider {} (skills: {}) assigned to appointment requiring skills: {}", 
                  provider_name, provider_skills, required_skills)
    return 1

def _calculate_skill_mismatch_penalty(assignment: AppointmentAssignment) -> int:
    """Calculate penalty for skill mismatch."""
    if assignment.provider is None:
        return 5  # Default penalty
    
    required_skills = assignment.appointment_data.required_skills
    provider_skills = assignment.provider.skills
    
    if required_skills is None or provider_skills is None:
        return 5  # Default penalty
    
    # Calculate penalty based on skill mismatch
    missing_skills = set(required_skills) - set(provider_skills)
    extra_skills = set(provider_skills) - set(required_skills)
    
    # Penalty increases with more missing skills
    penalty = len(missing_skills) * 2 + len(extra_skills)
    
    return max(1, penalty)  # Minimum penalty of 1

def _log_skill_violation(assignment: AppointmentAssignment) -> int:
    """Log skill violation and return penalty."""
    if assignment.provider is None:
        return 1
    
    logger.debug("HARD CONSTRAINT VIOLATION: Provider {} (skills: {}) assigned to appointment requiring skills: {}", 
                  assignment.provider.name, 
                  assignment.provider.skills, 
                  assignment.appointment_data.required_skills)
    return 1

# --- Constraint Factory ---
def provider_skill_validation_constraints(factory: ConstraintFactory):
    """Define all provider skill validation constraints."""
    return [
        required_skills(factory),
        provider_role_match(factory),
        role_hierarchy_penalty(factory),
        provider_skill_validation(factory),
        skill_mismatch_penalty(factory),
        advanced_skill_validation(factory),
    ] 