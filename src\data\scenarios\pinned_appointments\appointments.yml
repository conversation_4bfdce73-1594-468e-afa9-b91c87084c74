appointments:
  # Permanently pinned appointment (cannot be unpinned)
  - consumer_id: "pinned-consumer-001"
    required_skills: [ "complex_care", "wound_care" ]
    duration_min: 90
    appointment_date: "2024-01-15"
    priority: "urgent"
    pinning:
      is_pinned: true
      pinned_provider_id: "pinned-provider-001"
      pinned_time_slot: "10:00"
      pin_reason: "medical_necessity"
      can_unpin: false  # Cannot be unpinned
      pin_priority: 1  # Highest priority

  # Episode continuity pinning (can be unpinned if necessary)
  - consumer_id: "pinned-consumer-002"
    required_skills: [ "medication_management" ]
    duration_min: 30
    appointment_date: "2024-01-15"
    priority: "high"
    relationships:
      episode_id: "episode-complex-001"
      requires_same_provider: true
    pinning:
      is_pinned: true
      pinned_provider_id: "pinned-provider-001"
      pin_reason: "episode_continuity"
      can_unpin: true
      pin_priority: 2

  # Follow-up appointment in same episode
  - consumer_id: "pinned-consumer-002"
    required_skills: [ "wound_care", "assessment" ]
    duration_min: 60
    appointment_date: "2024-01-16"
    priority: "high"
    relationships:
      episode_id: "episode-complex-001"
      requires_same_provider: true
      predecessor_appointment_id: "previous_appointment_id"
    pinning:
      is_pinned: true
      pinned_provider_id: "pinned-provider-001"
      pin_reason: "episode_continuity"
      can_unpin: true
      pin_priority: 2

  # Time-slot pinned appointment
  - consumer_id: "pinned-consumer-004"
    required_skills: [ "basic_care" ]
    duration_min: 45
    appointment_date: "2024-01-15"
    priority: "normal"
    timing:
      preferred_time: "11:00"
      strict_timing: true
    pinning:
      is_pinned: true
      pinned_time_slot: "11:00"
      pin_reason: "patient_schedule_constraint"
      can_unpin: false
      pin_priority: 3

  # Provider preference pinning (lower priority)
  - consumer_id: "pinned-consumer-001"
    required_skills: [ "iv_therapy" ]
    duration_min: 30
    appointment_date: "2024-01-16"
    priority: "normal"
    pinning:
      is_pinned: true
      pinned_provider_id: "pinned-provider-001"
      pin_reason: "patient_preference"
      can_unpin: true
      pin_priority: 4

  # Unpinned appointment that should work around pinned ones
  - consumer_id: "pinned-consumer-003"
    required_skills: [ "medication_management" ]
    duration_min: 30
    appointment_date: "2024-01-15"
    priority: "normal"
    pinning:
      is_pinned: false

  # Another unpinned appointment
  - consumer_id: "pinned-consumer-005"
    required_skills: [ "basic_care" ]
    duration_min: 60
    appointment_date: "2024-01-15"
    priority: "normal"
    pinning:
      is_pinned: false

  # Conflicting pinned appointment (should cause scheduling conflict)
  - consumer_id: "pinned-consumer-006"
    required_skills: [ "companionship" ]
    duration_min: 120
    appointment_date: "2024-01-15"
    priority: "low"
    timing:
      preferred_time: "10:30"  # Conflicts with first pinned appointment
      strict_timing: true
    pinning:
      is_pinned: true
      pinned_provider_id: "pinned-provider-001"
      pinned_time_slot: "10:30"
      pin_reason: "patient_preference"
      can_unpin: true
      pin_priority: 5  # Lower priority, should be unpinned

  # Pinned to part-time provider
  - consumer_id: "pinned-consumer-003"
    required_skills: [ "companionship" ]
    duration_min: 90
    appointment_date: "2024-01-15"
    priority: "normal"
    pinning:
      is_pinned: true
      pinned_provider_id: "pinned-provider-003"
      pin_reason: "provider_rapport"
      can_unpin: true
      pin_priority: 4

  # Emergency appointment that might need to override pins
  - consumer_id: "pinned-consumer-005"
    required_skills: [ "assessment" ]
    duration_min: 30
    appointment_date: "2024-01-15"
    priority: "urgent"
    urgent: true
    pinning:
      is_pinned: false
