import yaml
import random

# Load consumers to get their IDs
with open("consumers.yml", "r", encoding="utf-8") as f:
    consumers_data = yaml.safe_load(f)
consumer_ids = [consumer["id"] for consumer in consumers_data["consumers"]]

print(f"Found {len(consumer_ids)} consumer IDs")

# Load appointments
with open("appointments.yml", "r", encoding="utf-8") as f:
    appointments_data = yaml.safe_load(f)

# Link each appointment to a random consumer ID
for appointment in appointments_data["appointments"]:
    appointment["consumer_id"] = random.choice(consumer_ids)

# Write back the updated appointments
with open("appointments.yml", "w", encoding="utf-8") as f:
    yaml.dump(appointments_data, f, sort_keys=False, allow_unicode=True)

print(f"Linked {len(appointments_data['appointments'])} appointments to consumer IDs") 