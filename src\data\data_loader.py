"""
Data loader for healthcare scheduling optimization.

This module loads demo data from YAML files instead of hardcoded Python objects,
making it easy to test various scenarios by simply editing the YAML files.
"""

from datetime import date, datetime, time
from pathlib import Path
from typing import List, Dict, Any, Optional
from uuid import uuid4, UUID, uuid5, NAMESPACE_DNS

import yaml
import sys
import os

# Add src to path for imports
src_path = os.path.join(os.path.dirname(__file__), '..', 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from model.domain import (
    Provider, Consumer, AppointmentData, Location,
    ProviderAvailability, ShiftPattern, ProviderCapacity, ProviderPreferences,
    ConsumerPreferences, AppointmentTiming, AppointmentRelationships, AppointmentPinning
)
from model.planning_models import ScheduledAppointment
from loguru import logger


class DataLoader:
    """Loads healthcare data from YAML files."""

    def __init__(self, data_folder: Optional[str] = None, scenario_data: Optional[Dict[str, Any]] = None):
        """Initialize data loader.

        Args:
            data_folder: Path to data folder (for file-based loading)
            scenario_data: Pre-loaded scenario data (for scenario-based loading)
        """
        if data_folder is None:
            # Default to the package-relative data directory
            data_folder = str(Path(__file__).parent)
        self.data_folder = Path(data_folder) if data_folder else None
        self.scenario_data = scenario_data

        # If using file-based loading, ensure folder exists
        if not self.scenario_data and self.data_folder and not self.data_folder.exists():
            raise FileNotFoundError(f"Data folder not found: {self.data_folder}")

        logger.info(f"DataLoader initialized: {'scenario-based' if scenario_data else 'file-based'}")

    def load_providers(self) -> List[Provider]:
        """Load providers from YAML file or scenario data."""
        logger.info("=== STAGE 1: Loading Provider Data ===")

        if self.scenario_data:
            # Load from scenario data
            logger.info("Reading provider data from scenario")
            provider_list = self.scenario_data.get('providers', [])
        else:
            # Load from file
            if not self.data_folder:
                raise ValueError("No data folder specified for file-based loading")
            providers_file = self.data_folder / "providers.yml"
            if not providers_file.exists():
                raise FileNotFoundError(f"Providers file not found: {providers_file}")

            logger.info(f"Reading provider data from: {providers_file}")
            with open(providers_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            provider_list = data.get('providers', [])

        providers = []
        for provider_data in provider_list:
            provider = self._create_provider_from_data(provider_data)
            providers.append(provider)

        logger.info(f"✅ Provider data loaded: {len(providers)} providers")
        for provider in providers:
            logger.debug(f"   - {provider.name} ({provider.role}) - Skills: {', '.join(provider.skills[:3])}")

        return providers

    def load_consumers(self) -> List[Consumer]:
        """Load consumers from YAML file or scenario data."""
        logger.info("=== STAGE 2: Loading Consumer Data ===")

        if self.scenario_data:
            # Load from scenario data
            logger.info("Reading consumer data from scenario")
            consumer_list = self.scenario_data.get('consumers', [])
        else:
            # Load from file
            if not self.data_folder:
                raise ValueError("No data folder specified for file-based loading")
            consumers_file = self.data_folder / "consumers.yml"
            if not consumers_file.exists():
                raise FileNotFoundError(f"Consumers file not found: {consumers_file}")

            logger.info(f"Reading consumer data from: {consumers_file}")
            with open(consumers_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            consumer_list = data.get('consumers', [])

        consumers = []
        for consumer_data in consumer_list:
            consumer = self._create_consumer_from_data(consumer_data)
            consumers.append(consumer)

        logger.info(f"✅ Consumer data loaded: {len(consumers)} consumers")
        for consumer in consumers:
            logger.debug(f"   - {consumer.name} - Episode: {consumer.care_episode_id}")

        return consumers

    def load_appointments(self) -> List[AppointmentData]:
        """Load appointments from YAML file or scenario data."""
        logger.info("=== STAGE 3: Loading Appointment Data ===")

        if self.scenario_data:
            # Load from scenario data
            logger.info("Reading appointment data from scenario")
            appointment_list = self.scenario_data.get('appointments', [])
        else:
            # Load from file
            if not self.data_folder:
                raise ValueError("No data folder specified for file-based loading")
            appointments_file = self.data_folder / "appointments.yml"
            if not appointments_file.exists():
                raise FileNotFoundError(f"Appointments file not found: {appointments_file}")

            logger.info(f"Reading appointment data from: {appointments_file}")
            with open(appointments_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            appointment_list = data.get('appointments', [])

        appointments = []
        for appointment_data in appointment_list:
            appointment = self._create_appointment_from_data(appointment_data)
            appointments.append(appointment)

        logger.info(f"✅ Appointment data loaded: {len(appointments)} appointments")
        for appointment in appointments:
            urgent_flag = "URGENT" if appointment.urgent else "Regular"
            logger.debug(
                f"   - {urgent_flag} {appointment.required_role} - {appointment.duration_min}min - {appointment.appointment_date}")

        return appointments

    def load_all_data(self) -> Dict[str, Any]:
        """Load all data (providers, consumers, appointments)."""
        logger.info("Starting data loading process...")

        providers = self.load_providers()
        consumers = self.load_consumers()
        appointments = self.load_appointments()

        logger.info("=== DATA LOADING SUMMARY ===")
        logger.info("Total records loaded:")
        logger.info(f"   - Providers: {len(providers)}")
        logger.info(f"   - Consumers: {len(consumers)}")
        logger.info(f"   - Appointments: {len(appointments)}")
        logger.info("All data loaded successfully!")

        return {
            "providers": providers,
            "consumers": consumers,
            "appointments": appointments
        }

    def _create_provider_from_data(self, data: Dict[str, Any]) -> Provider:
        """Create a Provider object from YAML data."""
        # Create location
        location_data = data.get('home_location', {})
        location = Location(
            latitude=location_data.get('latitude', 0.0),
            longitude=location_data.get('longitude', 0.0),
            city=location_data.get('city'),
            state=location_data.get('state'),
            address=location_data.get('address')
        )

        # Create availability
        availability_data = data.get('availability', {})
        availability = None
        if availability_data:
            working_days = [day.lower() for day in availability_data.get('working_days', [])]
            working_hours = availability_data.get('working_hours', ["08:00", "17:00"])
            break_periods = []
            for period in availability_data.get('break_periods', []):
                if len(period) == 2:
                    start_time = self._parse_time(period[0])
                    end_time = self._parse_time(period[1])
                    break_periods.append((start_time, end_time))

            availability = ProviderAvailability(
                working_days=working_days,
                break_periods=break_periods,
                working_hours=(self._parse_time(working_hours[0]), self._parse_time(working_hours[1]))
            )

        # Create capacity
        capacity_data = data.get('capacity', {})
        capacity = ProviderCapacity(
            max_allocated_task_points_in_day=capacity_data.get('max_allocated_task_points_in_day', 27),
            max_tasks_count_in_day=capacity_data.get('max_tasks_count_in_day', 6),
            max_hours_per_day=capacity_data.get('max_hours_per_day', 8),
            max_consecutive_tasks=capacity_data.get('max_consecutive_tasks', 4),
            min_break_between_tasks=capacity_data.get('min_break_between_tasks', 15)
        )

        # Create preferences
        preferences_data = data.get('provider_preferences', {})
        preferences = ProviderPreferences(
            preferred_task_types=preferences_data.get('preferred_task_types', []),
            blacklisted_task_types=preferences_data.get('blacklisted_task_types', []),
            preferred_consumers=preferences_data.get('preferred_consumers', [])
        )

        return Provider(
            id=parse_uuid(data.get('id', str(uuid4()))),  # Parse ID from YAML
            name=data.get('name', 'Unknown Provider'),
            home_location=location,
            languages=data.get('languages', ['English']),
            transportation=data.get('transportation'),
            role=data.get('role'),
            skills=data.get('skills', []),
            availability=availability,
            capacity=capacity,
            provider_preferences=preferences
        )

    def _create_consumer_from_data(self, data: Dict[str, Any]) -> Consumer:
        """Create a Consumer object from YAML data."""
        # Create location
        location_data = data.get('location', {})
        location = Location(
            latitude=location_data.get('latitude', 0.0),
            longitude=location_data.get('longitude', 0.0),
            city=location_data.get('city'),
            state=location_data.get('state'),
            address=location_data.get('address')
        )

        # Create preferences
        preferences_data = data.get('consumer_preferences', {})
        preferences = ConsumerPreferences(
            preferred_days=[day.lower() for day in preferences_data.get('preferred_days', [])],
            preferred_hours=self._parse_time_range(preferences_data.get('preferred_hours')),
            unavailable_hours=[self._parse_time_range(period) for period in
                               preferences_data.get('unavailable_hours', [])],
            language=preferences_data.get('language'),
            cultural_considerations=preferences_data.get('cultural_considerations', [])
        )

        return Consumer(
            id=parse_uuid(data.get('id', str(uuid4()))),  # Parse ID from YAML
            name=data.get('name', 'Unknown Consumer'),
            location=location,
            care_episode_id=data.get('care_episode_id'),
            consumer_preferences=preferences
        )

    def _create_appointment_from_data(self, data: Dict[str, Any]) -> AppointmentData:
        """Create an AppointmentData object from YAML data."""
        # Create location
        location_data = data.get('location', {})
        location = Location(
            latitude=location_data.get('latitude', 0.0),
            longitude=location_data.get('longitude', 0.0),
            city=location_data.get('city'),
            state=location_data.get('state'),
            address=location_data.get('address')
        )

        # Create timing
        timing_data = data.get('timing', {})
        timing = AppointmentTiming(
            is_timed_visit=timing_data.get('is_timed_visit', False),
            preferred_time=self._parse_time(timing_data.get('preferred_time')) if timing_data.get(
                'preferred_time') else None,
            time_flexibility_minutes=timing_data.get('time_flexibility_minutes', 15)
        )

        # Create relationships
        relationships_data = data.get('relationships', {})
        relationships = AppointmentRelationships(
            care_episode_id=relationships_data.get('care_episode_id'),
            same_provider_required=relationships_data.get('same_provider_required', False)
        )

        # Create pinning
        pinning_data = data.get('pinning', {})
        pinning = AppointmentPinning(
            is_pinned=pinning_data.get('is_pinned', False)
        )

        return AppointmentData(
            id=uuid4(),  # Generate new UUID for each run
            consumer_id=parse_uuid(data.get('consumer_id', str(uuid4()))),  # Parse consumer_id from YAML
            appointment_date=self._parse_date(data.get('appointment_date', '')),
            required_skills=data.get('required_skills', []),
            duration_min=data.get('duration_min', 30),
            urgent=data.get('urgent', False),
            active=data.get('active', True),
            status=data.get('status', 'PENDING_TO_ASSIGN'),
            location=location,
            priority=data.get('priority', 'normal'),
            task_points=data.get('task_points'),
            required_role=data.get('required_role'),
            timing=timing,
            relationships=relationships,
            pinning=pinning
        )

    def _parse_time(self, time_str: str) -> time:
        """Parse time string to time object."""
        if not time_str:
            return time(8, 0)  # Default to 8:00 AM

        try:
            if ':' in time_str:
                hour, minute = map(int, time_str.split(':'))
                return time(hour, minute)
            else:
                # Handle 24-hour format like "0800"
                hour = int(time_str[:2])
                minute = int(time_str[2:])
                return time(hour, minute)
        except (ValueError, IndexError):
            return time(8, 0)  # Default to 8:00 AM

    def _parse_time_range(self, time_range: List[str]) -> tuple[time, time]:
        """Parse time range list to tuple of time objects."""
        if not time_range or len(time_range) != 2:
            return (time(8, 0), time(17, 0))  # Default 8 AM to 5 PM

        start_time = self._parse_time(time_range[0])
        end_time = self._parse_time(time_range[1])
        return (start_time, end_time)

    def _parse_date(self, date_str: str) -> date:
        """Parse date string to date object."""
        if not date_str:
            return date.today()

        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return date.today()


def create_demo_data(scenario_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Create demo data using YAML files or scenario data."""
    if scenario_data:
        data_loader = DataLoader(scenario_data=scenario_data)
    else:
        data_loader = DataLoader()
    return data_loader.load_all_data()


def create_demo_providers() -> List[Provider]:
    """Create demo providers using YAML files."""
    data_loader = DataLoader()
    return data_loader.load_providers()


def create_demo_consumers() -> List[Consumer]:
    """Create demo consumers using YAML files."""
    data_loader = DataLoader()
    return data_loader.load_consumers()


def create_demo_appointments(consumers: List[Consumer]) -> List[AppointmentData]:
    """Create demo appointments using YAML files."""
    data_loader = DataLoader()
    appointments = data_loader.load_appointments()

    # Consumer IDs are now properly parsed from YAML data
    # No need for manual linking since consumer_id is already set correctly
    return appointments


def create_demo_scheduled_appointments(target_date: date) -> List['ScheduledAppointment']:
    """Create demo scheduled appointments for day planning using YAML data."""
    from model.planning_models import ScheduledAppointment

    # Load data using data loader
    data_loader = DataLoader()
    providers = data_loader.load_providers()
    appointments = data_loader.load_appointments()

    # Create scheduled appointments (already assigned providers and dates)
    scheduled_appointments = []

    for i, appointment in enumerate(appointments[:6]):  # Use first 6 appointments
        # Assign to a provider based on skills
        provider = None
        for p in providers:
            if any(skill in p.skills for skill in appointment.required_skills):
                provider = p
                break

        if provider is None:
            provider = providers[0]  # Fallback

        # Create scheduled appointment
        scheduled_appointment = ScheduledAppointment(
            id=f"scheduled_{appointment.id}",
            appointment_data=appointment,
            provider=provider,
            assigned_date=target_date,
            assigned_time=None  # Will be assigned by day planner
        )
        scheduled_appointments.append(scheduled_appointment)

    return scheduled_appointments


def parse_uuid(id_str):
    # Deterministically generate a UUID from a string (for demo data)
    try:
        return UUID(id_str)
    except Exception:
        return uuid5(NAMESPACE_DNS, id_str)


def parse_date(date_str):
    if isinstance(date_str, date):
        return date_str
    return datetime.strptime(date_str, "%Y-%m-%d").date()


def parse_time(time_str):
    if time_str is None:
        return None
    if isinstance(time_str, time):
        return time_str
    return datetime.strptime(time_str, "%H:%M").time()


def parse_weekdays(days):
    return [day.lower() for day in days]


def parse_location(loc_dict):
    if not loc_dict:
        return None
    return Location(**loc_dict)


def parse_provider_availability(avail_dict):
    if not avail_dict:
        return None
    # Convert working_days and working_hours
    avail_dict = dict(avail_dict)
    if 'working_days' in avail_dict:
        avail_dict['working_days'] = parse_weekdays(avail_dict['working_days'])
    if 'working_hours' in avail_dict:
        avail_dict['working_hours'] = tuple(parse_time(t) for t in avail_dict['working_hours'])
    if 'break_periods' in avail_dict:
        avail_dict['break_periods'] = [tuple(parse_time(t) for t in period) for period in avail_dict['break_periods']]
    return ProviderAvailability(**avail_dict)


def parse_provider_capacity(cap_dict):
    if not cap_dict:
        return ProviderCapacity()
    return ProviderCapacity(**cap_dict)


def parse_provider_preferences(pref_dict):
    if not pref_dict:
        return ProviderPreferences()
    return ProviderPreferences(**pref_dict)


def parse_consumer_preferences(pref_dict):
    if not pref_dict:
        return ConsumerPreferences()
    # Parse preferred_days and unavailable_days
    if 'preferred_days' in pref_dict:
        pref_dict['preferred_days'] = parse_weekdays(pref_dict['preferred_days'])
    if 'unavailable_days' in pref_dict:
        pref_dict['unavailable_days'] = parse_weekdays(pref_dict['unavailable_days'])
    if 'preferred_hours' in pref_dict:
        pref_dict['preferred_hours'] = tuple(parse_time(t) for t in pref_dict['preferred_hours'])
    if 'unavailable_hours' in pref_dict:
        pref_dict['unavailable_hours'] = [tuple(parse_time(t) for t in period) for period in
                                          pref_dict['unavailable_hours']]
    return ConsumerPreferences(**pref_dict)


def parse_timing(timing_dict):
    if not timing_dict:
        return AppointmentTiming()
    if 'preferred_time' in timing_dict:
        timing_dict['preferred_time'] = parse_time(timing_dict['preferred_time'])
    return AppointmentTiming(**timing_dict)


def parse_relationships(rel_dict):
    if not rel_dict:
        return AppointmentRelationships()
    return AppointmentRelationships(**rel_dict)


def parse_pinning(pin_dict):
    if not pin_dict:
        return AppointmentPinning()
    return AppointmentPinning(**pin_dict)


def load_providers(path):
    with open(path, 'r') as f:
        data = yaml.safe_load(f)
    providers = []
    for p in data.get('providers', []):
        provider = Provider(
            id=parse_uuid(p['id']),
            name=p['name'],
            home_location=parse_location(p.get('home_location')),
            languages=p.get('languages', []),
            transportation=p.get('transportation'),
            role=p.get('role'),
            skills=p.get('skills', []),
            availability=parse_provider_availability(p.get('availability')),
            capacity=parse_provider_capacity(p.get('capacity')),
            provider_preferences=parse_provider_preferences(p.get('provider_preferences')),
            # Optional fields
            service_areas=p.get('service_areas', []),
            current_task_count=p.get('current_task_count', 0),
            critical=p.get('critical', False),
            current_availability_status=p.get('current_availability_status', 'AVAILABLE'),
            current_unavailable_until=None,  # Not in demo data
            properties=p.get('properties', {})
        )
        providers.append(provider)
    return providers


def load_consumers(path):
    with open(path, 'r') as f:
        data = yaml.safe_load(f)
    consumers = []
    for c in data.get('consumers', []):
        consumer = Consumer(
            id=parse_uuid(c['id']),
            name=c['name'],
            location=parse_location(c.get('location')),
            care_episode_id=c.get('care_episode_id'),
            consumer_preferences=parse_consumer_preferences(c.get('consumer_preferences')),
            properties=c.get('properties', {})
        )
        consumers.append(consumer)
    return consumers


def load_appointments(path):
    with open(path, 'r') as f:
        data = yaml.safe_load(f)
    appointments = []
    for a in data.get('appointments', []):
        appointment = AppointmentData(
            id=parse_uuid(a['id']),
            consumer_id=parse_uuid(a['consumer_id']),
            appointment_date=parse_date(a['appointment_date']),
            required_skills=a.get('required_skills', []),
            duration_min=a.get('duration_min', 0),
            urgent=a.get('urgent', False),
            active=a.get('active', True),
            status=a.get('status', 'PENDING_TO_ASSIGN'),
            location=parse_location(a.get('location')),
            priority=a.get('priority', 'normal'),
            task_points=a.get('task_points'),
            required_role=a.get('required_role'),
            timing=parse_timing(a.get('timing')),
            relationships=parse_relationships(a.get('relationships')),
            pinning=parse_pinning(a.get('pinning')),
            properties=a.get('properties', {})
        )
        appointments.append(appointment)
    return appointments
