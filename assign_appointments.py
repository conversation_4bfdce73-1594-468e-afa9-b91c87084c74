#!/usr/bin/env python3
"""
AssignAppointment Job - Stage 1 of healthcare scheduling optimization.

This job assigns providers and dates to appointments using the Timefold solver.
It is the first stage of the 2-stage optimization process:
1. Assignment Solver: Assigns providers and dates to appointments (this job)
2. Day Plan Solver: Optimizes timing and routing within a day (DayPlan job)
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))
import time
from datetime import date, timedelta, datetime
from typing import List, Dict, Any, Optional
from uuid import uuid4
import argparse

from constraints import define_constraints
from src.data.data_loader import create_demo_data
from model.domain import (
    AppointmentData, Provider, Consumer
)
from model.planning_models import (
    AppointmentAssignment, AppointmentSchedule, DateRange
)
from constraints.config_registry import ConfigRegistry
from constraints.service_context import ServiceContextManager
from loguru import logger
from timefold.solver import SolverFactory, SolutionManager
from timefold.solver.config import Sol<PERSON><PERSON>onfig, ScoreDirectorFactoryConfig, TerminationConfig, Duration
from constraints.assignment_constraints import define_constraints

# Ensure Provider class is available for solver
_ = Provider  # Force import and make class available

# Configure logging to write to both console and file
def setup_logging():
    """Setup logging configuration for both console and file output."""
    # Remove default handler (console only)
    logger.remove()
    
    # Add console handler with color - INFO level to reduce noise
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO",  # Only show INFO and above in console
        colorize=True
    )
    
    # Add file handler - DEBUG level for detailed logging
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"scheduler_{date.today().strftime('%Y-%m-%d')}.log")
    logger.add(
        log_file,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",  # Keep DEBUG level for file logging
        rotation="1 day",
        retention="30 days",
        compression="zip"
    )
    
    logger.info(f"Logging configured - console (INFO+) and file (DEBUG+): {log_file}")

# Setup logging when module is imported
setup_logging()

class AssignAppointmentJob:
    """Job for assigning providers and dates to appointments."""

    def __init__(self, config_folder: str = "config", daemon_mode: bool = False):
        """Initialize the assignment job."""
        logger.info("Initializing AssignAppointmentJob...")
        
        # Load configurations using the new registry
        logger.info("Loading configurations...")
        ConfigRegistry.load_configurations(config_folder)
        self.scheduler_config = ConfigRegistry.get_core_config('scheduler')
        self.service_configs = ConfigRegistry.get_all_service_configs()
        self.daemon_mode = daemon_mode
        logger.info("Configurations loaded successfully")

        # Initialize solver with proper configuration for large datasets
        # Configuration is now handled automatically by decorators
        logger.info("Creating solver configuration...")
        solver_config = SolverConfig(
            solution_class=AppointmentSchedule,
            entity_class_list=[AppointmentAssignment],  # Provider is not a planning entity
            score_director_factory_config=ScoreDirectorFactoryConfig(
                constraint_provider_function=define_constraints
            ),
            termination_config=TerminationConfig(
                spent_limit=Duration(seconds=5),  # 5 seconds for testing
                unimproved_spent_limit=Duration(seconds=2)  # Stop if no improvement for 2 seconds
            )
        )
        logger.info("Solver configuration created")

        logger.info("Creating solver factory...")
        self.solver_factory = SolverFactory.create(solver_config)
        logger.info("Solver factory created")

        logger.info("Creating solution manager...")
        self.solution_manager = SolutionManager.create(self.solver_factory)
        logger.info("Solution manager created")

        logger.info("AssignAppointment job initialized with solver config")

    def run(self, target_date: Optional[date] = None, service_type: Optional[str] = None) -> Dict[str, Any]:
        """Run the appointment assignment job."""
        start_time = time.time()

        logger.info("Starting assignment job")
        logger.info(f"Target Date: {target_date or 'Today'}")
        logger.info(f"Service Type: {service_type or 'All Services'}")

        try:
            # STAGE 1: Load demo data
            logger.info("Loading data")
            demo_data = create_demo_data()
            providers = demo_data["providers"]
            consumers = demo_data["consumers"]
            appointments = demo_data["appointments"]

            logger.info(
                f"Initial data loaded: {len(providers)} providers, {len(consumers)} consumers, {len(appointments)} appointments")

            # Debug logging for data types
            logger.info(f"Providers type: {type(providers)}")
            if providers:
                logger.info(f"First provider type: {type(providers[0])}")
                logger.info(f"First provider class: {providers[0].__class__.__name__}")
            logger.info(f"Consumers type: {type(consumers)}")
            if consumers:
                logger.info(f"First consumer type: {type(consumers[0])}")
                logger.info(f"First consumer class: {consumers[0].__class__.__name__}")
            logger.info(f"Appointments type: {type(appointments)}")
            if appointments:
                logger.info(f"First appointment type: {type(appointments[0])}")
                logger.info(f"First appointment class: {appointments[0].__class__.__name__}")

            # STAGE 2: Filter appointments by service type
            if service_type:
                logger.info(f"Filtering by Service Type ({service_type})")
                appointments = self._filter_appointments_by_service(appointments, service_type)
                logger.info(f"Filtered to {len(appointments)} appointments for service type: {service_type}")
            else:
                logger.info("No service type filter applied")
                logger.info(f"Using all {len(appointments)} appointments")

            # STAGE 3: Filter appointments by date range
            if target_date:
                logger.info(f"Filtering by Date Range")
                appointments = self._filter_appointments_by_date_range(appointments, target_date)
                logger.info(f"Filtered to {len(appointments)} appointments for date range")
            else:
                logger.info("No date filter applied")
                logger.info(f"Using all {len(appointments)} appointments")

            if not appointments:
                logger.warning("No appointments to assign after filtering")
                result = {
                    "success": True,
                    "message": "No appointments to assign",
                    "assignments": [],
                    "processing_time": time.time() - start_time
                }
                self._handle_completion(result)
                return result

            # STAGE 4: Create planning entities
            logger.info("Creating planning entities")
            planning_assignments = self._create_planning_assignments(appointments)
            logger.info(f"Created {len(planning_assignments)} planning entities")

            # STAGE 5: Create available dates
            logger.info("Creating available dates")
            if target_date:
                # Single target date - create a list with just that date
                available_dates = [target_date]
                logger.info(f"Using target date for assignment: {target_date}")
            else:
                # Create rolling window of available dates
                rolling_window_days = self.scheduler_config.get('rolling_window_days', 7) if self.scheduler_config else 7
                start_date = date.today()
                available_dates = [start_date + timedelta(days=i) for i in range(rolling_window_days)]
                logger.info(f"Using rolling window: {rolling_window_days} days starting from {start_date}")
                logger.info(f"Available dates: {[str(d) for d in available_dates]}")
            
            # STAGE 6: Create solution
            logger.info("Creating solution")
            solution = AppointmentSchedule(
                id=f"schedule_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                providers=providers,
                available_dates=available_dates,
                appointment_assignments=planning_assignments
            )
            logger.info(f"Solution created with {len(planning_assignments)} assignments, {len(available_dates)} available dates")

            # Debug logging to identify conversion issues
            logger.info(f"Solution type: {type(solution)}")
            logger.info(f"Solution class: {solution.__class__.__name__}")
            if solution.appointment_assignments:
                logger.info(f"First assignment type: {type(solution.appointment_assignments[0])}")
                logger.info(f"First assignment class: {solution.appointment_assignments[0].__class__.__name__}")
            if solution.providers:
                logger.info(f"First provider type: {type(solution.providers[0])}")
                logger.info(f"First provider class: {solution.providers[0].__class__.__name__}")

            # STAGE 7: Solve the problem
            logger.info("Starting optimization solver")
            solved_solution = self._solve_assignment_problem(solution, service_type)
            logger.info("Optimization solver completed")

            # STAGE 8: Process results
            logger.info("Processing results")
            results = self._process_assignment_results(solved_solution, consumers, start_time)

            logger.info("Assignment job completed")
            logger.info(f"Total processing time: {results['processing_time']:.2f} seconds")

            self._handle_completion(results)
            return results

        except Exception as e:
            logger.error(f"Error in AssignAppointment job: {e}", exc_info=True)
            result = {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
            self._handle_completion(result)
            return result

    def _filter_appointments_by_service(self, appointments: List[AppointmentData], service_type: str) -> List[
        AppointmentData]:
        """Filter appointments by service type based on required skills."""
        service_config = self.service_configs.get(service_type)
        if not service_config:
            logger.warning(f"No service config found for {service_type}, using all appointments")
            return appointments

        filtered_appointments = []
        for appointment in appointments:
            # Check if appointment requires any of the service skills
            if any(skill in appointment.required_skills for skill in service_config.get('required_skills', [])):
                filtered_appointments.append(appointment)

        return filtered_appointments

    def _filter_appointments_by_date_range(self, appointments: List[AppointmentData], target_date: date) -> List[
        AppointmentData]:
        """Filter appointments by date range starting from target date."""
        logger.info("No date filtering applied - letting constraints handle date assignment")
        return appointments

    def _create_planning_assignments(self, appointments: List[AppointmentData]) -> List[AppointmentAssignment]:
        """Create planning assignments for the solver."""
        assignments = []
        for appointment in appointments:
            assignment = AppointmentAssignment(
                id=str(appointment.id),
                appointment_data=appointment,
                provider=None,  # Will be assigned by solver
                assigned_date=None  # Will be assigned by solver
            )
            assignments.append(assignment)

        return assignments

    def _solve_assignment_problem(self, solution: AppointmentSchedule,
                                  service_type: Optional[str]) -> AppointmentSchedule:
        """Solve the appointment assignment problem using Timefold."""
        logger.info(f"Starting solver with {len(solution.appointment_assignments)} assignments")
        logger.info(f"Using service type for constraints: {service_type or 'None (default)'}")

        # Get service config for constraints
        service_config = None
        if service_type:
            service_config = self.service_configs.get(service_type)
            if service_config:
                logger.info(f"Using service config: {service_type}")
                logger.info(f"   - Required skills: {service_config.get('required_skills', [])}")
                logger.info(f"   - Geographic radius: {service_config.get('geographic_radius_miles', 25.0)} miles")
                logger.info(f"   - Max daily appointments: {service_config.get('max_daily_appointments_per_provider', 8)}")
            else:
                logger.info(f"Service config not found for: {service_type}")
        else:
            logger.info("No specific service type - using default constraints")

        if not service_config:
            # Determine the most common service type from the actual appointments
            service_type_counts = {}
            for assignment in solution.appointment_assignments:
                service_type = self._determine_service_type(assignment.appointment_data)
                service_type_counts[service_type] = service_type_counts.get(service_type, 0) + 1
            
            # Use the most common service type, or fall back to first available
            if service_type_counts:
                most_common_service = None
                max_count = 0
                for service_type, count in service_type_counts.items():
                    if count > max_count:
                        max_count = count
                        most_common_service = service_type
                
                if most_common_service:
                    service_config = self.service_configs.get(most_common_service)
                    logger.info(f"Using most common service config: {most_common_service} ({max_count} appointments)")
                else:
                    service_config = list(self.service_configs.values())[0]
                    logger.info(f"Using default service config: {service_config.get('service_type', 'unknown')}")
            elif self.service_configs:
                service_config = list(self.service_configs.values())[0]
                logger.info(f"Using default service config: {service_config.get('service_type', 'unknown')}")
            else:
                service_config = {
                    'service_type': "default",
                    'required_skills': ["basic_care"],
                    'geographic_radius_miles': 25.0,
                    'max_daily_appointments_per_provider': 8,
                    'max_weekly_hours_per_provider': 40,
                    'continuity_weight': 0.7,
                    'workload_balance_weight': 0.6,
                    'geographic_clustering_weight': 0.4,
                    'patient_preference_weight': 0.7,
                    'capacity_threshold_percentage': 0.9
                }
                logger.info("Created default service config")

        # Create solver
        logger.info("Creating solver instance...")
        solver = self.solver_factory.build_solver()

        # Set solving time limit (use shorter timeout for testing)
        max_solving_time = self.scheduler_config.get('max_solving_time_seconds', 30) if self.scheduler_config else 30
        solving_time_seconds = min(5, max_solving_time)  # Max 5 seconds for testing
        logger.info(f"Solver time limit: {solving_time_seconds} seconds")

        logger.info("Starting optimization process...")
        logger.info("   - Applying constraint rules...")
        logger.info("   - Balancing workload across providers...")
        logger.info("   - Optimizing geographic distribution...")
        logger.info("   - Considering patient preferences...")

        # Use ServiceContextManager to set service type for this solve operation
        with ServiceContextManager(service_type):
            # Solve
            solved_solution = solver.solve(solution)

        logger.info(f"Solver completed successfully!")
        logger.info(f"Final score: {solved_solution.score}")

        return solved_solution

    def _process_assignment_results(self, solution: AppointmentSchedule, consumers: List[Consumer],
                                    start_time: float) -> Dict[str, Any]:
        """Process and format the assignment results."""
        logger.info("Processing assignment results...")
        processing_time = time.time() - start_time

        # Create consumer lookup
        consumer_lookup = {consumer.id: consumer for consumer in consumers}

        # Process assignments
        assignments = []
        assigned_count = 0
        unassigned_count = 0

        logger.info("Analyzing assignment results...")
        for assignment in solution.appointment_assignments:
            consumer = consumer_lookup.get(assignment.appointment_data.consumer_id)
            consumer_name = consumer.name if consumer else "Unknown"

            if assignment.provider is not None and assignment.assigned_date is not None:
                assigned_count += 1

                assignment_result = {
                    "appointment_id": str(assignment.appointment_data.id),
                    "patient_name": consumer_name,
                    "service_type": self._determine_service_type(assignment.appointment_data),
                    "provider_name": assignment.provider.name,
                    "assigned_date": assignment.assigned_date.isoformat(),
                    "visit_type": "Timed" if assignment.appointment_data.timing.is_timed_visit else "Flexible",
                    "duration_minutes": assignment.appointment_data.duration_min,
                    "urgent": assignment.appointment_data.urgent,
                }

                assignments.append(assignment_result)

                logger.info(f"Assigned: {consumer_name} -> {assignment.provider.name} on {assignment.assigned_date}")
                
                # Add detailed constraint validation logging
                self._log_constraint_validation(assignment, consumer_name)

            else:
                unassigned_count += 1
                logger.warning(f"Unassigned: {consumer_name} - No provider or date assigned")
                
                # Add detailed analysis for unassigned appointments
                self._log_unassigned_analysis(assignment, consumer_name, solution)

        # Calculate summary statistics
        total_appointments = len(solution.appointment_assignments)
        assignment_rate = (assigned_count / total_appointments * 100) if total_appointments > 0 else 0

        logger.info("Calculating assignment statistics...")

        # Group by service type
        service_type_stats = {}
        for assignment in assignments:
            service_type = assignment["service_type"]
            if service_type not in service_type_stats:
                service_type_stats[service_type] = {"assigned": 0, "total": 0}
            service_type_stats[service_type]["assigned"] += 1

        # Count total appointments by service type
        for assignment in solution.appointment_assignments:
            service_type = self._determine_service_type(assignment.appointment_data)
            if service_type not in service_type_stats:
                service_type_stats[service_type] = {"assigned": 0, "total": 0}
            service_type_stats[service_type]["total"] += 1

        # Calculate success rates
        for service_type in service_type_stats:
            stats = service_type_stats[service_type]
            stats["success_rate"] = (stats["assigned"] / stats["total"] * 100) if stats["total"] > 0 else 0

        results = {
            "success": True,
            "processing_time": processing_time,
            "summary": {
                "total_appointments": total_appointments,
                "assigned_appointments": assigned_count,
                "unassigned_appointments": unassigned_count,
                "assignment_rate_percent": assignment_rate,
                "final_score": str(solution.score) if solution.score else "N/A"
            },
            "service_type_statistics": service_type_stats,
            "assignments": assignments
        }

        # Log detailed results
        logger.info("=== ASSIGNMENT RESULTS SUMMARY ===")
        logger.info(f"Total appointments: {total_appointments}")
        logger.info(f"Assigned: {assigned_count} ({assignment_rate:.1f}%)")
        logger.info(f"Unassigned: {unassigned_count}")
        logger.info(f"Processing time: {processing_time:.2f} seconds")
        logger.info(f"Final score: {solution.score}")

        logger.info("=== SERVICE TYPE STATISTICS ===")
        for service_type, stats in service_type_stats.items():
            logger.info(f"{service_type}: {stats['assigned']}/{stats['total']} ({stats['success_rate']:.1f}%)")

        return results

    def _determine_service_type(self, appointment: AppointmentData) -> str:
        """Determine service type based on required skills.
        Defaults to 'skilled_nursing' if no match is found.
        """
        for service_type, config in self.service_configs.items():
            if any(skill in appointment.required_skills for skill in config.get('required_skills', [])):
                return service_type

        # Default based on role
        if appointment.required_role == "RN":
            return "skilled_nursing"
        elif appointment.required_role == "CNA":
            return "pcs"
        elif appointment.required_role and ("social" in appointment.required_role.lower() or "counselor" in appointment.required_role.lower() or "psych" in appointment.required_role.lower()):
            return "behavioral_care"
        elif appointment.required_role and ("hospital" in appointment.required_role.lower() or "critical" in appointment.required_role.lower()):
            return "hospital_at_home"
        else:
            return "skilled_nursing"  # Default to skilled_nursing if no match is found

    def _log_constraint_validation(self, assignment: AppointmentAssignment, consumer_name: str):
        """Log detailed constraint validation for an assignment."""
        try:
            from constraints.c001_asgn_provider_skill_validation import validate_appointment_constraints
            
            # Validate all constraints
            validation_results = validate_appointment_constraints(
                assignment.appointment_data, 
                assignment.provider, 
                assignment.assigned_date
            )
            
            # Log constraint validation results
            logger.info(f"  └─ Constraint Validation for {consumer_name}:")
            
            # Hard constraints (must be True)
            hard_constraints = {
                "has_required_skills": validation_results.get("has_required_skills", False),
                "is_working_day": validation_results.get("is_working_day", False),
                "within_service_radius": validation_results.get("within_service_radius", False),
                "role_matches": validation_results.get("role_matches", False),
                "duration_appropriate": validation_results.get("duration_appropriate", False)
            }
            
            # Log hard constraint results
            for constraint_name, is_satisfied in hard_constraints.items():
                status = "✓ SATISFIED" if is_satisfied else "✗ VIOLATED"
                logger.info(f"    ├─ {constraint_name}: {status}")
            
            # Check if all hard constraints are satisfied
            all_hard_satisfied = all(hard_constraints.values())
            if all_hard_satisfied:
                logger.info(f"    └─ All hard constraints satisfied ✓")
            else:
                violated_constraints = [name for name, satisfied in hard_constraints.items() if not satisfied]
                logger.debug(f"    └─ Hard constraint violations: {', '.join(violated_constraints)} ✗")
                
        except Exception as e:
            logger.debug(f"Could not validate constraints for {consumer_name}: {e}")

    def _log_unassigned_analysis(self, assignment: AppointmentAssignment, consumer_name: str, solution: AppointmentSchedule):
        """Log detailed analysis for unassigned appointments."""
        try:
            logger.info(f"  └─ Unassigned Analysis for {consumer_name}:")
            
            # Get the appointment data
            appointment_data = assignment.appointment_data
            
            # Check if we have provider data available
            if hasattr(solution, 'providers') and solution.providers:
                available_providers = solution.providers
                logger.info(f"    ├─ Available providers: {len(available_providers)}")
                
                # Check each provider for potential assignment issues
                for provider in available_providers[:5]:  # Check first 5 providers
                    logger.info(f"    ├─ Checking provider: {provider.name}")
                    
                    # Check skills
                    has_skills = all(skill in provider.skills for skill in appointment_data.required_skills) if provider.skills else False
                    logger.info(f"    │  ├─ Has required skills: {'✓' if has_skills else '✗'}")
                    
                    # Check role
                    role_matches = provider.role == appointment_data.required_role if appointment_data.required_role else True
                    logger.info(f"    │  ├─ Role matches: {'✓' if role_matches else '✗'}")
                    
                    # Check availability (simplified)
                    if hasattr(provider, 'availability') and provider.availability:
                        logger.info(f"    │  ├─ Has availability config: ✓")
                    else:
                        logger.info(f"    │  ├─ Has availability config: ✗")
                    
                    # Check location
                    if provider.home_location and appointment_data.location:
                        logger.info(f"    │  ├─ Has location data: ✓")
                    else:
                        logger.info(f"    │  ├─ Has location data: ✗")
                        
            else:
                logger.info(f"    ├─ No provider data available for analysis")
            
            # Log appointment details
            logger.info(f"    ├─ Appointment details:")
            logger.info(f"    │  ├─ Required skills: {appointment_data.required_skills}")
            logger.info(f"    │  ├─ Required role: {appointment_data.required_role}")
            logger.info(f"    │  ├─ Duration: {appointment_data.duration_min} minutes")
            logger.info(f"    │  ├─ Urgent: {appointment_data.urgent}")
            logger.info(f"    │  ├─ Timed visit: {appointment_data.timing.is_timed_visit if appointment_data.timing else False}")
            
            logger.info(f"    └─ Possible reasons: No suitable provider available or hard constraint violations")
            
        except Exception as e:
            logger.debug(f"Could not analyze unassigned appointment for {consumer_name}: {e}")

    def _handle_completion(self, results: Dict[str, Any]):
        """Handle job completion based on daemon mode."""
        if not self.daemon_mode:
            logger.info("Job completed.")
            # Don't exit when called programmatically (e.g., from API)
            # Only exit when run as main script
        else:
            logger.info("Job completed. Continuing in daemon mode...")


def main(daemon_mode: bool = False):
    """
    Main function to run the assignment job.
    
    Args:
        daemon_mode: If True, the job will not exit after completion (for continuous operation)
    """
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run the AssignAppointment job')
    parser.add_argument('--service-type', '-s', type=str, 
                       help='Service type to filter appointments (e.g., skilled_nursing, behavioral_care)')
    parser.add_argument('--daemon', '-d', action='store_true', 
                       help='Run in daemon mode (continuous operation)')
    parser.add_argument('--target-date', '-t', type=str,
                       help='Target date for appointments (YYYY-MM-DD format)')
    
    args = parser.parse_args()
    
    # Override daemon_mode if specified in command line
    if args.daemon:
        daemon_mode = True
    
    logger.info("Initializing AssignAppointmentJob...")
    job = AssignAppointmentJob(daemon_mode=daemon_mode)

    # Parse target date if provided
    target_date = None
    if args.target_date:
        try:
            target_date = datetime.strptime(args.target_date, '%Y-%m-%d').date()
            logger.info(f"Using specified target date: {target_date}")
        except ValueError:
            logger.error(f"Invalid date format: {args.target_date}. Use YYYY-MM-DD format.")
            sys.exit(1)
    else:
        target_date = date.today()
        logger.info(f"Using today's date: {target_date}")
    
    # Log service type if specified
    if args.service_type:
        logger.info(f"Using service type: {args.service_type}")
    
    results = job.run(target_date=target_date, service_type=args.service_type)

    if results["success"]:
        print("AssignAppointment job completed successfully!")
        if "summary" in results:
            print(
                f"Assigned {results['summary']['assigned_appointments']} out of {results['summary']['total_appointments']} appointments")
    else:
        print(f"AssignAppointment job failed: {results['error']}")

    logger.info("Job completed. Exiting...")
    sys.exit(0 if results["success"] else 1)


if __name__ == "__main__":
    main()
