"""
Assignment stage constraints for healthcare scheduling optimization.

This module contains active constraints for the first stage of optimization:
- Assigning providers and dates to appointments

This file now serves as a coordinator that imports and combines constraints
"""

from timefold.solver.score import constraint_provider, ConstraintFactory
from typing import Optional

# Import grouped constraint modules
from constraints.c001_asgn_provider_skill_validation import provider_skill_validation_constraints
from constraints.c002_asgn_date_based_availability import date_based_availability_constraints
from constraints.c003_asgn_geographic_service_area import geographic_service_area_constraints
from constraints.c004_asgn_timed_visit_date_assignment import timed_visit_date_assignment_constraints
from constraints.c005_asgn_workload_balance_optimization import workload_balance_optimization_constraints
from constraints.c006_asgn_geographic_clustering_optimization import geographic_clustering_optimization_constraints
from constraints.c007_asgn_patient_preference_matching import patient_preference_matching_constraints
from constraints.c008_asgn_provider_capacity_management import provider_capacity_management_constraints
from constraints.c009_asgn_continuity_of_care_optimization import continuity_of_care_optimization_constraints


@constraint_provider
def define_constraints(constraint_factory: ConstraintFactory):
    """Define all active constraints for the assignment stage."""
    # Handle Timefold validation calls where constraint_factory is None
    if constraint_factory is None:
        return []
        
    constraints = []

    # Hard constraints - must be satisfied (always enabled)
    # Enable C001 to prevent invalid assignments
    constraints.extend(provider_skill_validation_constraints(constraint_factory))
    # Enable C002 to ensure provider availability
    constraints.extend(date_based_availability_constraints(constraint_factory))
    # Enable C004 to handle rolling window date assignment
    constraints.extend(timed_visit_date_assignment_constraints(constraint_factory))
    # constraints.extend(geographic_service_area_constraints(constraint_factory))

    # Soft constraints - optimization preferences (always enabled with decorators)
    constraints.extend(workload_balance_optimization_constraints(constraint_factory))
    # constraints.extend(geographic_clustering_optimization_constraints(constraint_factory))
    # constraints.extend(geographic_service_area_constraints(constraint_factory))
    # constraints.extend(continuity_of_care_optimization_constraints(constraint_factory))
    # constraints.extend(patient_preference_matching_constraints(constraint_factory))
    # constraints.extend(provider_capacity_management_constraints(constraint_factory))

    return constraints
