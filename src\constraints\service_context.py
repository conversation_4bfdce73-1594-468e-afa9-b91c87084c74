"""
Service Context Management for Runtime Service Type Selection.

This module provides thread-local storage for service type context,
ensuring that concurrent solver jobs don't interfere with each other's
service type selection.
"""

import threading
from typing import Optional

# Thread-local storage for service type context
_service_type_context = threading.local()

def set_current_service_type(service_type: Optional[str]) -> None:
    """
    Set the current service type for the current thread.
    
    Args:
        service_type: The service type to use (e.g., 'skilled_nursing', 'behavioral_care')
                     or None to use default
    """
    _service_type_context.value = service_type

def get_current_service_type() -> Optional[str]:
    """
    Get the current service type for the current thread.
    
    Returns:
        The current service type or None if not set
    """
    return getattr(_service_type_context, 'value', None)

def clear_current_service_type() -> None:
    """Clear the current service type for the current thread."""
    if hasattr(_service_type_context, 'value'):
        delattr(_service_type_context, 'value')

class ServiceContextManager:
    """
    Context manager for automatically setting and clearing service type.
    
    Usage:
        with ServiceContextManager('skilled_nursing'):
            # Run solver - will use 'skilled_nursing' service type
            solver.solve(solution)
    """
    
    def __init__(self, service_type: Optional[str]):
        self.service_type = service_type
        self.previous_service_type = None
    
    def __enter__(self):
        """Set the service type for this context."""
        self.previous_service_type = get_current_service_type()
        set_current_service_type(self.service_type)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Restore the previous service type."""
        if self.previous_service_type is not None:
            set_current_service_type(self.previous_service_type)
        else:
            clear_current_service_type() 