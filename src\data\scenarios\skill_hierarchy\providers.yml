providers:
  # Perfect Match Providers
  - id: "skill-rn-perfect"
    name: "Dr. <PERSON>, <PERSON><PERSON>"
    role: "RN"
    skills: [ "medication_management", "wound_care", "assessment", "iv_therapy", "diabetes_management" ]
    home_location:
      latitude: 40.7589
      longitude: -73.9851
      city: "New York"
      state: "NY"
      address: "123 5th Avenue, New York, NY 10003"
    service_areas:
      - name: "Manhattan Central"
        zone_type: "service"
        boundary_wkt: "POLYGON((-74.0 40.7, -73.9 40.7, -73.9 40.8, -74.0 40.8, -74.0 40.7))"
    availability:
      working_days: [ "monday", "tuesday", "wednesday", "thursday", "friday" ]
      working_hours: [ "08:00", "17:00" ]
    capacity:
      max_hours_per_day: 8
      max_tasks_count_in_day: 6

  # Skill Hierarchy - RN with LPN/CNA skills
  - id: "skill-rn-hierarchy"
    name: "Dr. <PERSON>, R<PERSON>"
    role: "RN"
    skills: [ "medication_management", "medication_administration", "vital_signs", "personal_care", "assessment" ]
    home_location:
      latitude: 40.7505
      longitude: -73.9934
      city: "New York"
      state: "NY"
      address: "456 7th Avenue, New York, NY 10001"
    service_areas:
      - name: "Manhattan West"
        zone_type: "service"
        boundary_wkt: "POLYGON((-74.1 40.7, -74.0 40.7, -74.0 40.8, -74.1 40.8, -74.1 40.7))"
    availability:
      working_days: [ "monday", "tuesday", "wednesday", "thursday", "friday" ]
      working_hours: [ "09:00", "18:00" ]
    capacity:
      max_hours_per_day: 8
      max_tasks_count_in_day: 5

  # LPN with CNA skills
  - id: "skill-lpn-hierarchy"
    name: "Lisa Thompson, LPN"
    role: "LPN"
    skills: [ "medication_administration", "vital_signs", "personal_care", "mobility_assistance", "basic_care" ]
    home_location:
      latitude: 40.7829
      longitude: -73.9654
      city: "New York"
      state: "NY"
      address: "789 Central Park West, New York, NY 10024"
    service_areas:
      - name: "Manhattan North"
        zone_type: "service"
        boundary_wkt: "POLYGON((-74.0 40.8, -73.9 40.8, -73.9 40.9, -74.0 40.9, -74.0 40.8))"
    availability:
      working_days: [ "monday", "tuesday", "wednesday", "thursday", "friday" ]
      working_hours: [ "07:00", "16:00" ]
    capacity:
      max_hours_per_day: 8
      max_tasks_count_in_day: 4

  # Basic CNA
  - id: "skill-cna-basic"
    name: "Maria Garcia, CNA"
    role: "CNA"
    skills: [ "personal_care", "mobility_assistance", "housekeeping", "meal_assistance" ]
    home_location:
      latitude: 40.7580
      longitude: -73.9855
      city: "New York"
      state: "NY"
      address: "321 Broadway, New York, NY 10007"
    service_areas:
      - name: "Manhattan Central"
        zone_type: "service"
        boundary_wkt: "POLYGON((-74.0 40.7, -73.9 40.7, -73.9 40.8, -74.0 40.8, -74.0 40.7))"
    availability:
      working_days: [ "monday", "tuesday", "wednesday", "thursday", "friday" ]
      working_hours: [ "08:00", "17:00" ]
    capacity:
      max_hours_per_day: 8
      max_tasks_count_in_day: 3

  # Edge Case: Empty skills list
  - id: "skill-empty"
    name: "John Doe, RN"
    role: "RN"
    skills: [ ]
    home_location:
      latitude: 40.7589
      longitude: -73.9851
      city: "New York"
      state: "NY"
      address: "123 5th Avenue, New York, NY 10003"
    service_areas:
      - name: "Manhattan Central"
        zone_type: "service"
        boundary_wkt: "POLYGON((-74.0 40.7, -73.9 40.7, -73.9 40.8, -74.0 40.8, -74.0 40.7))"
    availability:
      working_days: [ "monday", "tuesday", "wednesday", "thursday", "friday" ]
      working_hours: [ "08:00", "17:00" ]
    capacity:
      max_hours_per_day: 8
      max_tasks_count_in_day: 6

  # Edge Case: Case sensitivity
  - id: "skill-case-sensitive"
    name: "Jane Smith, RN"
    role: "RN"
    skills: [ "Medication_Management", "WOUND_CARE", "Assessment", "IV_Therapy" ]
    home_location:
      latitude: 40.7505
      longitude: -73.9934
      city: "New York"
      state: "NY"
      address: "456 7th Avenue, New York, NY 10001"
    service_areas:
      - name: "Manhattan West"
        zone_type: "service"
        boundary_wkt: "POLYGON((-74.1 40.7, -74.0 40.7, -74.0 40.8, -74.1 40.8, -74.1 40.7))"
    availability:
      working_days: [ "monday", "tuesday", "wednesday", "thursday", "friday" ]
      working_hours: [ "09:00", "18:00" ]
    capacity:
      max_hours_per_day: 8
      max_tasks_count_in_day: 5

  # Edge Case: Duplicate skills
  - id: "skill-duplicate"
    name: "Robert Wilson, LPN"
    role: "LPN"
    skills: [ "medication_administration", "medication_administration", "vital_signs", "vital_signs", "basic_care" ]
    home_location:
      latitude: 40.7829
      longitude: -73.9654
      city: "New York"
      state: "NY"
      address: "789 Central Park West, New York, NY 10024"
    service_areas:
      - name: "Manhattan North"
        zone_type: "service"
        boundary_wkt: "POLYGON((-74.0 40.8, -73.9 40.8, -73.9 40.9, -74.0 40.9, -74.0 40.8))"
    availability:
      working_days: [ "monday", "tuesday", "wednesday", "thursday", "friday" ]
      working_hours: [ "07:00", "16:00" ]
    capacity:
      max_hours_per_day: 8
      max_tasks_count_in_day: 4

  # Edge Case: Non-existent skills
  - id: "skill-nonexistent"
    name: "Patricia O'Connor, CNA"
    role: "CNA"
    skills: [ "personal_care", "nonexistent_skill", "invalid_skill_123", "mobility_assistance" ]
    home_location:
      latitude: 40.7580
      longitude: -73.9855
      city: "New York"
      state: "NY"
      address: "321 Broadway, New York, NY 10007"
    service_areas:
      - name: "Manhattan Central"
        zone_type: "service"
        boundary_wkt: "POLYGON((-74.0 40.7, -73.9 40.7, -73.9 40.8, -74.0 40.8, -74.0 40.7))"
    availability:
      working_days: [ "monday", "tuesday", "wednesday", "thursday", "friday" ]
      working_hours: [ "08:00", "17:00" ]
    capacity:
      max_hours_per_day: 8
      max_tasks_count_in_day: 3 