# Patient Preferences Scenario

**Purpose**: Demonstrate patient preference matching optimization
**Best for**: Showing how the system respects patient preferences
**Complexity**: Medium

## Features Demonstrated

- Patient preference matching
- Language preferences
- Gender preferences
- Provider preferences
- Cultural considerations

## Data Overview

- **Providers**: 5 (with diverse backgrounds and languages)
- **Patients**: 8 (with specific preferences)
- **Appointments**: 10
- **Preference Types**: Language, gender, cultural, provider-specific

## Preference Categories

1. **Language Preferences**: Spanish, English, Mandarin
2. **Gender Preferences**: Male providers, Female providers
3. **Cultural Preferences**: Cultural sensitivity requirements
4. **Provider Preferences**: Specific provider requests

## Usage

```bash
# Copy this scenario to main data folder
cp -r data/scenarios/patient_preferences/* data/

# Enable patient preferences
# Edit config/scheduler.yml: enable_patient_preferences: true

# Run assignment job
python -m src.appointment_scheduler.jobs.assign_appointments

# Run day plan job  
python -m src.appointment_scheduler.jobs.day_plan
```

## Expected Results

- Patients should be matched with providers who meet their preferences
- Language preferences should be respected
- Gender preferences should be considered
- Cultural considerations should be factored in
- Provider preferences should be prioritized when possible 