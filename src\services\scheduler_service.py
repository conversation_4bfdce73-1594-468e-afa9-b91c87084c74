"""
Scheduler Service - Combined Service and Event-Driven Architecture.

This service demonstrates the combined approach by providing:
1. Synchronous operations for immediate responses
2. Asynchronous event processing for background optimization
3. Integration with external CareAXL services
4. RabbitMQ integration for production event processing
"""

import asyncio
import threading
from typing import Dict, List, Optional, Any
from datetime import date, datetime
from loguru import logger
from dataclasses import dataclass

from api.service_clients import (
    ServiceClientFactory, ServiceConfig, ServiceType,
    MockStaffServiceClient, MockPatientServiceClient, MockAppointmentServiceClient
)
from events.rabbitmq_integration import (
    RabbitMQConfig, initialize_rabbitmq, get_rabbitmq_event_bus,
    publish_event_rabbitmq, EventType, EventPriority, Event
)
from data.data_loader import create_demo_data
from assign_appointments import AssignAppointmentJob
from day_plan import DayPlanJob


@dataclass
class SchedulerRequest:
    """Request structure for scheduler operations."""
    operation: str
    data: Dict[str, Any]
    service_type: Optional[ServiceType] = None
    target_date: Optional[date] = None
    correlation_id: Optional[str] = None


@dataclass
class SchedulerResponse:
    """Response structure for scheduler operations."""
    success: bool
    data: Dict[str, Any]
    message: str
    correlation_id: Optional[str] = None
    processing_time: float = 0.0


class OptimizationEventHandler:
    """Event handler for schedule optimization events."""
    
    def __init__(self):
        self.assign_job = AssignAppointmentJob()
        self.day_plan_job = DayPlanJob()
        self.logger = logger.bind(component="OptimizationEventHandler")
    
    def can_handle(self, event: Event) -> bool:
        """Check if this handler can handle optimization events."""
        return event.type in [
            EventType.SCHEDULE_OPTIMIZATION_REQUESTED,
            EventType.APPOINTMENT_CREATED,
            EventType.APPOINTMENT_UPDATED,
            EventType.PROVIDER_AVAILABILITY_CHANGED
        ]
    
    async def handle(self, event: Event) -> bool:
        """Handle optimization events."""
        try:
            if event.type == EventType.SCHEDULE_OPTIMIZATION_REQUESTED:
                return await self._handle_optimization_request(event)
            elif event.type == EventType.APPOINTMENT_CREATED:
                return await self._handle_appointment_created(event)
            elif event.type == EventType.APPOINTMENT_UPDATED:
                return await self._handle_appointment_updated(event)
            elif event.type == EventType.PROVIDER_AVAILABILITY_CHANGED:
                return await self._handle_provider_availability_changed(event)
            else:
                self.logger.warning(f"Unhandled event type: {event.type}")
                return False
        except Exception as e:
            self.logger.error(f"Error handling event {event.id}: {e}")
            return False
    
    async def _handle_optimization_request(self, event: Event) -> bool:
        """Handle schedule optimization request."""
        self.logger.info(f"Processing optimization request for event {event.id}")
        
        data = event.data
        service_type = data.get('service_type')
        target_date = data.get('target_date')
        
        try:
            # Run assignment optimization
            assign_results = self.assign_job.run(
                target_date=target_date,
                service_type=service_type
            )
            
            if assign_results['success']:
                # Publish optimization completed event to RabbitMQ
                await publish_event_rabbitmq(
                    EventType.SCHEDULE_OPTIMIZATION_COMPLETED,
                    {
                        'optimization_type': 'assignment',
                        'results': assign_results,
                        'service_type': service_type,
                        'target_date': target_date
                    },
                    priority=EventPriority.HIGH,
                    correlation_id=event.correlation_id
                )
                
                return True
            else:
                # Publish optimization failed event to RabbitMQ
                await publish_event_rabbitmq(
                    EventType.SCHEDULE_OPTIMIZATION_FAILED,
                    {
                        'optimization_type': 'assignment',
                        'error': assign_results.get('error', 'Unknown error'),
                        'service_type': service_type,
                        'target_date': target_date
                    },
                    priority=EventPriority.HIGH,
                    correlation_id=event.correlation_id
                )
                return False
                
        except Exception as e:
            self.logger.error(f"Optimization failed: {e}")
            await publish_event_rabbitmq(
                EventType.SCHEDULE_OPTIMIZATION_FAILED,
                {
                    'optimization_type': 'assignment',
                    'error': str(e),
                    'service_type': service_type,
                    'target_date': target_date
                },
                priority=EventPriority.HIGH,
                correlation_id=event.correlation_id
            )
            return False
    
    async def _handle_appointment_created(self, event: Event) -> bool:
        """Handle appointment created event."""
        self.logger.info(f"Processing appointment created event {event.id}")
        
        # Trigger optimization for the affected date
        data = event.data
        appointment_date = data.get('appointment_date')
        
        if appointment_date:
            await publish_event_rabbitmq(
                EventType.SCHEDULE_OPTIMIZATION_REQUESTED,
                {
                    'optimization_type': 'assignment',
                    'trigger': 'appointment_created',
                    'appointment_id': data.get('appointment_id'),
                    'target_date': appointment_date,
                    'service_type': data.get('service_type')
                },
                priority=EventPriority.NORMAL,
                correlation_id=event.correlation_id
            )
        
        return True
    
    async def _handle_appointment_updated(self, event: Event) -> bool:
        """Handle appointment updated event."""
        self.logger.info(f"Processing appointment updated event {event.id}")
        
        # Trigger optimization for the affected date
        data = event.data
        appointment_date = data.get('appointment_date')
        
        if appointment_date:
            await publish_event_rabbitmq(
                EventType.SCHEDULE_OPTIMIZATION_REQUESTED,
                {
                    'optimization_type': 'assignment',
                    'trigger': 'appointment_updated',
                    'appointment_id': data.get('appointment_id'),
                    'target_date': appointment_date,
                    'service_type': data.get('service_type')
                },
                priority=EventPriority.NORMAL,
                correlation_id=event.correlation_id
            )
        
        return True
    
    async def _handle_provider_availability_changed(self, event: Event) -> bool:
        """Handle provider availability changed event."""
        self.logger.info(f"Processing provider availability changed event {event.id}")
        
        # Trigger optimization for affected dates
        data = event.data
        affected_dates = data.get('affected_dates', [])
        
        for affected_date in affected_dates:
            await publish_event_rabbitmq(
                EventType.SCHEDULE_OPTIMIZATION_REQUESTED,
                {
                    'optimization_type': 'assignment',
                    'trigger': 'provider_availability_changed',
                    'provider_id': data.get('provider_id'),
                    'target_date': affected_date,
                    'service_type': data.get('service_type')
                },
                priority=EventPriority.NORMAL,
                correlation_id=event.correlation_id
            )
        
        return True


class NotificationEventHandler:
    """Event handler for notification events."""
    
    def __init__(self):
        self.logger = logger.bind(component="NotificationEventHandler")
    
    def can_handle(self, event: Event) -> bool:
        """Check if this handler can handle notification events."""
        return event.type in [
            EventType.NOTIFICATION_SEND_REQUESTED,
            EventType.APPOINTMENT_ASSIGNED,
            EventType.SCHEDULE_OPTIMIZATION_COMPLETED
        ]
    
    async def handle(self, event: Event) -> bool:
        """Handle notification events."""
        try:
            if event.type == EventType.NOTIFICATION_SEND_REQUESTED:
                return await self._handle_notification_request(event)
            elif event.type == EventType.APPOINTMENT_ASSIGNED:
                return await self._handle_appointment_assigned(event)
            elif event.type == EventType.SCHEDULE_OPTIMIZATION_COMPLETED:
                return await self._handle_optimization_completed(event)
            else:
                self.logger.warning(f"Unhandled event type: {event.type}")
                return False
        except Exception as e:
            self.logger.error(f"Error handling event {event.id}: {e}")
            return False
    
    async def _handle_notification_request(self, event: Event) -> bool:
        """Handle notification request."""
        self.logger.info(f"Processing notification request for event {event.id}")
        
        data = event.data
        notification_type = data.get('type')
        recipient = data.get('recipient')
        message = data.get('message')
        
        # In a real implementation, this would integrate with notification services
        self.logger.info(f"Sending {notification_type} notification to {recipient}: {message}")
        
        # Publish notification sent event
        await publish_event_rabbitmq(
            EventType.NOTIFICATION_SENT,
            {
                'notification_type': notification_type,
                'recipient': recipient,
                'message': message,
                'sent_at': datetime.now().isoformat()
            },
            priority=EventPriority.LOW,
            correlation_id=event.correlation_id
        )
        
        return True
    
    async def _handle_appointment_assigned(self, event: Event) -> bool:
        """Handle appointment assigned notification."""
        self.logger.info(f"Processing appointment assigned notification for event {event.id}")
        
        data = event.data
        appointment_id = data.get('appointment_id')
        patient_id = data.get('patient_id')
        provider_id = data.get('provider_id')
        appointment_date = data.get('appointment_date')
        
        # Send notification to patient
        await publish_event_rabbitmq(
            EventType.NOTIFICATION_SEND_REQUESTED,
            {
                'type': 'appointment_assigned',
                'recipient': f'patient_{patient_id}',
                'message': f'Your appointment has been scheduled for {appointment_date}',
                'appointment_id': appointment_id,
                'provider_id': provider_id
            },
            priority=EventPriority.NORMAL,
            correlation_id=event.correlation_id
        )
        
        # Send notification to provider
        await publish_event_rabbitmq(
            EventType.NOTIFICATION_SEND_REQUESTED,
            {
                'type': 'appointment_assigned',
                'recipient': f'provider_{provider_id}',
                'message': f'New appointment assigned for {appointment_date}',
                'appointment_id': appointment_id,
                'patient_id': patient_id
            },
            priority=EventPriority.NORMAL,
            correlation_id=event.correlation_id
        )
        
        return True
    
    async def _handle_optimization_completed(self, event: Event) -> bool:
        """Handle optimization completed notification."""
        self.logger.info(f"Processing optimization completed notification for event {event.id}")
        
        data = event.data
        optimization_type = data.get('optimization_type')
        service_type = data.get('service_type')
        target_date = data.get('target_date')
        
        # Send notification to administrators
        await publish_event_rabbitmq(
            EventType.NOTIFICATION_SEND_REQUESTED,
            {
                'type': 'optimization_completed',
                'recipient': 'administrators',
                'message': f'{optimization_type} optimization completed for {service_type} on {target_date}',
                'optimization_type': optimization_type,
                'service_type': service_type,
                'target_date': target_date
            },
            priority=EventPriority.LOW,
            correlation_id=event.correlation_id
        )
        
        return True


class SchedulerService:
    """Main scheduler service combining synchronous operations and event processing."""
    
    def __init__(self, use_mock_services: bool = True, use_rabbitmq: bool = True):
        """Initialize the scheduler service."""
        self.use_mock_services = use_mock_services
        self.use_rabbitmq = use_rabbitmq
        
        # Initialize service clients
        if use_mock_services:
            self._init_mock_services()
        else:
            self._init_real_services()
        
        # Initialize RabbitMQ if enabled
        if use_rabbitmq:
            self._init_rabbitmq()
        
        # Register event handlers
        self._register_event_handlers()
        
        # Initialize optimization jobs
        self.assign_job = AssignAppointmentJob()
        self.day_plan_job = DayPlanJob()
        
        logger.info("SchedulerService initialized successfully")
    
    def _init_rabbitmq(self):
        """Initialize RabbitMQ connection and event bus."""
        try:
            # Initialize RabbitMQ with default config
            config = RabbitMQConfig()
            initialize_rabbitmq(config)
            self.rabbitmq_event_bus = asyncio.run(get_rabbitmq_event_bus())
            logger.info("RabbitMQ initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize RabbitMQ: {e}")
            self.use_rabbitmq = False
    
    def _init_mock_services(self):
        """Initialize mock service clients for development."""
        demo_data = create_demo_data()
        self.staff_client = MockStaffServiceClient(demo_data)
        self.patient_client = MockPatientServiceClient(demo_data)
        self.appointment_client = MockAppointmentServiceClient(demo_data)
        logger.info("Mock service clients initialized")
    
    def _init_real_services(self):
        """Initialize real service clients for production."""
        # Configure service endpoints
        staff_config = ServiceConfig(
            base_url="http://staff-service:8080",
            api_key="your-api-key",
            timeout=30,
            retry_attempts=3
        )
        patient_config = ServiceConfig(
            base_url="http://patient-service:8080",
            api_key="your-api-key",
            timeout=30,
            retry_attempts=3
        )
        appointment_config = ServiceConfig(
            base_url="http://appointment-service:8080",
            api_key="your-api-key",
            timeout=30,
            retry_attempts=3
        )
        
        # Create service clients using factory
        config = {
            'staff_service': staff_config,
            'patient_service': patient_config,
            'appointment_service': appointment_config
        }
        factory = ServiceClientFactory(config)
        
        self.staff_client = factory.get_staff_client()
        self.patient_client = factory.get_patient_client()
        self.appointment_client = factory.get_appointment_client()
        logger.info("Real service clients initialized")
    
    def _register_event_handlers(self):
        """Register event handlers with RabbitMQ event bus."""
        if not self.use_rabbitmq:
            return
        
        try:
            # Register optimization event handler
            optimization_handler = OptimizationEventHandler()
            # Note: MockRabbitMQEventBus uses subscribe method, not register_handler
            # This would be implemented in the actual RabbitMQ integration
            logger.info("Event handlers would be registered in production RabbitMQ")
        except Exception as e:
            logger.error(f"Failed to register event handlers: {e}")
    
    async def _publish_event(self, event_type: EventType, data: Dict[str, Any],
                           priority: EventPriority = EventPriority.NORMAL,
                           correlation_id: Optional[str] = None):
        """Publish event to RabbitMQ."""
        if not self.use_rabbitmq:
            logger.warning("RabbitMQ not enabled, skipping event publication")
            return
        
        try:
            await publish_event_rabbitmq(event_type, data, priority, "scheduler", correlation_id)
            logger.debug(f"Event published: {event_type}")
        except Exception as e:
            logger.error(f"Failed to publish event {event_type}: {e}")
    
    def process_request(self, request: SchedulerRequest) -> SchedulerResponse:
        """Process a scheduler request."""
        start_time = datetime.now()
        
        try:
            logger.info(f"Processing request: {request.operation}")
            
            # Handle different operation types
            if request.operation == "get_providers":
                response = self._handle_get_providers(request)
            elif request.operation == "get_patients":
                response = self._handle_get_patients(request)
            elif request.operation == "get_appointments":
                response = self._handle_get_appointments(request)
            elif request.operation == "create_appointment":
                response = self._handle_create_appointment(request)
            elif request.operation == "optimize_schedule":
                response = self._handle_optimize_schedule(request)
            elif request.operation == "get_schedule":
                response = self._handle_get_schedule(request)
            else:
                response = SchedulerResponse(
                    success=False,
                    data={},
                    message=f"Unknown operation: {request.operation}",
                    correlation_id=request.correlation_id
                )
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            response.processing_time = processing_time
            
            logger.info(f"Request completed in {processing_time:.3f}s: {response.success}")
            return response
            
        except Exception as e:
            logger.error(f"Error processing request: {e}")
            processing_time = (datetime.now() - start_time).total_seconds()
            return SchedulerResponse(
                success=False,
                data={},
                message=f"Internal error: {str(e)}",
                correlation_id=request.correlation_id,
                processing_time=processing_time
            )
    
    def _handle_get_providers(self, request: SchedulerRequest) -> SchedulerResponse:
        """Handle get providers request."""
        try:
            providers = self.staff_client.get_providers()
            return SchedulerResponse(
                success=True,
                data={'providers': providers},
                message="Providers retrieved successfully",
                correlation_id=request.correlation_id
            )
        except Exception as e:
            return SchedulerResponse(
                success=False,
                data={},
                message=f"Failed to get providers: {str(e)}",
                correlation_id=request.correlation_id
            )
    
    def _handle_get_patients(self, request: SchedulerRequest) -> SchedulerResponse:
        """Handle get patients request."""
        try:
            patients = self.patient_client.get_patients()
            return SchedulerResponse(
                success=True,
                data={'patients': patients},
                message="Patients retrieved successfully",
                correlation_id=request.correlation_id
            )
        except Exception as e:
            return SchedulerResponse(
                success=False,
                data={},
                message=f"Failed to get patients: {str(e)}",
                correlation_id=request.correlation_id
            )
    
    def _handle_get_appointments(self, request: SchedulerRequest) -> SchedulerResponse:
        """Handle get appointments request."""
        try:
            appointments = self.appointment_client.get_appointments()
            return SchedulerResponse(
                success=True,
                data={'appointments': appointments},
                message="Appointments retrieved successfully",
                correlation_id=request.correlation_id
            )
        except Exception as e:
            return SchedulerResponse(
                success=False,
                data={},
                message=f"Failed to get appointments: {str(e)}",
                correlation_id=request.correlation_id
            )
    
    def _handle_create_appointment(self, request: SchedulerRequest) -> SchedulerResponse:
        """Handle create appointment request."""
        try:
            # Create appointment via service
            appointment_data = request.data
            appointment = self.appointment_client.create_appointment(appointment_data)
            
            # Publish appointment created event (handle async in sync context)
            try:
                asyncio.run(self._publish_event(
                    EventType.APPOINTMENT_CREATED,
                    {
                        'appointment_id': appointment.get('id'),
                        'appointment_date': appointment.get('date'),
                        'service_type': request.service_type,
                        'patient_id': appointment.get('patient_id'),
                        'provider_id': appointment.get('provider_id')
                    },
                    priority=EventPriority.NORMAL,
                    correlation_id=request.correlation_id
                ))
            except Exception as e:
                logger.warning(f"Failed to publish appointment created event: {e}")
            
            return SchedulerResponse(
                success=True,
                data={'appointment': appointment},
                message="Appointment created successfully",
                correlation_id=request.correlation_id
            )
        except Exception as e:
            return SchedulerResponse(
                success=False,
                data={},
                message=f"Failed to create appointment: {str(e)}",
                correlation_id=request.correlation_id
            )
    
    def _handle_optimize_schedule(self, request: SchedulerRequest) -> SchedulerResponse:
        """Handle optimize schedule request."""
        try:
            # Run optimization
            service_type_str = request.service_type.value if request.service_type else None
            results = self.assign_job.run(
                target_date=request.target_date,
                service_type=service_type_str
            )
            
            # Publish optimization requested event (handle async in sync context)
            try:
                asyncio.run(self._publish_event(
                    EventType.SCHEDULE_OPTIMIZATION_REQUESTED,
                    {
                        'optimization_type': 'assignment',
                        'service_type': service_type_str,
                        'target_date': request.target_date
                    },
                    priority=EventPriority.HIGH,
                    correlation_id=request.correlation_id
                ))
            except Exception as e:
                logger.warning(f"Failed to publish optimization requested event: {e}")
            
            return SchedulerResponse(
                success=results['success'],
                data=results,
                message="Schedule optimization completed",
                correlation_id=request.correlation_id
            )
        except Exception as e:
            return SchedulerResponse(
                success=False,
                data={},
                message=f"Failed to optimize schedule: {str(e)}",
                correlation_id=request.correlation_id
            )
    
    def _handle_get_schedule(self, request: SchedulerRequest) -> SchedulerResponse:
        """Handle get schedule request."""
        try:
            # Run day plan optimization
            results = self.day_plan_job.run(
                target_date=request.target_date
            )
            
            # Convert BatchAssignmentResult to dictionary format
            results_dict = {
                'success': results.assigned_appointments > 0,  # Consider successful if any appointments assigned
                'batch_id': results.batch_id,
                'total_appointments': results.total_appointments,
                'assigned_appointments': results.assigned_appointments,
                'unassigned_appointments': results.unassigned_appointments,
                'average_score': results.average_score,
                'processing_time_seconds': results.processing_time_seconds,
                'results': [result.__dict__ for result in results.results] if results.results else []
            }
            
            return SchedulerResponse(
                success=results_dict['success'],
                data=results_dict,
                message="Schedule retrieved successfully",
                correlation_id=request.correlation_id
            )
        except Exception as e:
            return SchedulerResponse(
                success=False,
                data={},
                message=f"Failed to get schedule: {str(e)}",
                correlation_id=request.correlation_id
            ) 