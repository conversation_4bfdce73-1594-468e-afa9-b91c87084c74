"""
Main entry point for the CAXL Scheduling Engine FastAPI application.

This module serves as the primary entry point for the Docker container
and provides the FastAPI app instance that uvicorn will serve.
"""

from caxl_scheduling_engine.api.app import create_app

# Create the FastAPI application instance
app = create_app()

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8080)
