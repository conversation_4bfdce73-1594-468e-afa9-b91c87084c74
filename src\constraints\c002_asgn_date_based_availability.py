"""
Date-Based Availability Constraints for Assignment (C002)

This module provides date-based availability constraints for the assignment stage.
Using simplified constraint patterns to avoid Timefold API issues.
"""

from datetime import date, time
from typing import Optional

from timefold.solver.score import ConstraintFactory, HardSoftScore, Constraint
from loguru import logger

from constraints.base_constraints import with_config
from model.planning_models import AppointmentAssignment
from model.domain import Provider, MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY, weekday_from_int

DATE_AVAILABILITY = "dateAvailability"
BLACKOUT_PERIOD = "blackoutPeriod"
HOLIDAY_VIOLATION = "holidayViolation"
WEEKEND_COVERAGE = "weekendCoverage"


@with_config('behavioral_care', return_type='list')
def date_based_availability_constraints(factory: ConstraintFactory, **kwargs):
    """Return date-based availability constraints."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return []
    # Test individual constraints one by one
    return [
        date_availability(factory),
        blackout_period(factory),
        holiday_violation(factory),
        weekend_coverage(factory),
    ]


# --- Hard constraints ---
@with_config('behavioral_care', return_type='constraint')
def date_availability(factory: ConstraintFactory, core_config=None, service_config=None, combined_config=None, service_type: Optional[str] = None, **kwargs) -> Constraint:
    """Providers must be available on the assigned date."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (
        factory.for_each(AppointmentAssignment)
        .filter(lambda assignment: (
            assignment.provider is not None and
            assignment.assigned_date is not None and
            not _is_provider_available_on_date_safe(assignment.provider, assignment.assigned_date)))
        .penalize(HardSoftScore.ONE_HARD, lambda assignment: 
            _log_provider_unavailable(assignment))
        .as_constraint(DATE_AVAILABILITY)
    )


def _log_provider_unavailable(assignment: AppointmentAssignment) -> int:
    """Log provider unavailability and return penalty."""
    provider_name = assignment.provider.name if assignment.provider is not None else "None"
    logger.debug("Provider {} unavailable on {} for appointment {}", 
               provider_name, assignment.assigned_date, assignment.appointment_data.id)
    return 1


def _is_provider_available_on_date_safe(provider: Provider, target_date: date) -> bool:
    """Safe version of provider availability check with error handling."""
    try:
        return _is_provider_available_on_date(provider, target_date)
    except Exception as e:
        provider_name = provider.name if provider is not None else "None"
        logger.error("Error checking provider availability for {} on {}: {}", 
                    provider_name, target_date, str(e))
        return True  # Default to available on error


@with_config('behavioral_care', return_type='constraint')
def blackout_period(factory: ConstraintFactory, core_config=None, service_config=None, combined_config=None, service_type: Optional[str] = None, **kwargs) -> Constraint:
    """Providers cannot be assigned during blackout periods."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (
        factory.for_each(AppointmentAssignment)
        .filter(lambda assignment: (
            assignment.provider is not None and
            assignment.assigned_date is not None and
            _is_in_blackout_period(assignment.assigned_date)))
        .penalize(HardSoftScore.ONE_HARD, lambda assignment: 
            _log_blackout_violation(assignment))
        .as_constraint(BLACKOUT_PERIOD)
    )


def _log_blackout_violation(assignment: AppointmentAssignment) -> int:
    """Log blackout period violation and return penalty."""
    provider_name = assignment.provider.name if assignment.provider is not None else "None"
    logger.debug("Assignment during blackout period: {} on {} for appointment {}", 
               provider_name, assignment.assigned_date, assignment.appointment_data.id)
    return 1


@with_config('behavioral_care', return_type='constraint')
def holiday_violation(factory: ConstraintFactory, core_config=None, service_config=None, combined_config=None, service_type: Optional[str] = None, **kwargs) -> Constraint:
    """Providers cannot be assigned on holidays unless configured."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (
        factory.for_each(AppointmentAssignment)
        .filter(lambda assignment: (
            assignment.assigned_date is not None and
            _is_holiday(assignment.assigned_date)))
        .penalize(HardSoftScore.ONE_HARD, lambda assignment: 1)
        .as_constraint(HOLIDAY_VIOLATION)
    )


# --- Soft constraints ---
@with_config('behavioral_care', return_type='constraint')
def weekend_coverage(factory: ConstraintFactory, core_config=None, service_config=None, combined_config=None, service_type: Optional[str] = None, **kwargs) -> Constraint:
    """Penalize weekend assignments unless weekend coverage is enabled."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (
        factory.for_each(AppointmentAssignment)
        .filter(lambda assignment: (
            assignment.assigned_date is not None and
            _is_weekend(assignment.assigned_date) and
            not _is_weekend_coverage_enabled()))
        .penalize(HardSoftScore.ONE_SOFT, lambda assignment: 
            _log_weekend_penalty(assignment))
        .as_constraint(WEEKEND_COVERAGE)
    )


def _log_weekend_penalty(assignment: AppointmentAssignment) -> int:
    """Log weekend assignment penalty and return penalty value."""
    provider_name = assignment.provider.name if assignment.provider is not None else "None"
    penalty = _get_weekend_penalty()
    logger.debug("Weekend assignment penalty: {} on {} for appointment {} (penalty: {})", 
               provider_name, assignment.assigned_date, assignment.appointment_data.id, penalty)
    return penalty


# --- Helper functions ---
def _is_provider_available_on_date(provider: Provider, target_date: date) -> bool:
    """Check if provider is available on the given date."""
    if provider.availability is None:
        return True  # Default to available if no availability defined
    
    # Check if date is in time off periods (if they exist)
    if hasattr(provider.availability, 'time_off_periods') and provider.availability.time_off_periods:
        for start_date, end_date in provider.availability.time_off_periods:
            if start_date <= target_date <= end_date:
                return False
    
    # Check if date is in holidays (if they exist)
    if hasattr(provider.availability, 'holidays') and provider.availability.holidays:
        if target_date in provider.availability.holidays:
            return False
    
    # Check date-specific availability (if it exists)
    if hasattr(provider.availability, 'date_specific_availability') and provider.availability.date_specific_availability:
        for date_availability in provider.availability.date_specific_availability:
            if date_availability.date == target_date:
                return not date_availability.is_full_day_off
    
    # Check working days (this should always exist)
    if hasattr(provider.availability, 'working_days') and provider.availability.working_days:
        weekday = weekday_from_int(target_date.weekday())
        return weekday in provider.availability.working_days
    
    # If no working days defined, assume available
    return True


def _is_in_blackout_period(target_date: date) -> bool:
    """Check if date is in a blackout period using service configuration."""
    try:
        from constraints.base_constraints import ConfigRegistry
        service_config = ConfigRegistry.get_current_service_config()
        if not service_config or 'service_availability' not in service_config:
            return False
        
        blackout_periods = service_config['service_availability'].get('blackout_periods', [])
        for period in blackout_periods:
            start_date_str = period.get('start_date')
            end_date_str = period.get('end_date')
            if start_date_str and end_date_str:
                # Convert MM-DD format to date objects for current year
                start_date = date(target_date.year, int(start_date_str.split('-')[0]), int(start_date_str.split('-')[1]))
                end_date = date(target_date.year, int(end_date_str.split('-')[0]), int(end_date_str.split('-')[1]))
                if start_date <= target_date <= end_date:
                    return True
        return False
    except Exception:
        # If any error occurs (including missing service context), return False
        return False


def _is_holiday(target_date: date) -> bool:
    """Check if date is a holiday using service configuration."""
    try:
        from constraints.base_constraints import ConfigRegistry
        service_config = ConfigRegistry.get_current_service_config()
        if not service_config or 'service_availability' not in service_config:
            return False
            
        holidays = service_config['service_availability'].get('holidays', [])
        # For now, return False as holidays are not configured
        return False
    except Exception:
        # If any error occurs (including missing service context), return False
        return False


def _is_weekend(target_date: date) -> bool:
    """Check if date is a weekend."""
    return target_date.weekday() >= 5  # Saturday = 5, Sunday = 6


def _is_weekend_coverage_enabled() -> bool:
    """Check if weekend coverage is enabled using service configuration."""
    try:
        from constraints.base_constraints import ConfigRegistry
        service_config = ConfigRegistry.get_current_service_config()
        if not service_config or 'service_availability' not in service_config:
            return False
            
        weekend_config = service_config['service_availability'].get('weekend_coverage', {})
        return weekend_config.get('enabled', False)
    except Exception:
        # If any error occurs (including missing service context), return False
        return False


def _get_weekend_penalty() -> int:
    """Get penalty for weekend assignments using service configuration."""
    try:
        from constraints.base_constraints import ConfigRegistry
        service_config = ConfigRegistry.get_current_service_config()
        if not service_config or 'service_availability' not in service_config:
            return 2  # Default penalty
            
        weekend_config = service_config['service_availability'].get('weekend_coverage', {})
        return weekend_config.get('weekend_penalty', 2)
    except Exception:
        # If any error occurs (including missing service context), return default penalty
        return 2


# --- Public Utility Functions (moved from utils) ---
def is_working_day(provider: Provider, target_date) -> bool:
    """
    Check if the target date is a working day for the provider.
    
    Args:
        provider: Provider object with availability configuration
        target_date: Date to check
    
    Returns:
        bool: True if it's a working day, False otherwise
    """
    weekday = target_date.weekday()
    weekday_enum = weekday_from_int(weekday)

    # Check if provider has availability configuration
    if provider.availability is not None:
        return weekday_enum in provider.availability.working_days

    # Default: Monday-Friday (0=Monday, 6=Sunday)
    return weekday < 5 