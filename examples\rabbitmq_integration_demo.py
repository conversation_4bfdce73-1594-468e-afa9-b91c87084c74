#!/usr/bin/env python3
"""
RabbitMQ Integration Demonstration

This script demonstrates the RabbitMQ integration for production-ready
event processing in the CareAXL Scheduling Engine.
"""

import asyncio
import time
import sys
import os
from datetime import date, datetime
from typing import Dict, Any

# Add the parent directory to the path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.events.rabbitmq_integration import (
    RabbitMQConfig, initialize_rabbitmq, get_rabbitmq_event_bus,
    publish_event_rabbitmq, EventType, EventPriority, Event
)
from src.services.scheduler_service import SchedulerService, SchedulerRequest, ServiceType


def print_header(title: str):
    """Print a formatted header."""
    print(f"\n{title}")
    print("=" * len(title))


def print_section(title: str):
    """Print a formatted section."""
    print(f"\n{title}")
    print("-" * len(title))


async def demo_rabbitmq_basic_operations():
    """Demonstrate basic RabbitMQ operations."""
    print_section("🔧 Basic RabbitMQ Operations")
    
    # Initialize RabbitMQ
    config = RabbitMQConfig(
        host="localhost",
        port=5672,
        username="guest",
        password="guest"
    )
    
    initialize_rabbitmq(config)
    event_bus = await get_rabbitmq_event_bus()
    
    # Subscribe to events
    async def appointment_handler(event: Event):
        print(f"   📋 Handled appointment event: {event.type.value}")
        print(f"   📊 Data: {event.data}")
    
    async def optimization_handler(event: Event):
        print(f"   ⚡ Handled optimization event: {event.type.value}")
        print(f"   📊 Data: {event.data}")
    
    await event_bus.subscribe(EventType.APPOINTMENT_CREATED, appointment_handler)
    await event_bus.subscribe(EventType.SCHEDULE_OPTIMIZATION_REQUESTED, optimization_handler)
    
    # Publish events
    print("1. Publishing appointment created event...")
    await publish_event_rabbitmq(
        EventType.APPOINTMENT_CREATED,
        {
            'appointment_id': 'apt_001',
            'patient_id': 'patient_123',
            'provider_id': 'provider_456',
            'appointment_date': date.today().isoformat(),
            'service_type': 'skilled_nursing'
        },
        priority=EventPriority.HIGH,
        correlation_id="demo_001"
    )
    
    print("2. Publishing optimization request event...")
    await publish_event_rabbitmq(
        EventType.SCHEDULE_OPTIMIZATION_REQUESTED,
        {
            'optimization_type': 'assignment',
            'service_type': 'skilled_nursing',
            'target_date': date.today().isoformat(),
            'requested_by': 'demo_user'
        },
        priority=EventPriority.CRITICAL,
        correlation_id="demo_002"
    )
    
    # Get queue statistics
    print("3. Getting queue statistics...")
    stats = await event_bus.get_queue_stats()
    for queue_name, queue_stats in stats.items():
        print(f"   📊 {queue_name}: {queue_stats['message_count']} messages")
    
    return event_bus


async def demo_hybrid_service_with_rabbitmq():
    """Demonstrate hybrid service with RabbitMQ integration."""
    print_section("🏥 Hybrid Service with RabbitMQ")
    
    # Initialize hybrid service with RabbitMQ
    scheduler_service = SchedulerService(
        use_mock_services=True,
        use_rabbitmq=True
    )
    
    # Demo 1: Synchronous operations
    print("1. Synchronous Operations:")
    
    # Get providers
    request = SchedulerRequest(
        operation="get_providers",
        data={},
        service_type=ServiceType.SKILLED_NURSING,
        correlation_id="hybrid_demo_001"
    )
    
    response = scheduler_service.process_request(request)
    if response.success:
        providers = response.data.get('providers', [])
        print(f"   ✅ Retrieved {len(providers)} providers")
        print(f"   ⏱️  Response time: {response.processing_time:.3f}s")
    
    # Demo 2: Hybrid operations (sync + async with RabbitMQ)
    print("\n2. Hybrid Operations (Sync + RabbitMQ Async):")
    
    # Create appointment (triggers RabbitMQ events)
    appointment_data = {
        'patient_id': 'patient_urgent_001',
        'provider_id': 'provider_rn_001',
        'appointment_date': date.today().isoformat(),
        'service_type': 'skilled_nursing',
        'duration_min': 90,
        'urgent': True,
        'required_skills': ['wound_care', 'assessment']
    }
    
    request = SchedulerRequest(
        operation="create_appointment",
        data=appointment_data,
        correlation_id="hybrid_demo_002"
    )
    
    start_time = time.time()
    response = scheduler_service.process_request(request)
    processing_time = time.time() - start_time
    
    if response.success:
        print(f"   ✅ Appointment created immediately")
        print(f"   📋 Appointment ID: {response.data.get('id')}")
        print(f"   ⏱️  Response time: {processing_time:.3f}s")
        print(f"   🔄 RabbitMQ events triggered in background")
    
    # Request optimization (triggers RabbitMQ events)
    request = SchedulerRequest(
        operation="optimize_schedule",
        data={'requested_by': 'demo_user'},
        service_type=ServiceType.SKILLED_NURSING,
        target_date=date.today(),
        correlation_id="hybrid_demo_003"
    )
    
    start_time = time.time()
    response = scheduler_service.process_request(request)
    processing_time = time.time() - start_time
    
    if response.success:
        print(f"   ✅ Optimization request accepted immediately")
        print(f"   ⏱️  Response time: {processing_time:.3f}s")
        print(f"   🔄 RabbitMQ optimization events triggered")


async def demo_event_priority_handling():
    """Demonstrate event priority handling in RabbitMQ."""
    print_section("🎯 Event Priority Handling")
    
    event_bus = await get_rabbitmq_event_bus()
    
    # Subscribe to different priority events
    async def critical_handler(event: Event):
        print(f"   🚨 CRITICAL: {event.type.value} - {event.data}")
    
    async def high_handler(event: Event):
        print(f"   ⚡ HIGH: {event.type.value} - {event.data}")
    
    async def normal_handler(event: Event):
        print(f"   📋 NORMAL: {event.type.value} - {event.data}")
    
    async def low_handler(event: Event):
        print(f"   📝 LOW: {event.type.value} - {event.data}")
    
    await event_bus.subscribe(EventType.NOTIFICATION_SEND_REQUESTED, critical_handler)
    await event_bus.subscribe(EventType.SCHEDULE_OPTIMIZATION_REQUESTED, high_handler)
    await event_bus.subscribe(EventType.APPOINTMENT_CREATED, normal_handler)
    await event_bus.subscribe(EventType.EXTERNAL_SYSTEM_SYNC_REQUESTED, low_handler)
    
    # Publish events with different priorities
    print("Publishing events with different priorities...")
    
    # Critical priority
    await publish_event_rabbitmq(
        EventType.NOTIFICATION_SEND_REQUESTED,
        {'message': 'Urgent patient alert', 'patient_id': 'patient_001'},
        priority=EventPriority.CRITICAL,
        correlation_id="priority_demo_001"
    )
    
    # High priority
    await publish_event_rabbitmq(
        EventType.SCHEDULE_OPTIMIZATION_REQUESTED,
        {'optimization_type': 'assignment', 'urgent': True},
        priority=EventPriority.HIGH,
        correlation_id="priority_demo_002"
    )
    
    # Normal priority
    await publish_event_rabbitmq(
        EventType.APPOINTMENT_CREATED,
        {'appointment_id': 'apt_002', 'routine': True},
        priority=EventPriority.NORMAL,
        correlation_id="priority_demo_003"
    )
    
    # Low priority
    await publish_event_rabbitmq(
        EventType.EXTERNAL_SYSTEM_SYNC_REQUESTED,
        {'system': 'billing', 'sync_type': 'daily'},
        priority=EventPriority.LOW,
        correlation_id="priority_demo_004"
    )


async def demo_error_handling_and_retry():
    """Demonstrate error handling and retry mechanisms."""
    print_section("🛡️ Error Handling and Retry")
    
    event_bus = await get_rabbitmq_event_bus()
    
    # Subscribe to events with error handling
    async def failing_handler(event: Event):
        """Handler that fails to demonstrate retry logic."""
        if event.retry_count < 2:  # Fail first 2 attempts
            raise Exception(f"Simulated failure on attempt {event.retry_count + 1}")
        print(f"   ✅ Successfully handled after {event.retry_count} retries")
    
    await event_bus.subscribe(EventType.EXTERNAL_SYSTEM_SYNC_REQUESTED, failing_handler)
    
    # Publish event that will fail and retry
    print("Publishing event that will fail and retry...")
    await publish_event_rabbitmq(
        EventType.EXTERNAL_SYSTEM_SYNC_REQUESTED,
        {'system': 'test', 'data': 'will_retry'},
        priority=EventPriority.NORMAL,
        correlation_id="retry_demo_001"
    )


async def demo_production_scenarios():
    """Demonstrate real-world production scenarios."""
    print_section("🏭 Production Scenarios")
    
    scheduler_service = SchedulerService(
        use_mock_services=True,
        use_rabbitmq=True
    )
    
    # Scenario 1: High-volume appointment creation
    print("1. High-Volume Appointment Creation:")
    
    for i in range(5):
        appointment_data = {
            'patient_id': f'patient_bulk_{i:03d}',
            'provider_id': f'provider_{i % 3 + 1}',
            'appointment_date': date.today().isoformat(),
            'service_type': 'skilled_nursing',
            'duration_min': 60,
            'required_skills': ['assessment']
        }
        
        request = SchedulerRequest(
            operation="create_appointment",
            data=appointment_data,
            correlation_id=f"bulk_demo_{i:03d}"
        )
        
        response = scheduler_service.process_request(request)
        if response.success:
            print(f"   ✅ Created appointment {i+1}/5")
    
    # Scenario 2: Provider availability change
    print("\n2. Provider Availability Change:")
    
    # Simulate provider becoming unavailable
    await publish_event_rabbitmq(
        EventType.PROVIDER_AVAILABILITY_CHANGED,
        {
            'provider_id': 'provider_1',
            'available': False,
            'affected_dates': [date.today().isoformat()],
            'reason': 'emergency_leave'
        },
        priority=EventPriority.HIGH,
        correlation_id="availability_demo_001"
    )
    
    print("   🔄 Triggered optimization for affected dates")
    
    # Scenario 3: Patient preference change
    print("\n3. Patient Preference Change:")
    
    await publish_event_rabbitmq(
        EventType.PATIENT_PREFERENCES_CHANGED,
        {
            'patient_id': 'patient_001',
            'preferences': {
                'preferred_provider': 'provider_2',
                'preferred_time': 'morning',
                'accessibility_needs': ['wheelchair_access']
            }
        },
        priority=EventPriority.NORMAL,
        correlation_id="preference_demo_001"
    )
    
    print("   🔄 Triggered preference-based optimization")


async def main():
    """Run the RabbitMQ integration demonstration."""
    print_header("🐰 RabbitMQ Integration Demonstration")
    print("This demonstration shows production-ready event processing")
    print("using RabbitMQ for the CareAXL Scheduling Engine.")
    
    try:
        # Demo 1: Basic RabbitMQ operations
        event_bus = await demo_rabbitmq_basic_operations()
        
        # Demo 2: Hybrid service with RabbitMQ
        await demo_hybrid_service_with_rabbitmq()
        
        # Demo 3: Event priority handling
        await demo_event_priority_handling()
        
        # Demo 4: Error handling and retry
        await demo_error_handling_and_retry()
        
        # Demo 5: Production scenarios
        await demo_production_scenarios()
        
        # Final statistics
        print_section("📊 Final Statistics")
        stats = await event_bus.get_queue_stats()
        total_messages = sum(queue['message_count'] for queue in stats.values())
        print(f"Total messages processed: {total_messages}")
        
        for queue_name, queue_stats in stats.items():
            print(f"   📊 {queue_name}: {queue_stats['message_count']} messages")
        
        print_header("✅ RabbitMQ Integration Demonstration Completed")
        print("The RabbitMQ integration provides:")
        print("   🚀 Production-ready event processing")
        print("   📈 Scalable message queuing")
        print("   🛡️ Reliable delivery with retry mechanisms")
        print("   🎯 Priority-based event handling")
        print("   🔄 Seamless integration with hybrid architecture")
        
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main()) 