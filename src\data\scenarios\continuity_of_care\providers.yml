providers:
  - id: "cont-rn-diabetes"
    name: "Dr. <PERSON>, <PERSON><PERSON>"
    role: "RN"
    skills: [ "medication_management", "diabetes_management", "assessment", "iv_therapy" ]
    home_location:
      latitude: 40.7589
      longitude: -73.9851
      city: "New York"
      state: "NY"
      address: "123 5th Avenue, New York, NY 10003"
    service_areas:
      - name: "Manhattan Central"
        zone_type: "service"
        boundary_wkt: "POLYGON((-74.0 40.7, -73.9 40.7, -73.9 40.8, -74.0 40.8, -74.0 40.7))"
    availability:
      working_days: [ "monday", "tuesday", "wednesday", "thursday", "friday" ]
      working_hours: [ "08:00", "17:00" ]
    capacity:
      max_hours_per_day: 8
      max_tasks_count_in_day: 6
    provider_preferences:
      preferred_consumers: [ "cont-patient-diabetes-001" ]  # Established relationship

  - id: "cont-rn-wound"
    name: "Dr. <PERSON>, RN"
    role: "RN"
    skills: [ "wound_care", "assessment", "medication_management", "iv_therapy" ]
    home_location:
      latitude: 40.7505
      longitude: -73.9934
      city: "New York"
      state: "NY"
      address: "456 7th Avenue, New York, NY 10001"
    service_areas:
      - name: "Manhattan West"
        zone_type: "service"
        boundary_wkt: "POLYGON((-74.1 40.7, -74.0 40.7, -74.0 40.8, -74.1 40.8, -74.1 40.7))"
    availability:
      working_days: [ "monday", "tuesday", "wednesday", "thursday", "friday" ]
      working_hours: [ "09:00", "18:00" ]
    capacity:
      max_hours_per_day: 8
      max_tasks_count_in_day: 5
    provider_preferences:
      preferred_consumers: [ "cont-patient-wound-001" ]  # Established relationship

  - id: "cont-cna-mobility"
    name: "Maria Garcia, CNA"
    role: "CNA"
    skills: [ "personal_care", "mobility_assistance", "housekeeping", "meal_assistance" ]
    home_location:
      latitude: 40.7829
      longitude: -73.9654
      city: "New York"
      state: "NY"
      address: "789 Central Park West, New York, NY 10024"
    service_areas:
      - name: "Manhattan North"
        zone_type: "service"
        boundary_wkt: "POLYGON((-74.0 40.8, -73.9 40.8, -73.9 40.9, -74.0 40.9, -74.0 40.8))"
    availability:
      working_days: [ "monday", "tuesday", "wednesday", "thursday", "friday" ]
      working_hours: [ "07:00", "16:00" ]
    capacity:
      max_hours_per_day: 8
      max_tasks_count_in_day: 4
    provider_preferences:
      preferred_consumers: [ "cont-patient-mobility-001" ]  # Established relationship