import uuid
import random
import yaml
from datetime import datetime, timedelta, time, date

# Skills pool for random selection (matching appointments)
SKILLS = [
    "assessment", "medication_management", "wound_care", "iv_therapy", "personal_care",
    "mobility_assistance", "housekeeping", "meal_assistance", "vital_signs"
]

# Name pools for random generation
FIRST_NAMES = [
    "Dr. <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>",
    "Dr. <PERSON>", "Dr<PERSON> <PERSON>", "Dr<PERSON> <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>",
    "Dr. <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>",
    "Dr. <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>", "<PERSON>. <PERSON>"
]
LAST_NAMES = [
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
]

# Generate a random name
random_name = lambda: f"{random.choice(FIRST_NAMES)} {random.choice(LAST_NAMES)}"

# Generate a random address
random_address = lambda i: f"{200 + i} <PERSON>vider St, New York, NY 10001"

# Generate availability for the next 7 days
def generate_availability():
    availability = {}
    start_date = datetime.strptime("2025-06-27", "%Y-%m-%d")
    
    for i in range(7):
        date_str = (start_date + timedelta(days=i)).strftime("%Y-%m-%d")
        # Random availability: 70% chance of being available
        if random.random() < 0.7:
            # Generate 1-3 time slots per day
            num_slots = random.randint(1, 3)
            slots = []
            for _ in range(num_slots):
                start_hour = random.randint(8, 16)  # 8 AM to 4 PM
                start_minute = random.choice([0, 15, 30, 45])
                duration = random.choice([60, 90, 120, 180])  # 1-3 hours
                
                start_time = f"{start_hour:02d}:{start_minute:02d}"
                end_hour = (start_hour * 60 + start_minute + duration) // 60
                end_minute = (start_hour * 60 + start_minute + duration) % 60
                end_time = f"{end_hour:02d}:{end_minute:02d}"
                
                slots.append({
                    "start_time": start_time,
                    "end_time": end_time
                })
            availability[date_str] = slots
        else:
            availability[date_str] = []
    
    return availability

# Generate 50 providers (fewer than appointments for realistic ratio)
bulk_providers = []
for i in range(1, 51):
    provider = {
        "id": str(uuid.uuid4()),  # UUID as required by domain model
        "name": random_name(),
        "home_location": {
            "latitude": round(random.uniform(40.70, 40.80), 5),
            "longitude": round(random.uniform(-74.02, -73.95), 5),
            "address": random_address(i),
            "city": "New York",
            "state": "NY",
            "country": "USA",
            "zip_code": "10001"
        },
        "service_areas": [
            {
                "id": i,
                "name": f"Service Area {i}",
                "boundary_wkt": f"POLYGON(({-74.02 + random.uniform(-0.01, 0.01)} {40.70 + random.uniform(-0.01, 0.01)}, {-73.95 + random.uniform(-0.01, 0.01)} {40.70 + random.uniform(-0.01, 0.01)}, {-73.95 + random.uniform(-0.01, 0.01)} {40.80 + random.uniform(-0.01, 0.01)}, {-74.02 + random.uniform(-0.01, 0.01)} {40.80 + random.uniform(-0.01, 0.01)}, {-74.02 + random.uniform(-0.01, 0.01)} {40.70 + random.uniform(-0.01, 0.01)}))",
                "zone_type": "service",
                "description": f"Primary service area for provider {i}",
                "priority": 1
            }
        ],
        "languages": random.sample(["English", "Spanish", "French", "Mandarin", "Russian"], random.randint(1, 2)),
        "transportation": random.choice(["car", "public_transit", "bike", "walk"]),
        "availability": {
            "primary_shift": {
                "shift_name": "day_shift",
                "shift_start": "08:00",
                "shift_end": "16:00",
                "crosses_midnight": False,
                "shift_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
            },
            "working_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
            "break_periods": [["12:00", "13:00"]],
            "working_hours": ["08:00", "16:00"],
            "holidays": [],
            "date_specific_availability": [],
            "time_off_periods": [],
            "max_hours_per_day": 8,
            "max_hours_per_week": 40,
            "unavailable_time_slots": [],
            "min_gap_between_assignments": 30,
            "overtime_allowed": False,
            "on_call_available": False,
            "on_call_days": [],
            "properties": {}
        },
        "current_task_count": 0,
        "critical": False,
        "current_availability_status": "AVAILABLE",
        "current_unavailable_until": None,
        "role": random.choice(["RN", "LPN", "CNA", "PT", "OT"]),
        "skills": random.sample(SKILLS, k=random.randint(2, 5)),  # 2-5 skills each
        "capacity": {
            "max_allocated_task_points_in_day": 27,
            "max_tasks_count_in_day": random.choice([3, 4, 5, 6, 8]),
            "max_hours_per_day": 8,
            "max_consecutive_tasks": 4,
            "min_break_between_tasks": 15,
            "properties": {}
        },
        "provider_preferences": {
            "blacklisted_consumers": [],
            "preferred_consumers": [],
            "blackout_areas": [],
            "preferred_task_types": [],
            "blacklisted_task_types": [],
            "properties": {}
        },
        "properties": {}
    }
    bulk_providers.append(provider)

# Load the existing YAML
try:
    with open("providers.yml", "r", encoding="utf-8") as f:
        data = yaml.safe_load(f)
    # Handle empty file or None
    if data is None:
        data = {"providers": []}
except FileNotFoundError:
    data = {"providers": []}

# Append the new providers
if "providers" in data:
    data["providers"].extend(bulk_providers)
else:
    data["providers"] = bulk_providers

# Write back to the YAML file
with open("providers.yml", "w", encoding="utf-8") as f:
    yaml.dump(data, f, sort_keys=False, allow_unicode=True)

print("Appended 50 bulk providers to providers.yml") 