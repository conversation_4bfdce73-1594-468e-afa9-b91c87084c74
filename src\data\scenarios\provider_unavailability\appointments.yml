appointments:
  # Urgent appointments that were assigned to unavailable provider
  - consumer_id: "unavail-consumer-001"
    required_skills: [ "medication_management" ]
    duration_min: 30
    appointment_date: "2024-01-15"
    priority: "urgent"
    urgent: true
    pinning:
      is_pinned: true
      pinned_provider_id: "unavail-provider-001"  # Pinned to unavailable provider
      pin_reason: "patient_preference"
      can_unpin: true

  - consumer_id: "unavail-consumer-006"
    required_skills: [ "wound_care", "assessment" ]
    duration_min: 45
    appointment_date: "2024-01-15"
    priority: "urgent"
    urgent: true
    pinning:
      is_pinned: true
      pinned_provider_id: "unavail-provider-001"  # Pinned to unavailable provider
      pin_reason: "continuity_of_care"
      can_unpin: true

  # Regular appointments assigned to unavailable provider
  - consumer_id: "unavail-consumer-002"
    required_skills: [ "iv_therapy" ]
    duration_min: 60
    appointment_date: "2024-01-15"
    priority: "high"
    pinning:
      is_pinned: true
      pinned_provider_id: "unavail-provider-001"
      pin_reason: "provider_expertise"
      can_unpin: true

  - consumer_id: "unavail-consumer-007"
    required_skills: [ "medication_management" ]
    duration_min: 30
    appointment_date: "2024-01-16"
    priority: "normal"
    pinning:
      is_pinned: true
      pinned_provider_id: "unavail-provider-001"
      pin_reason: "routine_care"
      can_unpin: true

  # Appointments that can be reassigned to backup providers
  - consumer_id: "unavail-consumer-003"
    required_skills: [ "wound_care" ]
    duration_min: 45
    appointment_date: "2024-01-15"
    priority: "normal"
    pinning:
      is_pinned: false

  - consumer_id: "unavail-consumer-004"
    required_skills: [ "iv_therapy" ]
    duration_min: 30
    appointment_date: "2024-01-15"
    priority: "high"
    pinning:
      is_pinned: false

  # Basic care appointments (can go to CNA)
  - consumer_id: "unavail-consumer-005"
    required_skills: [ "basic_care" ]
    duration_min: 60
    appointment_date: "2024-01-15"
    priority: "normal"
    pinning:
      is_pinned: false

  - consumer_id: "unavail-consumer-008"
    required_skills: [ "companionship" ]
    duration_min: 120
    appointment_date: "2024-01-16"
    priority: "low"
    pinning:
      is_pinned: false

  # Appointments requiring skills not available in backup providers
  - consumer_id: "unavail-consumer-001"
    required_skills: [ "assessment", "wound_care" ]  # Complex combination
    duration_min: 90
    appointment_date: "2024-01-16"
    priority: "high"
    pinning:
      is_pinned: true
      pinned_provider_id: "unavail-provider-001"
      pin_reason: "complex_care_needs"
      can_unpin: false  # Cannot be unpinned due to complexity

  # Follow-up appointments in same episode
  - consumer_id: "unavail-consumer-002"
    required_skills: [ "medication_management" ]
    duration_min: 30
    appointment_date: "2024-01-17"
    priority: "normal"
    relationships:
      episode_id: "episode-regular-001"
      requires_same_provider: true
    pinning:
      is_pinned: true
      pinned_provider_id: "unavail-provider-001"
      pin_reason: "episode_continuity"
      can_unpin: true

  # Emergency appointment that needs immediate attention
  - consumer_id: "unavail-consumer-006"
    required_skills: [ "assessment" ]
    duration_min: 30
    appointment_date: "2024-01-15"
    priority: "urgent"
    urgent: true
    timing:
      preferred_time: "08:00"
      strict_timing: true
    pinning:
      is_pinned: false

  # Appointment that might not get reassigned due to capacity
  - consumer_id: "unavail-consumer-008"
    required_skills: [ "medication_management", "wound_care" ]
    duration_min: 75
    appointment_date: "2024-01-15"
    priority: "low"
    pinning:
      is_pinned: true
      pinned_provider_id: "unavail-provider-001"
      pin_reason: "patient_comfort"
      can_unpin: true
