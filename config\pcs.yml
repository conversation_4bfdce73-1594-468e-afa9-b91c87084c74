service_type: pcs
required_skills:
  - "Personal Care Aide"
  - "Activities of Daily Living (ADL)"
  - "Mobility Assistance"
  - "Personal Hygiene"
  - "Meal Preparation"
  - "Light Housekeeping"

geographic_radius_miles: 35.0
max_daily_appointments_per_provider: 10
max_weekly_hours_per_provider: 40

# Constraint weights (0.0 to 1.0)
continuity_weight: 0.7
workload_balance_weight: 0.9
geographic_clustering_weight: 0.8
patient_preference_weight: 0.6
capacity_threshold_percentage: 0.75

# Role hierarchy configuration
enable_role_hierarchy: true
role_hierarchy:
  "RN": ["LPN", "CNA", "Personal Care Aide"]
  "LPN": ["CNA", "Personal Care Aide"]
  "CNA": ["Personal Care Aide"]
  "Personal Care Aide": []

role_hierarchy_penalties:
  "RN->LPN": 5
  "RN->CNA": 10
  "RN->Personal Care Aide": 15
  "LPN->CNA": 5
  "LPN->Personal Care Aide": 10
  "CNA->Personal Care Aide": 5

# Service-specific settings
visit_duration_minutes: 30
requires_initial_assessment: false
allows_weekend_visits: true
emergency_response_time_hours: 6

# Geographic clustering settings
cluster_radius_miles: 20.0
max_cluster_size: 12
prefer_same_day_clustering: true

# Continuity of care settings
continuity_priority_bonus: 80
continuity_threshold_days: 30  # Days to consider for continuity
existing_relationship_bonus: 40

# Workload balancing settings
target_daily_appointments: 8
workload_variance_tolerance: 4
overtime_penalty_multiplier: 1.2

# Patient preference settings
preferred_provider_bonus: 25
preferred_time_bonus: 15
preferred_location_bonus: 35 