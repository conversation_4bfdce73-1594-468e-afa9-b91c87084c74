# CAXL Scheduling Engine

A US-based appointment scheduling system using Timefold optimization with a **two-job architecture** for realistic
healthcare scheduling.

## 🏗️ Two-Job Architecture

The system uses two separate jobs to handle appointment scheduling:

### 1. **AssignAppointment Job** (Nightly)

- **Purpose**: Assigns **date** and **provider** to appointment requests
- **Schedule**: Runs nightly at 2:00 AM
- **Output**: Appointments with date + provider assigned, but no specific time
- **Use Case**: Strategic planning for the upcoming week

### 2. **DayPlan Job** (Daily)

- **Purpose**: Assigns **time slots** to appointments already scheduled for that day
- **Schedule**: Runs daily at 6:00 AM
- **Input**: Appointments already assigned to that day with providers
- **Output**: Complete schedule with specific times
- **Use Case**: Daily operational planning

## 🎯 Benefits of Two-Job System

- **Realistic Workflow**: Matches actual healthcare scheduling practices
- **Flexibility**: Can handle last-minute changes and urgent appointments
- **Efficiency**: Separates strategic planning from operational scheduling
- **Scalability**: Each job can be optimized independently
- **Reliability**: If one job fails, the other can still run

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd caxl-scheduling-engine

# Install dependencies
pip install -e .
```

### Running the Jobs

#### 1. Run AssignAppointment Job (assigns date + provider)

```bash
# Run once
python -m caxl_scheduling_engine.jobs.assign_appointments

# Or use the scheduler
python -m caxl_scheduling_engine.scheduler --mode once --job assign
```

#### 2. Run DayPlan Job (assigns time slots)

```bash
# Run for today
python -m caxl_scheduling_engine.jobs.day_plan

# Run for specific date
python -m caxl_scheduling_engine.jobs.day_plan --date 2024-01-15

# Or use the scheduler
python -m caxl_scheduling_engine.scheduler --mode once --job dayplan --date 2024-01-15
```

#### 3. Run Both Jobs via Scheduler

```bash
# Run as daemon (runs both jobs on schedule)
python -m caxl_scheduling_engine.scheduler --mode daemon

# Run example demonstrating both jobs
python example_two_jobs.py
```

## 📋 Configuration

### Global Configuration (`config/scheduler.yml`)

```yaml
# System-wide settings
rolling_window_days: 7
batch_size: 100
max_solving_time_seconds: 300
log_level: INFO

# Feature toggles
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true

# Scheduling times
assign_appointment_time: "02:00"
day_plan_time: "06:00"
```

### Service-Specific Configuration

- `config/skilled_nursing.yml` - Skilled nursing service settings
- `config/physical_therapy.yml` - Physical therapy service settings

## 🏥 Service Types

The system supports multiple healthcare service types:

- **Skilled Nursing**: Home health nursing services
- **Physical Therapy**: Rehabilitation and therapy services
- **Occupational Therapy**: Daily living skills therapy
- **Speech Therapy**: Communication and swallowing therapy
- **Home Health Aide**: Personal care services
- **Medical Social Work**: Social services and counseling

## 🔧 Constraints

### AssignAppointment Job Constraints

- **Hard Constraints**:
    - Required provider skills
    - Geographic service area
    - Timed visit date requirements
    - Provider capacity limits

- **Soft Constraints**:
    - Workload balancing
    - Geographic clustering
    - Patient preferences
    - Continuity of care

### DayPlan Job Constraints

- **Hard Constraints**:
    - All appointments must have time assigned
    - No double booking of providers

- **Soft Constraints**:
    - Preferred working hours
    - Workload balance across time slots

## 📊 Output Examples

### AssignAppointment Job Output

```
=== Assignment Results ===
Batch ID: assign_20240115_020000
Total Appointments: 25
Assigned: 23
Unassigned: 2
Processing Time: 45.23s

=== Assignment Details ===
✅ John Smith
   Service: skilled_nursing (FLEXIBLE)
   Provider: Nurse Johnson on 2024-01-16
   ✅ Satisfied: provider_assigned, date_assigned, required_skills, service_area

❌ Mary Wilson
   Service: physical_therapy (TIMED)
   Provider: Unassigned
   ❌ Violated: no_provider_assigned, no_date_assigned
```

### DayPlan Job Output

```
=== Day Plan Results ===
Date: 2024-01-15
Batch ID: dayplan_20240115_060000
Total Appointments: 8
Time Assigned: 7
No Time Assigned: 1
Processing Time: 12.45s

=== Time Assignment Details ===
✅ Patient John Smith -> Provider Nurse Johnson -> Time 09:00-10:00
✅ Patient Mary Wilson -> Provider Dr. Brown -> Time 14:00-15:00
❌ Patient Bob Davis -> Provider Nurse Smith -> Time None
```

## 🗂️ Project Structure

```
caxl-scheduling-engine/
├── src/caxl_scheduling_engine/
│   ├── domain.py              # Data models and entities
│   ├── constraints.py         # Constraint definitions
│   ├── config_manager.py      # Configuration management
│   ├── demo_data.py           # Demo data generation
│   ├── scheduler.py           # Main scheduler
│   └── jobs/
│       ├── assign_appointments.py  # AssignAppointment job
│       └── day_plan.py             # DayPlan job
├── config/
│   ├── scheduler.yml          # Global configuration
│   ├── skilled_nursing.yml    # Service-specific config
│   └── physical_therapy.yml   # Service-specific config
├── logs/                      # Log files
├── example_two_jobs.py        # Example demonstrating both jobs
└── README.md
```

## 🔄 Workflow

1. **Nightly (2:00 AM)**: AssignAppointment job runs
    - Processes new appointment requests
    - Assigns date and provider based on constraints
    - Creates `ScheduledAppointment` records

2. **Daily (6:00 AM)**: DayPlan job runs
    - Loads appointments scheduled for that day
    - Assigns specific time slots
    - Creates final schedule

3. **Real-time**: System can handle urgent changes
    - Emergency appointments can be added
    - Cancellations can be processed
    - Schedule adjustments can be made

## 🛠️ Customization

### Adding New Service Types

1. Create configuration file: `config/new_service.yml`
2. Add service type to `ServiceType` class in `domain.py`
3. Update constraints if needed

### Modifying Constraints

- Edit `constraints.py` for AssignAppointment constraints
- Edit `constraints.py` for DayPlan constraints
- Update configuration files for weights and parameters

### Database Integration

- Replace demo data functions with database queries
- Implement data persistence for assignments
- Add transaction management

## 📈 Monitoring and Logging

- **Log Files**: All jobs log to `logs/` directory
- **Metrics**: Processing time, success rates, constraint violations
- **Alerts**: Failed jobs, high violation rates, capacity issues

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details. 