"""
Geographic Clustering Optimization Constraint (C006)

This constraint maintains capacity thresholds for service types per date.
This is a SOFT constraint for optimization preferences.
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint, Joiners

from model.planning_models import AppointmentAssignment


def capacity_thresholds(constraint_factory: ConstraintFactory) -> Constraint:
    """Maintain capacity thresholds for service types per date."""
    # Handle Timefold validation calls where factory is None
    if constraint_factory is None:
        return constraint_factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .join(AppointmentAssignment,
                  Joiners.equal(lambda assignment: assignment.assigned_date),
                  Joiners.equal(lambda assignment: assignment.appointment_data.required_skills))
            .group_by(lambda assignment1, assignment2: assignment1.assigned_date,
                      lambda assignment1, assignment2: 1)
            .filter(lambda assigned_date, count: count > 3)  # Max 3 appointments per date per service type
            .penalize(HardSoftScore.ONE_SOFT, lambda assigned_date, count: count - 3)
            .as_constraint("Capacity thresholds"))


def geographic_clustering_optimization_constraints(factory: ConstraintFactory):
    """Return all geographic clustering optimization constraints."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return []
        
    return [
        capacity_thresholds(factory),
    ]
