# CareAXL Scheduling Engine: Technical Reference Guide

## Table of Contents
1. [Timefold Constraint Solver Fundamentals](#1-timefold-constraint-solver-fundamentals)
2. [Two-Stage Optimization Architecture](#2-two-stage-optimization-architecture)
3. [Domain Models and Planning Entities](#3-domain-models-and-planning-entities)
4. [Constraint System Architecture](#4-constraint-system-architecture)
5. [Configuration Management System](#5-configuration-management-system)
6. [Scheduler Parameterization and Execution](#6-scheduler-parameterization-and-execution)
7. [Service Integration and Event Architecture](#7-service-integration-and-event-architecture)
8. [Daily Solver Operations](#8-daily-solver-operations)
9. [Adding New Constraints](#9-adding-new-constraints)
10. [External System Integration](#10-external-system-integration)

---

## 1. Timefold Constraint Solver Fundamentals

### 1.1 Timefold Overview

Timefold is an AI-powered constraint solver that optimizes complex scheduling problems by evaluating millions of combinations against business rules.

```
┌─────────────────────────────────────────────────────────────┐
│                    TIMEFOLD SOLVER PROCESS                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  INPUT DATA          SOLVER ENGINE           OUTPUT         │
│  ┌─────────────┐    ┌─────────────────┐    ┌─────────────┐  │
│  │ Planning    │───▶│ Constraint      │───▶│ Optimized   │  │
│  │ Entities    │    │ Evaluation      │    │ Solution    │  │
│  │             │    │                 │    │             │  │
│  │ Problem     │    │ Search          │    │ Score:      │  │
│  │ Facts       │    │ Algorithms      │    │ 0hard/      │  │
│  │             │    │                 │    │ -150soft    │  │
│  │ Value       │    │ Score           │    │             │  │
│  │ Ranges      │    │ Calculation     │    │             │  │
│  └─────────────┘    └─────────────────┘    └─────────────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 Core Concepts

```
┌─────────────────────────────────────────────────────────────┐
│                 TIMEFOLD CORE CONCEPTS                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  PLANNING ENTITIES (What to optimize)                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  @planning_entity                                       │ │
│  │  class AppointmentAssignment:                           │ │
│  │      id: str                                            │ │
│  │      appointment_data: AppointmentData                  │ │
│  │      provider: Provider = None      ← PLANNING VARIABLE │ │
│  │      assigned_date: date = None     ← PLANNING VARIABLE │ │
│  │                                                         │ │
│  │  • Each appointment needs provider + date assignment    │ │
│  │  • Solver tries different combinations                  │ │
│  │  • Variables start unassigned (None)                    │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  PROBLEM FACTS (Fixed data)                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  @planning_solution                                     │ │
│  │  class AppointmentSchedule:                             │ │
│  │      providers: List[Provider]      ← VALUE RANGE      │ │
│  │      available_dates: List[date]    ← VALUE RANGE      │ │
│  │      appointments: List[Assignment] ← ENTITIES         │ │
│  │      score: HardSoftScore = None    ← SOLUTION SCORE   │ │
│  │                                                         │ │
│  │  • Providers/dates are fixed options                   │ │
│  │  • Solver selects from these ranges                    │ │
│  │  • Score measures solution quality                     │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  CONSTRAINTS (Business rules)                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  @with_config('skilled_nursing')                        │ │
│  │  def provider_skill_match(factory):                     │ │
│  │      return (factory.for_each(AppointmentAssignment)    │ │
│  │              .filter(lambda a: not has_skills(a))       │ │
│  │              .penalize(HardSoftScore.ONE_HARD)          │ │
│  │              .as_constraint("Skill match required"))    │ │
│  │                                                         │ │
│  │  • HARD constraint: Must be satisfied                  │ │
│  │  • Penalizes invalid skill assignments                 │ │
│  │  • @with_config injects configuration                  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.3 Constraint Types and Scoring

```
┌─────────────────────────────────────────────────────────────┐
│                    CONSTRAINT SCORING SYSTEM               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  HARD CONSTRAINTS (Must be satisfied)                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • Provider skill validation                             │ │
│  │ • Date availability                                     │ │
│  │ • Geographic service area                               │ │
│  │ • No double booking                                     │ │
│  │                                                         │ │
│  │ Score Impact: -1hard per violation                      │ │
│  │ Result: Solution is infeasible if any hard violated     │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  SOFT CONSTRAINTS (Optimization goals)                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • Workload balancing                                    │ │
│  │ • Geographic clustering                                 │ │
│  │ • Continuity of care                                    │ │
│  │ • Travel time minimization                              │ │
│  │                                                         │ │
│  │ Score Impact: -1soft to -100soft per violation         │ │
│  │ Result: Better scores = better optimization             │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  SCORE COMPARISON                                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Solution A: 0hard/-150soft  ← Better (feasible)        │ │
│  │ Solution B: -1hard/-50soft  ← Worse (infeasible)       │ │
│  │ Solution C: 0hard/-200soft  ← Worse (less optimized)   │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 2. Two-Stage Optimization Architecture

### 2.1 Architecture Overview

The system uses a two-stage approach to manage complexity and optimize different aspects separately.

```
┌─────────────────────────────────────────────────────────────┐
│                TWO-STAGE OPTIMIZATION FLOW                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  INPUT: Appointment Requests                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • Patient needs skilled nursing visit                   │ │
│  │ • Required skills: ["wound_care", "medication"]         │ │
│  │ • Preferred dates: July 1-7, 2024                       │ │
│  │ • Location: 123 Main St                                 │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  STAGE 1: ASSIGNMENT SOLVER                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Goal: Assign provider and date                          │ │
│  │                                                         │ │
│  │ Input:  AppointmentAssignment entities                  │ │
│  │ Output: Provider + Date assignments                     │ │
│  │                                                         │ │
│  │ Constraints:                                            │ │
│  │ • C001: Provider skill validation                       │ │
│  │ • C002: Date availability                               │ │
│  │ • C003: Geographic service area                         │ │
│  │ • C005: Workload balancing                              │ │
│  │ • C006: Geographic clustering                           │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  STAGE 2: DAY PLAN SOLVER                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Goal: Assign time slots and optimize routes             │ │
│  │                                                         │ │
│  │ Input:  TimeSlotAssignment entities                     │ │
│  │ Output: Time slots + Visit orders                       │ │
│  │                                                         │ │
│  │ Constraints:                                            │ │
│  │ • C011: No appointment overlap                          │ │
│  │ • C013: Healthcare task sequencing                     │ │
│  │ • C014: Travel time optimization                        │ │
│  │ • C016: Route optimization                              │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  OUTPUT: Complete Schedule                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • Provider: Sarah Johnson (RN)                          │ │
│  │ • Date: July 1, 2024                                    │ │
│  │ • Time: 9:00 AM - 10:00 AM                              │ │
│  │ • Visit order: 2nd appointment of the day               │ │
│  │ • Route: Optimized for minimal travel time              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Stage 1: Assignment Solver

```
┌─────────────────────────────────────────────────────────────┐
│                    ASSIGNMENT SOLVER PROCESS               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  EXECUTION: assign_appointments.py                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  class AssignAppointmentJob:                            │ │
│  │      def run(self, service_type, date_range):           │ │
│  │          # Load appointment requests                    │ │
│  │          appointments = load_appointments()             │ │
│  │                                                         │ │
│  │          # Create planning entities                     │ │
│  │          assignments = [AppointmentAssignment(apt)      │ │
│  │                        for apt in appointments]        │ │
│  │                                                         │ │
│  │          # Configure solver                             │ │
│  │          solver = SolverFactory.create(                 │ │
│  │              solution_class=AppointmentSchedule,        │ │
│  │              constraint_provider=define_constraints     │ │
│  │          )                                              │ │
│  │                                                         │ │
│  │          # Solve and return results                     │ │
│  │          solution = solver.solve(problem)               │ │
│  │          return process_assignment_results(solution)    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.3 Stage 2: Day Plan Solver

```
┌─────────────────────────────────────────────────────────────┐
│                    DAY PLAN SOLVER PROCESS                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  EXECUTION: day_plan.py                                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  class DayPlanJob:                                      │ │
│  │      def run(self, target_date):                        │ │
│  │          # Load assignments from Stage 1                │ │
│  │          assignments = load_assignments(target_date)    │ │
│  │                                                         │ │
│  │          # Transform to time slot entities              │ │
│  │          time_assignments = [                           │ │
│  │              TimeSlotAssignment(                        │ │
│  │                  scheduled_appointment=create_scheduled(│ │
│  │                      assignment                         │ │
│  │                  )                                      │ │
│  │              ) for assignment in assignments            │ │
│  │          ]                                              │ │
│  │                                                         │ │
│  │          # Configure day solver                         │ │
│  │          solver = SolverFactory.create(                 │ │
│  │              solution_class=DaySchedule,                │ │
│  │              constraint_provider=define_day_constraints │ │
│  │          )                                              │ │
│  │                                                         │ │
│  │          # Solve and return results                     │ │
│  │          solution = solver.solve(day_schedule)          │ │
│  │          return process_day_plan_results(solution)      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 3. Domain Models and Planning Entities

### 3.1 Model Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    MODEL ARCHITECTURE                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  DOMAIN MODELS (domain.py)                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Framework-agnostic business entities                    │ │
│  │                                                         │ │
│  │ • Provider                                              │ │
│  │ • Consumer (Patient)                                    │ │
│  │ • AppointmentData                                       │ │
│  │ • Location                                              │ │
│  │ • ServiceConfig                                         │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  PLANNING MODELS (planning_models.py)                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Timefold-specific entities with annotations             │ │
│  │                                                         │ │
│  │ • AppointmentAssignment (@planning_entity)              │ │
│  │ • TimeSlotAssignment (@planning_entity)                 │ │
│  │ • AppointmentSchedule (@planning_solution)              │ │
│  │ • DaySchedule (@planning_solution)                      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Core Domain Models

<augment_code_snippet path="src/model/domain.py" mode="EXCERPT">
````python
@dataclass
class Provider:
    """Healthcare provider (nurse, therapist, etc.)"""
    id: str
    name: str
    role: str  # "RN", "LPN", "CNA", "PT", "OT"
    skills: List[str]
    home_location: Optional[Location] = None
    availability: Optional[ProviderAvailability] = None
    capacity: Optional[ProviderCapacity] = None
    preferences: Optional[ProviderPreferences] = None

@dataclass
class AppointmentData:
    """Core appointment information"""
    id: str
    patient_id: str
    service_type: str
    required_skills: List[str]
    required_role: Optional[str] = None
    duration_min: int = 60
    location: Optional[Location] = None
    timing: Optional[AppointmentTiming] = None
    status: AppointmentStatus = AppointmentStatus.REQUESTED
````
</augment_code_snippet>

### 3.3 Planning Entities

<augment_code_snippet path="src/model/planning_models.py" mode="EXCERPT">
````python
@planning_entity
@dataclass
class AppointmentAssignment:
    """Planning entity for Stage 1: Assignment Solver"""
    id: Annotated[str, PlanningId]
    appointment_data: AppointmentData
    provider: Annotated[Optional[Provider], PlanningVariable] = field(default=None)
    assigned_date: Annotated[Optional[date], PlanningVariable] = field(default=None)

@planning_entity
@dataclass
class TimeSlotAssignment:
    """Planning entity for Stage 2: Day Plan Solver"""
    id: Annotated[str, PlanningId]
    scheduled_appointment: ScheduledAppointment
    time_slot: Annotated[Optional[time], PlanningVariable] = field(default=None)
    visit_order: Annotated[Optional[int], PlanningVariable] = field(default=None)
````
</augment_code_snippet>

### 3.4 Planning Solutions

<augment_code_snippet path="src/model/planning_models.py" mode="EXCERPT">
````python
@planning_solution
@dataclass
class AppointmentSchedule:
    """Solution for Stage 1: Assignment optimization"""
    id: str
    providers: Annotated[List[Provider], ProblemFactCollectionProperty, ValueRangeProvider]
    available_dates: Annotated[List[date], ProblemFactCollectionProperty, ValueRangeProvider]
    appointment_assignments: Annotated[List[AppointmentAssignment], PlanningEntityCollectionProperty]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)

@planning_solution
@dataclass
class DaySchedule:
    """Solution for Stage 2: Day planning optimization"""
    id: str
    date: date
    time_slots: Annotated[List[time], ProblemFactCollectionProperty, ValueRangeProvider]
    scheduled_appointments: Annotated[List[ScheduledAppointment], ProblemFactCollectionProperty]
    time_assignments: Annotated[List[TimeSlotAssignment], PlanningEntityCollectionProperty]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)
````
</augment_code_snippet>

---

## 4. Constraint System Architecture

### 4.1 Constraint Organization

```
┌─────────────────────────────────────────────────────────────┐
│                 CONSTRAINT FILE STRUCTURE                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  src/constraints/                                           │
│  ├── assignment_constraints.py    ← Stage 1 coordinator     │
│  ├── day_constraints.py          ← Stage 2 coordinator     │
│  ├── base_constraints.py         ← @with_config decorator  │
│  ├── config_registry.py          ← Configuration system    │
│  │                                                         │
│  ├── c001_asgn_provider_skill_validation.py                │
│  ├── c002_asgn_date_based_availability.py                  │
│  ├── c003_asgn_geographic_service_area.py                  │
│  ├── c004_asgn_timed_visit_date_assignment.py              │
│  ├── c005_asgn_workload_balance_optimization.py            │
│  ├── c006_asgn_geographic_clustering_optimization.py       │
│  ├── c007_asgn_patient_preference_matching.py              │
│  ├── c008_asgn_provider_capacity_management.py             │
│  ├── c009_asgn_continuity_of_care_optimization.py          │
│  │                                                         │
│  ├── c010_schd_timeslot_availability_validation.py         │
│  ├── c011_schd_appointment_overlap_prevention.py           │
│  ├── c012_schd_flexible_appointment_timing_optimization.py │
│  ├── c013_schd_healthcare_task_sequencing.py               │
│  ├── c014_schd_route_travel_time_optimization.py           │
│  ├── c015_schd_timed_appointment_pinning.py                │
│  └── c016_schd_route_optimization.py                       │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 Constraint Registration Flow

```
┌─────────────────────────────────────────────────────────────┐
│                CONSTRAINT REGISTRATION FLOW                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  STAGE 1: Assignment Constraints                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  assignment_constraints.py                              │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ @constraint_provider                                │ │ │
│  │  │ def define_constraints(factory):                    │ │ │
│  │  │     constraints = []                                │ │ │
│  │  │                                                     │ │ │
│  │  │     # Hard constraints                              │ │ │
│  │  │     constraints.extend(                             │ │ │
│  │  │         provider_skill_validation_constraints(      │ │ │
│  │  │             factory))                               │ │ │
│  │  │     constraints.extend(                             │ │ │
│  │  │         date_based_availability_constraints(        │ │ │
│  │  │             factory))                               │ │ │
│  │  │                                                     │ │ │
│  │  │     # Soft constraints                              │ │ │
│  │  │     constraints.extend(                             │ │ │
│  │  │         workload_balance_optimization_constraints(  │ │ │
│  │  │             factory))                               │ │ │
│  │  │                                                     │ │ │
│  │  │     return constraints                              │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  STAGE 2: Day Planning Constraints                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  day_constraints.py                                     │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ @constraint_provider                                │ │ │
│  │  │ def define_day_constraints(factory):                │ │ │
│  │  │     constraints = []                                │ │ │
│  │  │                                                     │ │ │
│  │  │     # Hard constraints                              │ │ │
│  │  │     constraints.extend(                             │ │ │
│  │  │         appointment_overlap_prevention_constraints( │ │ │
│  │  │             factory))                               │ │ │
│  │  │                                                     │ │ │
│  │  │     # Soft constraints                              │ │ │
│  │  │     constraints.extend(                             │ │ │
│  │  │         route_optimization_constraints(factory))    │ │ │
│  │  │                                                     │ │ │
│  │  │     return constraints                              │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4.3 Configuration Injection System

<augment_code_snippet path="src/constraints/base_constraints.py" mode="EXCERPT">
````python
def with_config(service_name, return_type='constraint'):
    """
    Decorator for constraints that need configuration.
    Automatically provides core_config, service_config, and combined_config.
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Runtime service override
            runtime_service = get_current_service_type()
            actual_service = runtime_service if runtime_service else service_name

            # Get configurations
            ConfigRegistry.set_current_service_context(actual_service)
            core_config = ConfigRegistry.get_core_config('scheduler')
            service_config = ConfigRegistry.get_service_config(actual_service)

            # Create combined config (service overrides core)
            combined_config = {**(core_config or {}), **(service_config or {})}

            # Call function with all configs
            return func(*args, core_config=core_config,
                       service_config=service_config,
                       combined_config=combined_config, **kwargs)
        return wrapper
    return decorator
````
</augment_code_snippet>

### 4.4 Constraint Implementation Pattern

<augment_code_snippet path="src/constraints/c001_asgn_provider_skill_validation.py" mode="EXCERPT">
````python
@with_config('skilled_nursing', return_type='constraint')
def provider_skill_validation(factory: ConstraintFactory,
                             core_config=None,
                             service_config=None,
                             combined_config=None,
                             **kwargs) -> Constraint:
    """Providers must have all required skills for their assigned appointments."""
    # Handle Timefold validation calls
    if factory is None:
        return factory.for_each(AppointmentAssignment).filter(
            lambda x: False).as_constraint("no_op_constraint")

    # Use configuration
    skill_validation_enabled = core_config.get('enable_skill_validation', True)
    if not skill_validation_enabled:
        return factory.for_each(AppointmentAssignment).filter(
            lambda x: False).as_constraint("skill_validation_disabled")

    return (
        factory.for_each(AppointmentAssignment)
        .filter(lambda assignment: (
            assignment.provider is not None and
            assignment.appointment_data.required_skills is not None and
            not _has_required_skills(assignment.provider,
                                   assignment.appointment_data.required_skills)
        ))
        .penalize(HardSoftScore.ONE_HARD, lambda assignment: 1)
        .as_constraint("Provider skill validation")
    )
````
</augment_code_snippet>

---

## 5. Configuration Management System

### 5.1 Hierarchical Configuration Architecture

```
┌─────────────────────────────────────────────────────────────┐
│              HIERARCHICAL CONFIGURATION SYSTEM             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  CONFIGURATION HIERARCHY                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  CORE CONFIG (scheduler.yml)                            │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ # Global settings                                   │ │ │
│  │  │ rolling_window_days: 7                              │ │ │
│  │  │ max_solving_time_seconds: 300                       │ │ │
│  │  │ enable_geographic_clustering: true                  │ │ │
│  │  │ enable_continuity_of_care: true                     │ │ │
│  │  │ enable_workload_balancing: true                     │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼ OVERRIDES                │ │
│  │  SERVICE CONFIG (skilled_nursing.yml)                   │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ # Service-specific settings                         │ │ │
│  │  │ service_type: skilled_nursing                       │ │ │
│  │  │ required_skills:                                    │ │ │
│  │  │   - "medication_administration"                     │ │ │
│  │  │   - "wound_care"                                    │ │ │
│  │  │ geographic_radius_miles: 25.0                       │ │ │
│  │  │ max_daily_appointments_per_provider: 8              │ │ │
│  │  │ continuity_weight: 0.8                              │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼ RUNTIME MERGE            │ │
│  │  COMBINED CONFIG                                        │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ # Merged at runtime (service overrides core)        │ │ │
│  │  │ rolling_window_days: 7              ← from core     │ │ │
│  │  │ service_type: skilled_nursing       ← from service  │ │ │
│  │  │ enable_continuity_of_care: true     ← from core     │ │ │
│  │  │ continuity_weight: 0.8              ← from service  │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 5.2 Configuration Integration with Provider Data

```
┌─────────────────────────────────────────────────────────────┐
│           CONFIGURATION + PROVIDER DATA INTEGRATION        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  RUNTIME CONFIGURATION FLOW                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  1. LOAD CONFIGURATIONS                                 │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ ConfigRegistry.load_configurations()            │ │ │
│  │     │ ├─ scheduler.yml → core_config                  │ │ │
│  │     │ ├─ skilled_nursing.yml → service_config         │ │ │
│  │     │ └─ behavioral_care.yml → service_config         │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  2. PROVIDER DATA INTEGRATION                           │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ Provider preferences override config defaults   │ │ │
│  │     │                                                 │ │ │
│  │     │ Provider: Sarah Johnson                         │ │ │
│  │     │ ├─ Service: skilled_nursing                     │ │ │
│  │     │ ├─ Preferred radius: 20 miles                   │ │ │
│  │     │ │  (overrides config: 25 miles)                 │ │ │
│  │     │ ├─ Max daily appointments: 6                    │ │ │
│  │     │ │  (overrides config: 8)                        │ │ │
│  │     │ └─ Continuity bonus: 120                        │ │ │
│  │     │    (overrides config: 100)                      │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  3. CONSTRAINT EVALUATION                               │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ @with_config('skilled_nursing')                 │ │ │
│  │     │ def geographic_clustering(factory,              │ │ │
│  │     │                          service_config,        │ │ │
│  │     │                          **kwargs):             │ │ │
│  │     │                                                 │ │ │
│  │     │     # Get base radius from config               │ │ │
│  │     │     base_radius = service_config.get(           │ │ │
│  │     │         'geographic_radius_miles', 25.0)        │ │ │
│  │     │                                                 │ │ │
│  │     │     def calculate_penalty(assignment):          │ │ │
│  │     │         provider = assignment.provider          │ │ │
│  │     │         # Use provider preference if available  │ │ │
│  │     │         radius = provider.preferences.          │ │ │
│  │     │                 preferred_radius or base_radius │ │ │
│  │     │         return distance_penalty(assignment,     │ │ │
│  │     │                               radius)           │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 5.3 Feature Toggle System

<augment_code_snippet path="config/scheduler.yml" mode="EXCERPT">
````yaml
# Feature Toggles - Control constraint activation
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true

# Day Planning Stage Feature Toggles
enable_healthcare_task_sequencing: true
enable_travel_time_optimization: true
enable_break_time_management: true
enable_route_optimization: true

# Advanced Features
enable_advanced_traffic_integration: true
````
</augment_code_snippet>

<augment_code_snippet path="src/constraints/c005_asgn_workload_balance_optimization.py" mode="EXCERPT">
````python
@with_config('skilled_nursing')
def workload_balance_optimization(factory, core_config=None, **kwargs):
    """Balance workload across providers."""

    # Check feature toggle
    if not core_config.get('enable_workload_balancing', True):
        return factory.for_each(AppointmentAssignment).filter(
            lambda x: False).as_constraint("workload_balancing_disabled")

    # Implementation continues...
````
</augment_code_snippet>

---

## 6. Scheduler Parameterization and Execution

### 6.1 Scheduler Architecture

<augment_code_snippet path="scheduler.py" mode="EXCERPT">
````python
class SchedulerMain:
    """Main scheduler class with command-line interface."""

    def __init__(self):
        self.config_registry = ConfigRegistry()
        self.assign_job = AssignAppointmentJob()
        self.day_plan_job = DayPlanJob()

    def run_assignment_job(self, service_type: str, date_range: DateRange):
        """Run Stage 1: Assignment optimization."""
        return self.assign_job.run(service_type, date_range)

    def run_day_plan_job(self, target_date: date):
        """Run Stage 2: Day planning optimization."""
        return self.day_plan_job.run(target_date)
````
</augment_code_snippet>

### 6.2 Command-Line Parameters

```
┌─────────────────────────────────────────────────────────────┐
│                 SCHEDULER COMMAND-LINE INTERFACE           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  EXECUTION MODES                                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  # Run assignment job once                              │ │
│  │  python -m scheduler --job assign                       │ │
│  │                     --service skilled_nursing           │ │
│  │                     --start-date 2024-07-01             │ │
│  │                     --end-date 2024-07-07               │ │
│  │                                                         │ │
│  │  # Run day planning job once                            │ │
│  │  python -m scheduler --job dayplan                      │ │
│  │                     --target-date 2024-07-01            │ │
│  │                                                         │ │
│  │  # Run both jobs in sequence                            │ │
│  │  python -m scheduler --job both                         │ │
│  │                     --service skilled_nursing           │ │
│  │                     --target-date 2024-07-01            │ │
│  │                                                         │ │
│  │  # Run as daemon (scheduled execution)                  │ │
│  │  python -m scheduler --mode daemon                      │ │
│  │                                                         │ │
│  │  # Use warm solver (faster startup)                     │ │
│  │  python -m scheduler --job assign                       │ │
│  │                     --warm-solver                       │ │
│  │                     --service skilled_nursing           │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 6.3 Execution Modes

```
┌─────────────────────────────────────────────────────────────┐
│                    EXECUTION MODE FLOWS                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ONCE MODE (Single execution)                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Start → Load Config → Run Job → Output Results → Exit  │ │
│  │                                                         │ │
│  │  Use cases:                                             │ │
│  │  • Manual optimization runs                             │ │
│  │  • Testing and development                              │ │
│  │  • Ad-hoc schedule adjustments                          │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  DAEMON MODE (Scheduled execution)                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Start → Load Config → Schedule Jobs → Wait → Execute   │ │
│  │    ▲                                           │        │ │
│  │    └───────────────────────────────────────────┘        │ │
│  │                                                         │ │
│  │  Schedule:                                              │ │
│  │  • 02:00 AM: Assignment job (nightly)                  │ │
│  │  • 06:00 AM: Day plan job (daily)                      │ │
│  │                                                         │ │
│  │  Use cases:                                             │ │
│  │  • Production deployment                                │ │
│  │  • Automated scheduling                                 │ │
│  │  • Continuous optimization                              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 6.4 Warm Solver Concept

```
┌─────────────────────────────────────────────────────────────┐
│                    WARM SOLVER OPTIMIZATION                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  COLD SOLVER (Default)                                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Start → Initialize → Load Data → Solve → Results       │ │
│  │          ├─ Create entities                             │ │
│  │          ├─ Set up constraints                          │ │
│  │          └─ Initialize solver                           │ │
│  │                                                         │ │
│  │  Time: ~30-60 seconds initialization                    │ │
│  │  Memory: Full data reload each time                     │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  WARM SOLVER (Optimized)                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Start → Load Cached State → Update Data → Solve        │ │
│  │          ├─ Reuse previous solution                     │ │
│  │          ├─ Incremental data updates                    │ │
│  │          └─ Continue from last state                    │ │
│  │                                                         │ │
│  │  Time: ~5-10 seconds initialization                     │ │
│  │  Memory: Incremental updates only                       │ │
│  │                                                         │ │
│  │  Benefits:                                              │ │
│  │  • Faster convergence                                   │ │
│  │  • Better solution quality                              │ │
│  │  • Reduced resource usage                               │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 7. Service Integration and Event Architecture

### 7.1 Hybrid Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                 HYBRID SERVICE ARCHITECTURE                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  PARALLEL INPUT PROCESSING                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  SYNCHRONOUS (REST API)        ASYNCHRONOUS (Events)    │ │
│  │  ┌─────────────────────────┐   ┌─────────────────────────┐ │ │
│  │  │ External System         │   │ RabbitMQ Event Bus      │ │ │
│  │  │ ┌─────────────────────┐ │   │ ┌─────────────────────┐ │ │ │
│  │  │ │ POST /api/schedule  │ │   │ │ CRITICAL Queue      │ │ │ │
│  │  │ │ {                   │ │   │ │ HIGH Queue          │ │ │ │
│  │  │ │   "operation":      │ │   │ │ NORMAL Queue        │ │ │ │
│  │  │ │     "assign",       │ │   │ │ LOW Queue           │ │ │ │
│  │  │ │   "service_type":   │ │   │ └─────────────────────┘ │ │ │
│  │  │ │     "skilled_nursing"│ │   └─────────────────────────┘ │ │
│  │  │ │ }                   │ │                               │ │
│  │  │ └─────────────────────┘ │                               │ │
│  │  └─────────────────────────┘                               │ │
│  │              │                              │               │ │
│  │              ▼                              ▼               │ │
│  │  ┌─────────────────────────────────────────────────────────┐ │ │
│  │  │            SCHEDULER SERVICE                            │ │ │
│  │  │  ┌─────────────────────────────────────────────────────┐ │ │ │
│  │  │  │ • Process immediate requests                        │ │ │ │
│  │  │  │ • Handle background events                          │ │ │ │
│  │  │  │ • Coordinate solver execution                       │ │ │ │
│  │  │  │ • Manage warm solver state                          │ │ │ │
│  │  │  └─────────────────────────────────────────────────────┘ │ │ │
│  │  └─────────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 7.2 Service Client System

<augment_code_snippet path="src/api/service_clients.py" mode="EXCERPT">
````python
class ServiceClientFactory:
    """Factory for creating service clients with mock/production modes."""

    @staticmethod
    def create_staff_client(use_mock: bool = False) -> StaffServiceClient:
        if use_mock:
            return MockStaffServiceClient()
        return StaffServiceClient(ServiceConfig(
            base_url="https://api.careaxl.com/staff",
            api_key=os.getenv("STAFF_API_KEY")
        ))

    @staticmethod
    def create_patient_client(use_mock: bool = False) -> PatientServiceClient:
        if use_mock:
            return MockPatientServiceClient()
        return PatientServiceClient(ServiceConfig(
            base_url="https://api.careaxl.com/patients",
            api_key=os.getenv("PATIENT_API_KEY")
        ))
````
</augment_code_snippet>

### 7.3 Event System Integration

<augment_code_snippet path="src/events/rabbitmq_integration.py" mode="EXCERPT">
````python
class EventType(Enum):
    """Event types for the scheduling system."""
    # Appointment events
    APPOINTMENT_CREATED = "appointment.created"
    APPOINTMENT_UPDATED = "appointment.updated"
    APPOINTMENT_CANCELLED = "appointment.cancelled"
    APPOINTMENT_ASSIGNED = "appointment.assigned"

    # Provider events
    PROVIDER_AVAILABILITY_CHANGED = "provider.availability.changed"
    PROVIDER_SCHEDULE_UPDATED = "provider.schedule.updated"

    # Optimization events
    SCHEDULE_OPTIMIZATION_REQUESTED = "schedule.optimization.requested"
    SCHEDULE_OPTIMIZATION_COMPLETED = "schedule.optimization.completed"

class EventPriority(Enum):
    """Event priority levels for queue routing."""
    CRITICAL = "critical"  # Immediate processing required
    HIGH = "high"         # Process within minutes
    NORMAL = "normal"     # Process within hours
    LOW = "low"          # Process when resources available
````
</augment_code_snippet>

### 7.4 Event Processing Flow

```
┌─────────────────────────────────────────────────────────────┐
│                   EVENT PROCESSING FLOW                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  TYPICAL SCENARIO: Provider Availability Change             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  1. EXTERNAL SYSTEM EVENT                               │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ Staff Service: Provider availability updated    │ │ │
│  │     │ Event: PROVIDER_AVAILABILITY_CHANGED            │ │ │
│  │     │ Priority: HIGH                                  │ │ │
│  │     │ Data: {                                         │ │ │
│  │     │   provider_id: "PROV001",                       │ │ │
│  │     │   affected_dates: ["2024-07-01", "2024-07-02"] │ │ │
│  │     │ }                                               │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  2. EVENT HANDLER PROCESSING                            │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ async def handle_provider_change(event):        │ │ │
│  │     │     # Identify affected appointments            │ │ │
│  │     │     affected = find_affected_appointments(      │ │ │
│  │     │         event.data['provider_id'],              │ │ │
│  │     │         event.data['affected_dates']            │ │ │
│  │     │     )                                           │ │ │
│  │     │                                                 │ │ │
│  │     │     # Trigger incremental re-optimization       │ │ │
│  │     │     await trigger_optimization(                 │ │ │
│  │     │         optimization_type='incremental',        │ │ │
│  │     │         affected_appointments=affected          │ │ │
│  │     │     )                                           │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  3. OPTIMIZATION EXECUTION                              │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ • Load warm solver state                        │ │ │
│  │     │ • Update provider availability facts            │ │ │
│  │     │ • Re-optimize affected assignments              │ │ │
│  │     │ • Publish optimization results                  │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  4. RESULT PROPAGATION                                  │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ • Update external systems                       │ │ │
│  │     │ • Send provider notifications                   │ │ │
│  │     │ • Log optimization metrics                      │ │ │
│  │     │ • Publish completion events                     │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 8. Daily Solver Operations

### 8.1 Daily Execution Schedule

```
┌─────────────────────────────────────────────────────────────┐
│                   DAILY SOLVER EXECUTION                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  DAILY SCHEDULE                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  02:00 AM - Assignment Job (Stage 1)                    │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ • Process new appointment requests                  │ │ │
│  │  │ • Assign providers and dates                        │ │ │
│  │  │ • Optimize for next 7 days (rolling window)         │ │ │
│  │  │ • Output: Provider/date assignments                 │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  06:00 AM - Day Plan Job (Stage 2)                      │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ • Load today's assignments from Stage 1             │ │ │
│  │  │ • Assign specific time slots                        │ │ │
│  │  │ • Optimize visit orders and routes                  │ │ │
│  │  │ • Output: Complete daily schedules                  │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 8.2 Time Slot Assignment Process

<augment_code_snippet path="day_plan.py" mode="EXCERPT">
````python
def _solve_time_assignment_problem(self, day_schedule: DaySchedule) -> DaySchedule:
    """Solve the time slot assignment problem using Timefold."""
    logger.info(f"Solving time assignment problem with {len(day_schedule.time_assignments)} appointments")

    # Create solver configuration
    solver_config = SolverConfig(
        solution_class=DaySchedule,
        entity_class_list=[TimeSlotAssignment],
        score_director_factory_config=ScoreDirectorFactoryConfig(
            constraint_provider_function=define_day_constraints
        ),
        termination_config=TerminationConfig(
            spent_limit=Duration(seconds=60)  # Shorter timeout for daily planning
        )
    )

    # Create and run solver
    solver_factory = SolverFactory.create(solver_config)
    solver = solver_factory.build_solver()
    solution = solver.solve(day_schedule)

    return solution
````
</augment_code_snippet>

### 8.3 Visit Order Optimization

```
┌─────────────────────────────────────────────────────────────┐
│                 VISIT ORDER OPTIMIZATION                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  OPTIMIZATION FACTORS                                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  GEOGRAPHIC OPTIMIZATION                                │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Algorithm: Nearest Neighbor with Constraints        │ │ │
│  │  │                                                     │ │ │
│  │  │ 1. Start with timed appointments (fixed)            │ │ │
│  │  │ 2. For each remaining appointment:                  │ │ │
│  │  │    ├─ Calculate travel time from current location   │ │ │
│  │  │    ├─ Consider appointment duration                 │ │ │
│  │  │    ├─ Check time slot availability                  │ │ │
│  │  │    └─ Select nearest feasible appointment           │ │ │
│  │  │ 3. Repeat until all appointments scheduled          │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  HEALTHCARE TASK SEQUENCING                             │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Clinical workflow optimization:                     │ │ │
│  │  │ ├─ Medication administration early in day           │ │ │
│  │  │ ├─ Assessments before treatments                    │ │ │
│  │  │ ├─ Wound care after assessments                     │ │ │
│  │  │ ├─ IV therapy in controlled time windows            │ │ │
│  │  │ └─ Follow-up visits after primary care             │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  BREAK TIME MANAGEMENT                                  │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Break requirements:                                 │ │ │
│  │  │ ├─ Lunch break: 30-60 minutes around midday        │ │ │
│  │  │ ├─ Short breaks: 15 minutes every 3 hours          │ │ │
│  │  │ ├─ Travel buffer: 5-10 minutes between appointments │ │ │
│  │  │ └─ Documentation time: 10 minutes after each visit │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 8.4 Route Planning and Travel Time

<augment_code_snippet path="src/constraints/c014_schd_route_travel_time_optimization.py" mode="EXCERPT">
````python
def calculate_travel_time(from_location, to_location, departure_time=None):
    """Calculate travel time between locations."""
    # Basic model (fallback)
    distance = haversine_distance(from_location, to_location)

    # Determine area type and speed
    if is_urban_area(from_location):
        speed = 25  # mph in urban areas
    elif is_suburban_area(from_location):
        speed = 35  # mph in suburban areas
    else:
        speed = 45  # mph in rural areas

    # Apply time-of-day factors
    if departure_time and is_rush_hour(departure_time):
        speed *= 0.6  # 40% slower during rush hour

    return (distance / speed) * 60  # minutes
````
</augment_code_snippet>

---

## 9. Adding New Constraints

### 9.1 Real-World Example: Patient Preference Matching

**Scenario**: Patients prefer specific providers based on previous positive experiences. The system should prioritize these preferences while maintaining feasible assignments.

**Business Rule**: When a patient has worked with a provider before and rated the experience positively, that provider should be preferred for future appointments.

### 9.2 Constraint Implementation

<augment_code_snippet path="src/constraints/c007_asgn_patient_preference_matching.py" mode="EXCERPT">
````python
"""
Patient Preference Matching Constraint (C007)

Real-world scenario: Mrs. Johnson (patient) has diabetes and requires regular
wound care visits. She has worked with Nurse Sarah three times and feels
comfortable with her care approach. The system should prioritize assigning
Sarah to Mrs. Johnson's future appointments when possible.
"""

@with_config('skilled_nursing', return_type='constraint')
def patient_provider_preference_bonus(factory: ConstraintFactory,
                                    core_config=None,
                                    service_config=None,
                                    **kwargs) -> Constraint:
    """Reward assignments that match patient provider preferences."""

    if factory is None:
        return factory.for_each(AppointmentAssignment).filter(
            lambda x: False).as_constraint("no_op_constraint")

    # Get preference bonus from config
    preference_bonus = service_config.get('preferred_provider_bonus', 40)

    def calculate_preference_bonus(assignment):
        """Calculate bonus for preferred provider assignments."""
        if not assignment.provider or not assignment.appointment_data:
            return 0

        patient_id = assignment.appointment_data.patient_id
        provider_id = assignment.provider.id

        # Check if this provider is preferred by the patient
        if is_preferred_provider(patient_id, provider_id):
            return preference_bonus

        return 0

    return (
        factory.for_each(AppointmentAssignment)
        .filter(lambda assignment: (
            assignment.provider is not None and
            assignment.appointment_data is not None
        ))
        .reward(HardSoftScore.ONE_SOFT, calculate_preference_bonus)
        .as_constraint("Patient provider preference bonus")
    )

def is_preferred_provider(patient_id: str, provider_id: str) -> bool:
    """Check if provider is preferred by patient based on history."""
    # In real implementation, this would query patient history database
    # For demo, using mock data
    patient_preferences = {
        "patient_001": ["provider_sarah", "provider_john"],
        "patient_002": ["provider_mary"],
        "patient_003": ["provider_sarah", "provider_bob"]
    }

    return provider_id in patient_preferences.get(patient_id, [])
````
</augment_code_snippet>

### 9.3 Configuration Integration

<augment_code_snippet path="config/skilled_nursing.yml" mode="EXCERPT">
````yaml
# Patient preference settings
preferred_provider_bonus: 40
preferred_time_bonus: 25
preferred_location_bonus: 20

# Enable patient preference matching
enable_patient_preferences: true
````
</augment_code_snippet>

### 9.4 Testing and Validation

<augment_code_snippet path="tests/test_patient_preferences.py" mode="EXCERPT">
````python
def test_patient_preference_matching():
    """Test that patient preferences are properly considered."""

    # Setup test data
    patient = Consumer(id="patient_001", name="Mrs. Johnson")
    preferred_provider = Provider(id="provider_sarah", name="Sarah", role="RN")
    other_provider = Provider(id="provider_john", name="John", role="RN")

    appointment = AppointmentData(
        id="apt_001",
        patient_id="patient_001",
        service_type="skilled_nursing",
        required_skills=["wound_care"]
    )

    # Create assignments
    preferred_assignment = AppointmentAssignment(
        id="assign_001",
        appointment_data=appointment,
        provider=preferred_provider,
        assigned_date=date(2024, 7, 1)
    )

    other_assignment = AppointmentAssignment(
        id="assign_002",
        appointment_data=appointment,
        provider=other_provider,
        assigned_date=date(2024, 7, 1)
    )

    # Test constraint evaluation
    constraint = patient_provider_preference_bonus(constraint_factory)

    # Preferred provider should get bonus
    preferred_score = evaluate_constraint(constraint, preferred_assignment)
    other_score = evaluate_constraint(constraint, other_assignment)

    assert preferred_score > other_score
    assert preferred_score == 40  # Expected bonus value
````
</augment_code_snippet>

---

## 10. External System Integration

### 10.1 Integration Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                EXTERNAL SYSTEM INTEGRATION                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  API ENDPOINTS (Inbound)                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  POST /api/v1/schedule/assign                           │ │
│  │  ├─ Trigger assignment optimization                     │ │
│  │  └─ Response: assignment results and statistics         │ │
│  │                                                         │ │
│  │  POST /api/v1/schedule/dayplan                          │ │
│  │  ├─ Trigger day planning optimization                   │ │
│  │  └─ Response: time slot assignments and routes          │ │
│  │                                                         │ │
│  │  GET /api/v1/schedule/status                            │ │
│  │  └─ Response: solver state, queue status, metrics      │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  EXTERNAL SERVICE APIs (Outbound)                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Staff Service API                                      │ │
│  │  ├─ GET /api/staff/providers                            │ │
│  │  ├─ GET /api/staff/availability/{provider_id}           │ │
│  │  └─ PUT /api/staff/assignments                          │ │
│  │                                                         │ │
│  │  Patient Service API                                    │ │
│  │  ├─ GET /api/patients                                   │ │
│  │  ├─ GET /api/patients/{id}/location                     │ │
│  │  └─ GET /api/patients/{id}/preferences                  │ │
│  │                                                         │ │
│  │  Appointment Service API                                │ │
│  │  ├─ GET /api/appointments                               │ │
│  │  ├─ POST /api/appointments                              │ │
│  │  └─ PUT /api/appointments/{id}/schedule                 │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 10.2 Best Practices

```
┌─────────────────────────────────────────────────────────────┐
│                 INTEGRATION BEST PRACTICES                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ERROR HANDLING AND RESILIENCE                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Circuit Breaker Pattern                                │ │
│  │  ├─ Monitor external service health                     │ │
│  │  ├─ Fail fast when services are down                    │ │
│  │  ├─ Automatic recovery detection                        │ │
│  │  └─ Graceful degradation with cached data               │ │
│  │                                                         │ │
│  │  Retry Logic                                            │ │
│  │  ├─ Exponential backoff for transient failures         │ │
│  │  ├─ Maximum retry limits                                │ │
│  │  ├─ Different strategies per error type                 │ │
│  │  └─ Dead letter queues for failed events               │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  MONITORING AND OBSERVABILITY                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Metrics Collection                                     │ │
│  │  ├─ API response times and success rates               │ │
│  │  ├─ Event processing latency                           │ │
│  │  ├─ Queue depths and processing rates                  │ │
│  │  └─ Optimization performance metrics                   │ │
│  │                                                         │ │
│  │  Health Checks                                          │ │
│  │  ├─ Service health endpoints                           │ │
│  │  ├─ Dependency health monitoring                       │ │
│  │  ├─ Automated alerting on failures                     │ │
│  │  └─ Dashboard visualization                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## Conclusion

The CareAXL Scheduling Engine provides a robust, scalable solution for healthcare appointment optimization through:

**Key Architecture Benefits:**
- **Two-Stage Design**: Separates strategic (provider/date) from tactical (time/route) decisions
- **Constraint-Driven**: Flexible business rule implementation with configuration injection
- **Hybrid Integration**: Combines synchronous APIs with asynchronous event processing
- **Warm Solver**: Optimized performance through state persistence

**Production Readiness:**
- Comprehensive error handling and resilience patterns
- Monitoring and observability built-in
- Scalable event-driven architecture
- Extensible constraint system for future requirements

**Maintenance and Extension:**
- Clear patterns for adding new constraints
- Hierarchical configuration management
- Comprehensive testing framework
- Professional documentation and examples

This system is designed for production healthcare environments where reliability, performance, and compliance are critical requirements.
