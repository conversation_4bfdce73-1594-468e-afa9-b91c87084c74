# CareAXL Scheduling Engine: Comprehensive System Guide

## Table of Contents
1. [Timefold Constraint Solver Fundamentals](#1-timefold-constraint-solver-fundamentals)
2. [Two-Stage Optimization Architecture](#2-two-stage-optimization-architecture)
3. [Domain Models and Planning Entities](#3-domain-models-and-planning-entities)
4. [Constraint System Architecture](#4-constraint-system-architecture)
5. [Configuration Management System](#5-configuration-management-system)
6. [Scheduler Parameterization and Execution](#6-scheduler-parameterization-and-execution)
7. [Service Integration and Event Architecture](#7-service-integration-and-event-architecture)
8. [Daily Solver Operations](#8-daily-solver-operations)
9. [Adding New Constraints](#9-adding-new-constraints)
10. [External System Integration](#10-external-system-integration)

---

## 1. Timefold Constraint Solver Fundamentals

### 1.1 What is Timefold?

Timefold is an open-source constraint solver that automates complex planning and optimization problems. It uses AI-powered algorithms to find optimal solutions while satisfying business rules (constraints).

```
┌─────────────────────────────────────────────────────────────┐
│                    TIMEFOLD SOLVER PROCESS                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  INPUT DATA          SOLVER ENGINE           OUTPUT         │
│  ┌─────────────┐    ┌─────────────────┐    ┌─────────────┐  │
│  │ Planning    │───▶│ Constraint      │───▶│ Optimized   │  │
│  │ Entities    │    │ Evaluation      │    │ Solution    │  │
│  │             │    │                 │    │             │  │
│  │ Problem     │    │ Search          │    │ Score:      │  │
│  │ Facts       │    │ Algorithms      │    │ -2hard/     │  │
│  │             │    │                 │    │ -150soft    │  │
│  │ Value       │    │ Score           │    │             │  │
│  │ Ranges      │    │ Calculation     │    │             │  │
│  └─────────────┘    └─────────────────┘    └─────────────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 Core Timefold Concepts

#### Planning Entities vs Problem Facts

```
┌─────────────────────────────────────────────────────────────┐
│                  TIMEFOLD DATA CLASSIFICATION               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  PLANNING ENTITIES              PROBLEM FACTS               │
│  (Can be changed by solver)     (Fixed input data)         │
│                                                             │
│  ┌─────────────────────────┐    ┌─────────────────────────┐ │
│  │ @planning_entity        │    │ @problem_fact           │ │
│  │ AppointmentAssignment   │    │ Provider                │ │
│  │ ┌─────────────────────┐ │    │ ┌─────────────────────┐ │ │
│  │ │ @planning_variable  │ │    │ │ - id                │ │ │
│  │ │ provider: Provider  │ │    │ │ - name              │ │ │
│  │ │                     │ │    │ │ - skills            │ │ │
│  │ │ @planning_variable  │ │    │ │ - availability      │ │ │
│  │ │ assigned_date: Date │ │    │ │ - location          │ │ │
│  │ └─────────────────────┘ │    │ └─────────────────────┘ │ │
│  └─────────────────────────┘    │                         │ │
│                                 │ AppointmentData         │ │
│  TimeSlotAssignment             │ ┌─────────────────────┐ │ │
│  ┌─────────────────────────┐    │ │ - id                │ │ │
│  │ @planning_variable      │    │ │ - patient_id        │ │ │
│  │ time_slot: TimeSlot     │    │ │ - service_type      │ │ │
│  └─────────────────────────┘    │ │ - required_skills   │ │ │
│                                 │ │ - location          │ │ │
│                                 │ └─────────────────────┘ │ │
│                                 └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### Planning Variables and Value Ranges

```
┌─────────────────────────────────────────────────────────────┐
│                   PLANNING VARIABLE SYSTEM                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  PLANNING ENTITY: AppointmentAssignment                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  @planning_variable                                     │ │
│  │  provider: Provider = None  ◄─────┐                    │ │
│  │                                   │                    │ │
│  │  @planning_variable               │                    │ │
│  │  assigned_date: Date = None  ◄────┼─────┐              │ │
│  │                                   │     │              │ │
│  └───────────────────────────────────┼─────┼──────────────┘ │
│                                      │     │                │
│  VALUE RANGE PROVIDERS               │     │                │
│  ┌─────────────────────────────────┐ │     │                │
│  │ @value_range_provider           │ │     │                │
│  │ def available_providers():      │ │     │                │
│  │   return [                      │ │     │                │
│  │     Provider("John", ["RN"]),   │─┘     │                │
│  │     Provider("Jane", ["LPN"]),  │       │                │
│  │     Provider("Bob", ["CNA"])    │       │                │
│  │   ]                             │       │                │
│  └─────────────────────────────────┘       │                │
│                                            │                │
│  ┌─────────────────────────────────┐       │                │
│  │ @value_range_provider           │       │                │
│  │ def available_dates():          │       │                │
│  │   return [                      │       │                │
│  │     Date(2024, 7, 1),          │───────┘                │
│  │     Date(2024, 7, 2),          │                        │
│  │     Date(2024, 7, 3)           │                        │
│  │   ]                             │                        │
│  └─────────────────────────────────┘                        │
└─────────────────────────────────────────────────────────────┘
```

### 1.3 Constraint Types and Scoring

```
┌─────────────────────────────────────────────────────────────┐
│                    CONSTRAINT SCORING SYSTEM               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  HARD CONSTRAINTS (Must be satisfied)                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ ❌ VIOLATION = INFEASIBLE SOLUTION                      │ │
│  │                                                         │ │
│  │ • Provider must have required skills                   │ │
│  │ • Provider must be available on assigned date          │ │
│  │ • No double-booking of providers                       │ │
│  │ • Timed appointments must be on requested date         │ │
│  │                                                         │ │
│  │ Score Impact: -1 hard per violation                    │ │
│  │ Example: -3hard/0soft (3 hard constraint violations)   │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  SOFT CONSTRAINTS (Optimization preferences)               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ ⚠️  VIOLATION = SUBOPTIMAL BUT ACCEPTABLE               │ │
│  │                                                         │ │
│  │ • Minimize travel time between appointments            │ │
│  │ • Balance workload across providers                    │ │
│  │ • Prefer continuity of care (same provider)            │ │
│  │ • Respect patient preferences                          │ │
│  │                                                         │ │
│  │ Score Impact: -N soft per violation                    │ │
│  │ Example: 0hard/-150soft (optimal hard, 150 soft penalty)│ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  SOLUTION COMPARISON                                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Solution A: -1hard/-50soft  ◄── INFEASIBLE             │ │
│  │ Solution B:  0hard/-200soft ◄── FEASIBLE               │ │
│  │ Solution C:  0hard/-100soft ◄── BETTER (WINNER)        │ │
│  │                                                         │ │
│  │ Ranking: C > B > A                                     │ │
│  │ (Hard constraints always take priority)                │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.4 Timefold Annotations Reference

```
┌─────────────────────────────────────────────────────────────┐
│                  TIMEFOLD ANNOTATIONS GUIDE                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  CLASS-LEVEL ANNOTATIONS                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ @planning_solution                                      │ │
│  │ ├─ Marks the main solution container                    │ │
│  │ └─ Example: AppointmentSchedule, DaySchedule            │ │
│  │                                                         │ │
│  │ @planning_entity                                        │ │
│  │ ├─ Marks classes that can be changed by solver         │ │
│  │ └─ Example: AppointmentAssignment, TimeSlotAssignment  │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  FIELD-LEVEL ANNOTATIONS                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ @planning_id                                            │ │
│  │ ├─ Unique identifier for planning objects               │ │
│  │ └─ Required for all entities and facts                  │ │
│  │                                                         │ │
│  │ @planning_variable                                      │ │
│  │ ├─ Fields that solver can modify                        │ │
│  │ └─ Must have corresponding @value_range_provider        │ │
│  │                                                         │ │
│  │ @value_range_provider                                   │ │
│  │ ├─ Provides possible values for planning variables     │ │
│  │ └─ Returns list of valid options                        │ │
│  │                                                         │ │
│  │ @planning_score                                         │ │
│  │ ├─ Holds the solution's constraint violation score     │ │
│  │ └─ Type: HardSoftScore                                  │ │
│  │                                                         │ │
│  │ @problem_fact_collection_property                       │ │
│  │ ├─ Collections of immutable input data                  │ │
│  │ └─ Example: providers, appointments, time_slots        │ │
│  │                                                         │ │
│  │ @planning_entity_collection_property                    │ │
│  │ ├─ Collections of planning entities                     │ │
│  │ └─ Example: assignment_list, time_slot_assignments     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 2. Two-Stage Optimization Architecture

### 2.1 Architecture Overview

The CareAXL Scheduling Engine uses a sophisticated two-stage optimization approach to handle the complexity of healthcare scheduling:

```
┌─────────────────────────────────────────────────────────────┐
│                    TWO-STAGE ARCHITECTURE                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  INPUT DATA                                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • Appointments (patient requests)                       │ │
│  │ • Providers (staff with skills, availability)          │ │
│  │ • Configurations (business rules, constraints)         │ │
│  │ • Geographic data (locations, travel times)            │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    STAGE 1                              │ │
│  │              ASSIGNMENT SOLVER                          │ │
│  │                                                         │ │
│  │  DECIDES:                    CONSTRAINTS:               │ │
│  │  • Which provider?           • Skill matching          │ │
│  │  • Which date?               • Availability            │ │
│  │                              • Geographic limits       │ │
│  │  OUTPUT:                     • Workload balance        │ │
│  │  AppointmentAssignment       • Continuity of care      │ │
│  │  ├─ appointment_data         • Patient preferences     │ │
│  │  ├─ provider: Provider                                 │ │
│  │  └─ assigned_date: Date                                │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    STAGE 2                              │ │
│  │               DAYPLAN SOLVER                            │ │
│  │                                                         │ │
│  │  DECIDES:                    CONSTRAINTS:               │ │
│  │  • What time slot?           • No double-booking       │ │
│  │  • Visit order?              • Travel time optimization│ │
│  │                              • Appointment sequencing  │ │
│  │  OUTPUT:                     • Break management        │ │
│  │  TimeSlotAssignment          • Route optimization      │ │
│  │  ├─ scheduled_appointment                               │ │
│  │  ├─ time_slot: TimeSlot                                │ │
│  │  └─ visit_order: int                                   │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 FINAL SCHEDULE                          │ │
│  │  • Provider assignments with dates                     │ │
│  │  • Time slots and visit orders                         │ │
│  │  • Optimized routes and travel times                   │ │
│  │  • Constraint satisfaction scores                      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Stage 1: Assignment Solver Deep Dive

```
┌─────────────────────────────────────────────────────────────┐
│                  ASSIGNMENT SOLVER PROCESS                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  INPUT TRANSFORMATION                                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ AppointmentData                                         │ │
│  │ ┌─────────────────┐    TRANSFORM    ┌─────────────────┐ │ │
│  │ │ id: "APT001"    │    ────────▶    │ AppointmentAssignment│ │
│  │ │ patient_id      │                 │ ┌─────────────────┐ │ │
│  │ │ service_type    │                 │ │ id: "APT001"    │ │ │
│  │ │ required_skills │                 │ │ appointment_data│ │ │
│  │ │ location        │                 │ │ provider: None  │ │ │
│  │ │ timing          │                 │ │ assigned_date:  │ │ │
│  │ └─────────────────┘                 │ │   None          │ │ │
│  │                                     │ └─────────────────┘ │ │
│  │                                     └─────────────────────┘ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  CONSTRAINT EVALUATION PROCESS                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  For each AppointmentAssignment:                        │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ 1. HARD CONSTRAINTS (Must pass)                    │ │ │
│  │  │    ├─ Provider has required skills?                │ │ │
│  │  │    ├─ Provider available on date?                  │ │ │
│  │  │    ├─ Within geographic service area?              │ │ │
│  │  │    └─ Timed appointment on correct date?           │ │ │
│  │  │                                                    │ │ │
│  │  │ 2. SOFT CONSTRAINTS (Optimization)                 │ │ │
│  │  │    ├─ Workload balance across providers            │ │ │
│  │  │    ├─ Geographic clustering efficiency             │ │ │
│  │  │    ├─ Patient preference satisfaction              │ │ │
│  │  │    ├─ Continuity of care bonus                     │ │ │
│  │  │    └─ Provider capacity utilization                │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ROLLING WINDOW CONCEPT                                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Today: July 1st    Rolling Window: 7 days             │ │
│  │  ┌─────┬─────┬─────┬─────┬─────┬─────┬─────┐            │ │
│  │  │ 7/1 │ 7/2 │ 7/3 │ 7/4 │ 7/5 │ 7/6 │ 7/7 │            │ │
│  │  └─────┴─────┴─────┴─────┴─────┴─────┴─────┘            │ │
│  │    ▲                                       ▲            │ │
│  │    │                                       │            │ │
│  │  Timed appointments                   Flexible          │ │
│  │  must be on                          appointments       │ │
│  │  requested date                      can be moved       │ │
│  │                                                         │ │
│  │  Benefits:                                              │ │
│  │  • Better workload distribution                        │ │
│  │  • Improved geographic clustering                      │ │
│  │  • Higher provider utilization                         │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.3 Stage 2: Day Plan Solver Deep Dive

```
┌─────────────────────────────────────────────────────────────┐
│                   DAYPLAN SOLVER PROCESS                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  INPUT: Assignments from Stage 1                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Provider: John Smith (RN)    Date: July 1st            │ │
│  │ ┌─────────────────────────────────────────────────────┐ │ │
│  │ │ Appointment A: Patient 001 (Wound Care)            │ │ │
│  │ │ Appointment B: Patient 002 (Medication)            │ │ │
│  │ │ Appointment C: Patient 003 (Assessment)            │ │ │
│  │ │ Appointment D: Patient 004 (IV Therapy)            │ │ │
│  │ └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  TIME SLOT ASSIGNMENT PROCESS                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Available Time Slots (8 AM - 5 PM)                    │ │
│  │  ┌─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┐ │ │
│  │  │ 8AM │ 9AM │10AM │11AM │12PM │ 1PM │ 2PM │ 3PM │ 4PM │ │ │
│  │  └─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┘ │ │
│  │                                                         │ │
│  │  Constraint Considerations:                             │ │
│  │  ├─ No double-booking (hard)                           │ │
│  │  ├─ Timed appointments pinned (hard)                   │ │
│  │  ├─ Travel time between locations (soft)               │ │
│  │  ├─ Healthcare task sequencing (soft)                  │ │
│  │  ├─ Break time management (soft)                       │ │
│  │  └─ Route optimization (soft)                          │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  OPTIMIZED DAILY SCHEDULE                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Provider: John Smith (RN)    Date: July 1st            │ │
│  │ ┌─────────────────────────────────────────────────────┐ │ │
│  │ │  8:00 AM - Patient 002 (Medication) [Timed]        │ │ │
│  │ │  9:00 AM - Travel to Patient 001                   │ │ │
│  │ │  9:30 AM - Patient 001 (Wound Care)                │ │ │
│  │ │ 10:30 AM - Travel to Patient 003                   │ │ │
│  │ │ 11:00 AM - Patient 003 (Assessment)                │ │ │
│  │ │ 12:00 PM - Lunch Break                             │ │ │
│  │ │  1:00 PM - Travel to Patient 004                   │ │ │
│  │ │  1:30 PM - Patient 004 (IV Therapy)                │ │ │
│  │ │  2:30 PM - Return to base                          │ │ │
│  │ └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │ Optimization Results:                                   │ │
│  │ • Total travel time: 45 minutes                        │ │
│  │ • Route efficiency: 92%                                │ │
│  │ • All constraints satisfied                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 3. Domain Models and Planning Entities

### 3.1 Model Architecture Overview

The system uses a clear separation between domain models (business data) and planning models (Timefold optimization entities):

```
┌─────────────────────────────────────────────────────────────┐
│                    MODEL ARCHITECTURE                       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  DOMAIN MODELS                    PLANNING MODELS           │
│  (Business Logic)                 (Timefold Optimization)   │
│                                                             │
│  ┌─────────────────────────┐    ┌─────────────────────────┐ │
│  │ src/model/domain.py     │    │ src/model/planning_     │ │
│  │                         │    │ models.py               │ │
│  │ • Provider              │    │                         │ │
│  │ • Consumer              │    │ • AppointmentAssignment │ │
│  │ • AppointmentData       │    │ • AppointmentSchedule   │ │
│  │ • Location              │    │ • TimeSlotAssignment    │ │
│  │ • ServiceConfig         │    │ • DaySchedule           │ │
│  │ • SchedulerConfig       │    │ • DateRange             │ │
│  │ • Availability          │    │                         │ │
│  │ • Preferences           │    │ @planning_entity        │ │
│  │                         │    │ @planning_solution      │ │
│  │ @dataclass              │    │ @planning_variable      │ │
│  │ @problem_fact           │    │ @value_range_provider   │ │
│  └─────────────────────────┘    └─────────────────────────┘ │
│              │                              ▲               │
│              │                              │               │
│              └──────────── USES ────────────┘               │
│                                                             │
│  RELATIONSHIP: Planning models contain and reference        │
│  domain models but add Timefold-specific annotations       │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Core Domain Models

```
┌─────────────────────────────────────────────────────────────┐
│                     CORE DOMAIN MODELS                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  PROVIDER MODEL                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ @dataclass                                              │ │
│  │ class Provider:                                         │ │
│  │   id: str                    ← Unique identifier        │ │
│  │   name: str                  ← Display name             │ │
│  │   role: str                  ← "RN", "LPN", "CNA"       │ │
│  │   skills: List[str]          ← ["wound_care", "iv"]     │ │
│  │   location: Location         ← Home base location       │ │
│  │   availability: ProviderAvailability                    │ │
│  │   capacity: ProviderCapacity                            │ │
│  │   preferences: ProviderPreferences                      │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  APPOINTMENT DATA MODEL                                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ @dataclass                                              │ │
│  │ class AppointmentData:                                  │ │
│  │   id: str                    ← Unique identifier        │ │
│  │   patient_id: str            ← Patient reference        │ │
│  │   service_type: str          ← "skilled_nursing"        │ │
│  │   required_skills: List[str] ← Skills needed            │ │
│  │   location: Location         ← Patient location         │ │
│  │   timing: AppointmentTiming  ← Timing constraints       │ │
│  │   relationships: AppointmentRelationships               │ │
│  │   status: AppointmentStatus                             │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  LOCATION MODEL                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ @dataclass                                              │ │
│  │ class Location:                                         │ │
│  │   address: str               ← Full address             │ │
│  │   city: str                  ← City name                │ │
│  │   state: str                 ← State code               │ │
│  │   zip_code: str              ← ZIP code                 │ │
│  │   latitude: float            ← GPS coordinates          │ │
│  │   longitude: float           ← GPS coordinates          │ │
│  │   geofence: Optional[Geofence] ← Service area           │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3.3 Planning Models (Timefold Entities)

```
┌─────────────────────────────────────────────────────────────┐
│                    PLANNING MODELS                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  APPOINTMENT ASSIGNMENT (Stage 1 Planning Entity)          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ @planning_entity                                        │ │
│  │ @dataclass                                              │ │
│  │ class AppointmentAssignment:                            │ │
│  │   id: Annotated[str, PlanningId]                       │ │
│  │   appointment_data: AppointmentData  ← Domain model     │ │
│  │                                                         │ │
│  │   # PLANNING VARIABLES (Solver decides these)          │ │
│  │   provider: Annotated[                                 │ │
│  │     Optional[Provider],                                │ │
│  │     PlanningVariable                                   │ │
│  │   ] = None                                             │ │
│  │                                                         │ │
│  │   assigned_date: Annotated[                            │ │
│  │     Optional[date],                                    │ │
│  │     PlanningVariable                                   │ │
│  │   ] = None                                             │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  TIME SLOT ASSIGNMENT (Stage 2 Planning Entity)            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ @planning_entity                                        │ │
│  │ @dataclass                                              │ │
│  │ class TimeSlotAssignment:                               │ │
│  │   id: Annotated[str, PlanningId]                       │ │
│  │   scheduled_appointment: ScheduledAppointment           │ │
│  │                                                         │ │
│  │   # PLANNING VARIABLES (Solver decides these)          │ │
│  │   time_slot: Annotated[                                │ │
│  │     Optional[TimeSlot],                                │ │
│  │     PlanningVariable                                   │ │
│  │   ] = None                                             │ │
│  │                                                         │ │
│  │   visit_order: Annotated[                              │ │
│  │     Optional[int],                                     │ │
│  │     PlanningVariable                                   │ │
│  │   ] = None                                             │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3.4 Solution Containers

```
┌─────────────────────────────────────────────────────────────┐
│                    SOLUTION CONTAINERS                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  APPOINTMENT SCHEDULE (Stage 1 Solution)                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ @planning_solution                                      │ │
│  │ @dataclass                                              │ │
│  │ class AppointmentSchedule:                              │ │
│  │                                                         │ │
│  │   # PROBLEM FACTS (Input data)                         │ │
│  │   providers: Annotated[                                │ │
│  │     List[Provider],                                    │ │
│  │     ProblemFactCollectionProperty                      │ │
│  │   ]                                                    │ │
│  │                                                         │ │
│  │   appointments: Annotated[                             │ │
│  │     List[AppointmentData],                             │ │
│  │     ProblemFactCollectionProperty                      │ │
│  │   ]                                                    │ │
│  │                                                         │ │
│  │   date_range: Annotated[                               │ │
│  │     DateRange,                                         │ │
│  │     ProblemFactProperty                                │ │
│  │   ]                                                    │ │
│  │                                                         │ │
│  │   # PLANNING ENTITIES (What solver changes)            │ │
│  │   assignment_list: Annotated[                          │ │
│  │     List[AppointmentAssignment],                       │ │
│  │     PlanningEntityCollectionProperty                   │ │
│  │   ]                                                    │ │
│  │                                                         │ │
│  │   # SOLUTION SCORE                                      │ │
│  │   score: Annotated[                                    │ │
│  │     HardSoftScore,                                     │ │
│  │     PlanningScore                                      │ │
│  │   ] = field(default=None)                              │ │
│  │                                                         │ │
│  │   # VALUE RANGE PROVIDERS                               │ │
│  │   @value_range_provider                                │ │
│  │   def get_provider_range(self):                        │ │
│  │     return self.providers                              │ │
│  │                                                         │ │
│  │   @value_range_provider                                │ │
│  │   def get_date_range(self):                            │ │
│  │     return self.date_range.get_dates()                 │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  DAY SCHEDULE (Stage 2 Solution)                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ @planning_solution                                      │ │
│  │ @dataclass                                              │ │
│  │ class DaySchedule:                                      │ │
│  │                                                         │ │
│  │   # PROBLEM FACTS                                       │ │
│  │   scheduled_appointments: Annotated[                   │ │
│  │     List[ScheduledAppointment],                        │ │
│  │     ProblemFactCollectionProperty                      │ │
│  │   ]                                                    │ │
│  │                                                         │ │
│  │   time_slots: Annotated[                               │ │
│  │     List[TimeSlot],                                    │ │
│  │     ProblemFactCollectionProperty                      │ │
│  │   ]                                                    │ │
│  │                                                         │ │
│  │   # PLANNING ENTITIES                                   │ │
│  │   time_slot_assignments: Annotated[                    │ │
│  │     List[TimeSlotAssignment],                          │ │
│  │     PlanningEntityCollectionProperty                   │ │
│  │   ]                                                    │ │
│  │                                                         │ │
│  │   # SOLUTION SCORE                                      │ │
│  │   score: Annotated[HardSoftScore, PlanningScore]       │ │
│  │                                                         │ │
│  │   # VALUE RANGE PROVIDERS                               │ │
│  │   @value_range_provider                                │ │
│  │   def get_time_slot_range(self):                       │ │
│  │     return self.time_slots                             │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3.5 Model Relationships and Data Flow

```
┌─────────────────────────────────────────────────────────────┐
│                   MODEL RELATIONSHIPS                       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  STAGE 1: ASSIGNMENT FLOW                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  AppointmentData ──┐                                    │ │
│  │  (Domain Model)    │                                    │ │
│  │                    ▼                                    │ │
│  │  AppointmentAssignment ──┐                              │ │
│  │  (Planning Entity)       │                              │ │
│  │                          ▼                              │ │
│  │  AppointmentSchedule ────────▶ SOLVER ──▶ SOLUTION      │ │
│  │  (Solution Container)                                   │ │
│  │                                                         │ │
│  │  Provider ──────────────────┘                           │ │
│  │  (Problem Fact)                                         │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼ TRANSFORM                    │
│  STAGE 2: DAY PLANNING FLOW                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  ScheduledAppointment ──┐                               │ │
│  │  (From Stage 1)         │                               │ │
│  │                         ▼                               │ │
│  │  TimeSlotAssignment ────┐                               │ │
│  │  (Planning Entity)      │                               │ │
│  │                         ▼                               │ │
│  │  DaySchedule ───────────────▶ SOLVER ──▶ FINAL SCHEDULE │ │
│  │  (Solution Container)                                   │ │
│  │                                                         │ │
│  │  TimeSlot ──────────────────┘                           │ │
│  │  (Problem Fact)                                         │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 4. Constraint System Architecture

### 4.1 Constraint Organization Structure

The constraint system is organized into modular, maintainable components with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                  CONSTRAINT SYSTEM STRUCTURE               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  src/constraints/                                           │
│  ├─ assignment_constraints.py    ← Stage 1 coordinator      │
│  ├─ day_constraints.py           ← Stage 2 coordinator      │
│  ├─ base_constraints.py          ← Common utilities         │
│  ├─ config_registry.py           ← Configuration system     │
│  │                                                          │
│  ├─ ASSIGNMENT CONSTRAINTS (C001-C009)                     │
│  ├─ c001_asgn_provider_skill_validation.py                 │
│  ├─ c002_asgn_date_based_availability.py                   │
│  ├─ c003_asgn_geographic_service_area.py                   │
│  ├─ c004_asgn_timed_visit_date_assignment.py               │
│  ├─ c005_asgn_workload_balance_optimization.py             │
│  ├─ c006_asgn_geographic_clustering_optimization.py        │
│  ├─ c007_asgn_patient_preference_matching.py               │
│  ├─ c008_asgn_provider_capacity_management.py              │
│  ├─ c009_asgn_continuity_of_care_optimization.py           │
│  │                                                          │
│  └─ DAY PLANNING CONSTRAINTS (C010-C016)                   │
│     ├─ c010_schd_timeslot_availability_validation.py       │
│     ├─ c011_schd_appointment_overlap_prevention.py         │
│     ├─ c012_schd_flexible_appointment_timing_optimization.py│
│     ├─ c013_schd_healthcare_task_sequencing.py             │
│     ├─ c014_schd_route_travel_time_optimization.py         │
│     ├─ c015_schd_timed_appointment_pinning.py              │
│     └─ c016_schd_route_optimization.py                     │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 Constraint Registration and Coordination

```
┌─────────────────────────────────────────────────────────────┐
│                 CONSTRAINT REGISTRATION FLOW               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  STAGE 1: ASSIGNMENT CONSTRAINTS                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ assignment_constraints.py                               │ │
│  │                                                         │ │
│  │ @constraint_provider                                    │ │
│  │ def define_constraints(constraint_factory):             │ │
│  │   constraints = []                                      │ │
│  │                                                         │ │
│  │   # HARD CONSTRAINTS (Always enabled)                  │ │
│  │   constraints.extend(                                  │ │
│  │     provider_skill_validation_constraints(factory)     │ │
│  │   )                                                    │ │
│  │   constraints.extend(                                  │ │
│  │     date_based_availability_constraints(factory)       │ │
│  │   )                                                    │ │
│  │   constraints.extend(                                  │ │
│  │     timed_visit_date_assignment_constraints(factory)   │ │
│  │   )                                                    │ │
│  │                                                         │ │
│  │   # SOFT CONSTRAINTS (Configurable)                    │ │
│  │   constraints.extend(                                  │ │
│  │     workload_balance_optimization_constraints(factory) │ │
│  │   )                                                    │ │
│  │   # Other soft constraints...                          │ │
│  │                                                         │ │
│  │   return constraints                                   │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  STAGE 2: DAY PLANNING CONSTRAINTS                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ day_constraints.py                                      │ │
│  │                                                         │ │
│  │ @constraint_provider                                    │ │
│  │ def define_day_constraints(constraint_factory):         │ │
│  │   constraints = []                                      │ │
│  │                                                         │ │
│  │   # HARD CONSTRAINTS                                    │ │
│  │   constraints.extend(                                  │ │
│  │     timeslot_availability_validation_constraints(...)  │ │
│  │   )                                                    │ │
│  │   constraints.extend(                                  │ │
│  │     appointment_overlap_prevention_constraints(...)    │ │
│  │   )                                                    │ │
│  │                                                         │ │
│  │   # SOFT CONSTRAINTS                                    │ │
│  │   constraints.extend(                                  │ │
│  │     route_travel_time_optimization_constraints(...)    │ │
│  │   )                                                    │ │
│  │   # Other constraints...                               │ │
│  │                                                         │ │
│  │   return constraints                                   │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4.3 Configuration Injection System

```
┌─────────────────────────────────────────────────────────────┐
│                CONFIGURATION INJECTION SYSTEM              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  @with_config DECORATOR MECHANISM                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  # In constraint file (e.g., c001_asgn_provider_skill_  │ │
│  │  # validation.py)                                       │ │
│  │                                                         │ │
│  │  @with_config('skilled_nursing')                        │ │
│  │  def provider_skill_validation(                         │ │
│  │      constraint_factory,                               │ │
│  │      core_config=None,                                 │ │
│  │      service_config=None,                              │ │
│  │      combined_config=None,                             │ │
│  │      **kwargs                                          │ │
│  │  ):                                                    │ │
│  │      # Access configuration values                     │ │
│  │      required_skills = service_config.get(             │ │
│  │          'required_skills', []                         │ │
│  │      )                                                 │ │
│  │      enable_role_hierarchy = service_config.get(       │ │
│  │          'enable_role_hierarchy', False                │ │
│  │      )                                                 │ │
│  │                                                         │ │
│  │      # Define constraint logic                         │ │
│  │      return constraint_factory.for_each(...)           │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  CONFIGURATION FLOW                                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  config/scheduler.yml        ┌─ CORE CONFIG             │ │
│  │  ├─ rolling_window_days: 7   │                         │ │
│  │  ├─ max_solving_time: 300    │                         │ │
│  │  └─ enable_*: true/false     │                         │ │
│  │                              │                         │ │
│  │  config/skilled_nursing.yml  ┌─ SERVICE CONFIG         │ │
│  │  ├─ required_skills: [...]   │                         │ │
│  │  ├─ continuity_weight: 0.8   │                         │ │
│  │  ├─ workload_balance: 0.7    │                         │ │
│  │  └─ role_hierarchy: {...}    │                         │ │
│  │                              │                         │ │
│  │                              ▼                         │ │
│  │  ConfigRegistry.load_configurations()                  │ │
│  │                              │                         │ │
│  │                              ▼                         │ │
│  │  @with_config decorator injects:                       │ │
│  │  ├─ core_config: scheduler.yml data                    │ │
│  │  ├─ service_config: service-specific data              │ │
│  │  └─ combined_config: merged configuration              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4.4 Constraint Implementation Examples

```
┌─────────────────────────────────────────────────────────────┐
│                 CONSTRAINT IMPLEMENTATION PATTERNS         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  HARD CONSTRAINT EXAMPLE: Provider Skill Validation        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  @with_config('skilled_nursing')                        │ │
│  │  def provider_skill_validation(                         │ │
│  │      constraint_factory,                               │ │
│  │      service_config=None,                              │ │
│  │      **kwargs                                          │ │
│  │  ):                                                    │ │
│  │      """Provider must have ALL required skills."""     │ │
│  │                                                         │ │
│  │      def has_required_skills(assignment):              │ │
│  │          if not assignment.provider:                   │ │
│  │              return True  # Unassigned is OK           │ │
│  │                                                         │ │
│  │          required = assignment.appointment_data        │ │
│  │                      .required_skills                  │ │
│  │          provider_skills = assignment.provider.skills  │ │
│  │                                                         │ │
│  │          # Check if provider has ALL required skills   │ │
│  │          return all(skill in provider_skills           │ │
│  │                    for skill in required)             │ │
│  │                                                         │ │
│  │      return (constraint_factory                        │ │
│  │              .for_each(AppointmentAssignment)          │ │
│  │              .filter(lambda a: not has_required_skills(a))│ │
│  │              .penalize(HardSoftScore.ONE_HARD)         │ │
│  │              .as_constraint("Provider skill validation"))│ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  SOFT CONSTRAINT EXAMPLE: Workload Balance                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  @with_config('skilled_nursing')                        │ │
│  │  def workload_balance_optimization(                     │ │
│  │      constraint_factory,                               │ │
│  │      service_config=None,                              │ │
│  │      **kwargs                                          │ │
│  │  ):                                                    │ │
│  │      """Balance workload across providers."""          │ │
│  │                                                         │ │
│  │      # Get configuration                               │ │
│  │      target_daily = service_config.get(                │ │
│  │          'target_daily_appointments', 6                │ │
│  │      )                                                 │ │
│  │      weight = service_config.get(                      │ │
│  │          'workload_balance_weight', 0.7                │ │
│  │      )                                                 │ │
│  │                                                         │ │
│  │      def calculate_workload_penalty(assignment):       │ │
│  │          # Complex logic to calculate penalty          │ │
│  │          # based on provider's daily appointment count │ │
│  │          return penalty_value * weight                 │ │
│  │                                                         │ │
│  │      return (constraint_factory                        │ │
│  │              .for_each(AppointmentAssignment)          │ │
│  │              .filter(lambda a: a.provider is not None) │ │
│  │              .penalize(HardSoftScore.ONE_SOFT,         │ │
│  │                       calculate_workload_penalty)      │ │
│  │              .as_constraint("Workload balance"))       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4.5 Constraint Categories and Purposes

```
┌─────────────────────────────────────────────────────────────┐
│                   CONSTRAINT CATEGORIES                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ASSIGNMENT STAGE CONSTRAINTS                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  HARD CONSTRAINTS (Business Rules)                     │ │
│  │  ├─ C001: Provider Skill Validation                    │ │
│  │  │   └─ Provider must have ALL required skills         │ │
│  │  ├─ C002: Date-Based Availability                      │ │
│  │  │   └─ Provider must be available on assigned date    │ │
│  │  ├─ C003: Geographic Service Area                      │ │
│  │  │   └─ Appointment must be within provider's area     │ │
│  │  └─ C004: Timed Visit Date Assignment                  │ │
│  │      └─ Timed appointments must be on requested date   │ │
│  │                                                         │ │
│  │  SOFT CONSTRAINTS (Optimization Goals)                 │ │
│  │  ├─ C005: Workload Balance Optimization                │ │
│  │  │   └─ Distribute appointments evenly across providers│ │
│  │  ├─ C006: Geographic Clustering Optimization           │ │
│  │  │   └─ Group appointments by geographic proximity     │ │
│  │  ├─ C007: Patient Preference Matching                  │ │
│  │  │   └─ Assign preferred providers when possible       │ │
│  │  ├─ C008: Provider Capacity Management                 │ │
│  │  │   └─ Respect provider capacity limits               │ │
│  │  └─ C009: Continuity of Care Optimization              │ │
│  │      └─ Prefer same provider for ongoing care          │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  DAY PLANNING STAGE CONSTRAINTS                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  HARD CONSTRAINTS (Scheduling Rules)                   │ │
│  │  ├─ C010: Timeslot Availability Validation             │ │
│  │  │   └─ Provider must be available at assigned time    │ │
│  │  ├─ C011: Appointment Overlap Prevention               │ │
│  │  │   └─ No double-booking of providers                 │ │
│  │  ├─ C012: Flexible Appointment Timing                  │ │
│  │  │   └─ Respect appointment duration constraints       │ │
│  │  └─ C015: Timed Appointment Pinning                    │ │
│  │      └─ Timed appointments must be at exact time       │ │
│  │                                                         │ │
│  │  SOFT CONSTRAINTS (Route & Time Optimization)          │ │
│  │  ├─ C013: Healthcare Task Sequencing                   │ │
│  │  │   └─ Optimize order of healthcare tasks             │ │
│  │  ├─ C014: Route Travel Time Optimization               │ │
│  │  │   └─ Minimize travel time between appointments      │ │
│  │  └─ C016: Route Optimization                           │ │
│  │      └─ Optimize overall daily route efficiency        │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 5. Configuration Management System

### 5.1 Configuration Architecture Overview

The configuration system provides a hierarchical, service-aware approach to managing business rules and optimization parameters:

```
┌─────────────────────────────────────────────────────────────┐
│                 CONFIGURATION ARCHITECTURE                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  CONFIGURATION HIERARCHY                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  config/scheduler.yml          ← CORE CONFIG            │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ • rolling_window_days: 7                            │ │ │
│  │  │ • max_solving_time_seconds: 300                     │ │ │
│  │  │ • enable_geographic_clustering: true                │ │ │
│  │  │ • enable_continuity_of_care: true                   │ │ │
│  │  │ • traffic_integration: {...}                        │ │ │
│  │  │ • global_availability: {...}                        │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼ MERGED WITH              │ │
│  │  SERVICE-SPECIFIC CONFIGS    ▼                          │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ config/skilled_nursing.yml                          │ │ │
│  │  │ ├─ required_skills: ["medication", "wound_care"]    │ │ │
│  │  │ ├─ continuity_weight: 0.8                           │ │ │
│  │  │ ├─ workload_balance_weight: 0.7                     │ │ │
│  │  │ ├─ role_hierarchy: {"RN": ["LPN", "CNA"]}           │ │ │
│  │  │ └─ service_availability: {...}                      │ │ │
│  │  │                                                     │ │ │
│  │  │ config/behavioral_care.yml                          │ │ │
│  │  │ ├─ required_skills: ["counseling", "assessment"]    │ │ │
│  │  │ ├─ continuity_weight: 0.95                          │ │ │
│  │  │ ├─ patient_preference_weight: 0.8                   │ │ │
│  │  │ └─ specialized_constraints: {...}                   │ │ │
│  │  │                                                     │ │ │
│  │  │ config/hospital_at_home.yml                         │ │ │
│  │  │ config/pcs.yml                                      │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  RUNTIME CONFIGURATION INJECTION                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ ConfigRegistry                                          │ │
│  │ ├─ Thread-local service context                         │ │
│  │ ├─ Configuration merging logic                          │ │
│  │ ├─ @with_config decorator                               │ │
│  │ └─ Runtime service switching                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 5.2 Configuration Integration with Provider Data

```
┌─────────────────────────────────────────────────────────────┐
│            CONFIGURATION + PROVIDER DATA INTEGRATION       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  PROVIDER DATA SOURCES                                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  External Staff Service                                 │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Provider: John Smith                                │ │ │
│  │  │ ├─ id: "PROV001"                                    │ │ │
│  │  │ ├─ role: "RN"                                       │ │ │
│  │  │ ├─ skills: ["medication", "wound_care", "iv"]       │ │ │
│  │  │ ├─ location: {lat: 40.7128, lng: -74.0060}          │ │ │
│  │  │ ├─ availability: {...}                              │ │ │
│  │  │ └─ preferences: {...}                               │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼ COMBINED WITH                │
│  CONFIGURATION-DRIVEN CONSTRAINT LOGIC                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Skill Validation Constraint                            │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ @with_config('skilled_nursing')                     │ │ │
│  │  │ def provider_skill_validation(...):                 │ │ │
│  │  │                                                     │ │ │
│  │  │   # Get required skills from config                 │ │ │
│  │  │   required_skills = service_config.get(             │ │ │
│  │  │     'required_skills',                              │ │ │
│  │  │     ["medication", "wound_care"]                    │ │ │
│  │  │   )                                                 │ │ │
│  │  │                                                     │ │ │
│  │  │   # Check provider skills against requirements      │ │ │
│  │  │   def has_skills(assignment):                       │ │ │
│  │  │     provider_skills = assignment.provider.skills    │ │ │
│  │  │     return all(skill in provider_skills             │ │ │
│  │  │               for skill in required_skills)         │ │ │
│  │  │                                                     │ │ │
│  │  │   # Apply role hierarchy if enabled                 │ │ │
│  │  │   if service_config.get('enable_role_hierarchy'):   │ │ │
│  │  │     # Complex role hierarchy logic...               │ │ │
│  │  │                                                     │ │ │
│  │  │   return constraint_factory.for_each(...)           │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 5.3 Configuration Categories and Impact

```
┌─────────────────────────────────────────────────────────────┐
│                  CONFIGURATION CATEGORIES                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  SOLVER BEHAVIOR CONFIGURATION                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ scheduler.yml                                           │ │
│  │ ┌─────────────────────────────────────────────────────┐ │ │
│  │ │ rolling_window_days: 7                              │ │ │
│  │ │ ├─ Impact: How many days ahead to consider          │ │ │
│  │ │ └─ Used by: Assignment solver date range            │ │ │
│  │ │                                                     │ │ │
│  │ │ max_solving_time_seconds: 300                       │ │ │
│  │ │ ├─ Impact: Maximum time for optimization            │ │ │
│  │ │ └─ Used by: Timefold solver configuration           │ │ │
│  │ │                                                     │ │ │
│  │ │ enable_geographic_clustering: true                  │ │ │
│  │ │ ├─ Impact: Enable/disable geographic optimization   │ │ │
│  │ │ └─ Used by: C006 constraint activation              │ │ │
│  │ └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  BUSINESS RULE CONFIGURATION                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ skilled_nursing.yml                                     │ │
│  │ ┌─────────────────────────────────────────────────────┐ │ │
│  │ │ required_skills: ["medication", "wound_care"]       │ │ │
│  │ │ ├─ Impact: Defines mandatory provider skills        │ │ │
│  │ │ └─ Used by: C001 skill validation constraint        │ │ │
│  │ │                                                     │ │ │
│  │ │ max_daily_appointments_per_provider: 8              │ │ │
│  │ │ ├─ Impact: Provider workload limits                 │ │ │
│  │ │ └─ Used by: C008 capacity management constraint     │ │ │
│  │ │                                                     │ │ │
│  │ │ role_hierarchy: {"RN": ["LPN", "CNA"]}              │ │ │
│  │ │ ├─ Impact: Allow higher roles to do lower work     │ │ │
│  │ │ └─ Used by: C001 with role hierarchy penalties     │ │ │
│  │ └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  OPTIMIZATION WEIGHT CONFIGURATION                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Service-specific weight tuning                          │ │
│  │ ┌─────────────────────────────────────────────────────┐ │ │
│  │ │ continuity_weight: 0.8                              │ │ │
│  │ │ ├─ Impact: How much to prefer same provider         │ │ │
│  │ │ └─ Used by: C009 continuity constraint scoring      │ │ │
│  │ │                                                     │ │ │
│  │ │ workload_balance_weight: 0.7                        │ │ │
│  │ │ ├─ Impact: How much to balance provider workloads   │ │ │
│  │ │ └─ Used by: C005 workload balance constraint        │ │ │
│  │ │                                                     │ │ │
│  │ │ patient_preference_weight: 0.6                      │ │ │
│  │ │ ├─ Impact: How much to respect patient preferences  │ │ │
│  │ │ └─ Used by: C007 preference matching constraint     │ │ │
│  │ └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 5.4 Runtime Configuration Management

```
┌─────────────────────────────────────────────────────────────┐
│                RUNTIME CONFIGURATION FLOW                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  CONFIGURATION LOADING PROCESS                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  1. APPLICATION STARTUP                                 │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ ConfigRegistry.load_configurations('config/')   │ │ │
│  │     │                                                 │ │ │
│  │     │ # Load core configurations                      │ │ │
│  │     │ for config_file in ['scheduler.yml']:          │ │ │
│  │     │   config_data = yaml.safe_load(file)            │ │ │
│  │     │   register_core_config(name, config_data)       │ │ │
│  │     │                                                 │ │ │
│  │     │ # Load service configurations                   │ │ │
│  │     │ for service_file in ['skilled_nursing.yml',     │ │ │
│  │     │                      'behavioral_care.yml']:   │ │ │
│  │     │   config_data = yaml.safe_load(file)            │ │ │
│  │     │   register_service_config(name, config_data)    │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  2. CONSTRAINT EXECUTION TIME                           │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ @with_config('skilled_nursing')                 │ │ │
│  │     │ def constraint_function(...):                   │ │ │
│  │     │                                                 │ │ │
│  │     │   # Decorator automatically injects:            │ │ │
│  │     │   # - core_config: scheduler.yml data           │ │ │
│  │     │   # - service_config: skilled_nursing.yml data  │ │ │
│  │     │   # - combined_config: merged configuration     │ │ │
│  │     │                                                 │ │ │
│  │     │   # Access configuration values                 │ │ │
│  │     │   weight = service_config.get('continuity_weight')│ │ │
│  │     │   enabled = core_config.get('enable_continuity') │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  3. THREAD-LOCAL SERVICE CONTEXT                       │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ # For multi-service scenarios                   │ │ │
│  │     │ ConfigRegistry.set_current_service_context(     │ │ │
│  │     │   'behavioral_care'                             │ │ │
│  │     │ )                                               │ │ │
│  │     │                                                 │ │ │
│  │     │ # Constraints now use behavioral_care config    │ │ │
│  │     │ run_assignment_solver()                         │ │ │
│  │     │                                                 │ │ │
│  │     │ ConfigRegistry.clear_current_service_context()  │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 5.5 Configuration-Driven Feature Toggles

```
┌─────────────────────────────────────────────────────────────┐
│                  FEATURE TOGGLE SYSTEM                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  GLOBAL FEATURE TOGGLES (scheduler.yml)                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  # Assignment Stage Feature Toggles                    │ │
│  │  enable_geographic_clustering: true                     │ │
│  │  ├─ Controls: C006 constraint activation               │ │
│  │  └─ Impact: Geographic optimization on/off             │ │
│  │                                                         │ │
│  │  enable_continuity_of_care: true                       │ │
│  │  ├─ Controls: C009 constraint activation               │ │
│  │  └─ Impact: Same provider preference on/off            │ │
│  │                                                         │ │
│  │  enable_workload_balancing: true                       │ │
│  │  ├─ Controls: C005 constraint activation               │ │
│  │  └─ Impact: Provider workload distribution on/off      │ │
│  │                                                         │ │
│  │  # Day Planning Stage Feature Toggles                  │ │
│  │  enable_route_optimization: true                       │ │
│  │  ├─ Controls: C016 constraint activation               │ │
│  │  └─ Impact: Daily route optimization on/off            │ │
│  │                                                         │ │
│  │  enable_travel_time_optimization: true                 │ │
│  │  ├─ Controls: C014 constraint activation               │ │
│  │  └─ Impact: Travel time minimization on/off            │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  CONSTRAINT ACTIVATION LOGIC                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  # In assignment_constraints.py                        │ │
│  │  @constraint_provider                                  │ │
│  │  def define_constraints(constraint_factory):            │ │
│  │    constraints = []                                    │ │
│  │                                                         │ │
│  │    # Always enabled hard constraints                   │ │
│  │    constraints.extend(                                 │ │
│  │      provider_skill_validation_constraints(factory)    │ │
│  │    )                                                   │ │
│  │                                                         │ │
│  │    # Conditionally enabled soft constraints            │ │
│  │    core_config = ConfigRegistry.get_core_config(       │ │
│  │      'scheduler'                                       │ │
│  │    )                                                   │ │
│  │                                                         │ │
│  │    if core_config.get('enable_workload_balancing'):    │ │
│  │      constraints.extend(                               │ │
│  │        workload_balance_optimization_constraints(...)  │ │
│  │      )                                                 │ │
│  │                                                         │ │
│  │    if core_config.get('enable_continuity_of_care'):    │ │
│  │      constraints.extend(                               │ │
│  │        continuity_of_care_optimization_constraints(...)│ │
│  │      )                                                 │ │
│  │                                                         │ │
│  │    return constraints                                  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 6. Scheduler Parameterization and Execution

### 6.1 Scheduler.py Architecture

The `scheduler.py` file serves as the main orchestrator for the scheduling system, providing flexible execution modes and comprehensive parameterization:

```
┌─────────────────────────────────────────────────────────────┐
│                    SCHEDULER.PY ARCHITECTURE                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  CLASS STRUCTURE                                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  AppointmentScheduler                                   │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ __init__(config_folder, daemon_mode)                │ │ │
│  │  │ ├─ Load configurations via ConfigRegistry           │ │ │
│  │  │ ├─ Initialize AssignAppointmentJob                  │ │ │
│  │  │ ├─ Initialize DayPlanJob                            │ │ │
│  │  │ └─ Set daemon mode flag                             │ │ │
│  │  │                                                     │ │ │
│  │  │ EXECUTION METHODS                                   │ │ │
│  │  │ ├─ run_assign_appointments() → dict                 │ │ │
│  │  │ ├─ run_day_plan(target_date) → BatchAssignmentResult│ │ │
│  │  │ ├─ run_today_day_plan() → BatchAssignmentResult     │ │ │
│  │  │ ├─ run_tomorrow_day_plan() → BatchAssignmentResult  │ │ │
│  │  │ └─ run_once(job_type, target_date) → Union[...]     │ │ │
│  │  │                                                     │ │ │
│  │  │ DAEMON MODE METHODS                                 │ │ │
│  │  │ ├─ setup_schedule() → Configure job timing          │ │ │
│  │  │ ├─ run_daemon() → Continuous execution              │ │ │
│  │  │ └─ stop() → Graceful shutdown                       │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  COMMAND-LINE INTERFACE                                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ main() function with argparse                           │ │
│  │ ├─ --config-folder: Configuration directory path       │ │
│  │ ├─ --mode: 'daemon' or 'once' execution mode           │ │
│  │ ├─ --job: 'assign' or 'dayplan' job type               │ │
│  │ └─ --date: Target date for dayplan job (YYYY-MM-DD)    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 6.2 Command-Line Parameters and Usage

```
┌─────────────────────────────────────────────────────────────┐
│                  COMMAND-LINE PARAMETERS                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  BASIC USAGE PATTERNS                                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  # Run assignment job once                              │ │
│  │  python scheduler.py --mode once --job assign          │ │
│  │                                                         │ │
│  │  # Run day planning for specific date                   │ │
│  │  python scheduler.py --mode once --job dayplan \       │ │
│  │    --date 2024-07-01                                   │ │
│  │                                                         │ │
│  │  # Run day planning for today (default)                │ │
│  │  python scheduler.py --mode once --job dayplan         │ │
│  │                                                         │ │
│  │  # Run as daemon (continuous scheduling)               │ │
│  │  python scheduler.py --mode daemon                     │ │
│  │                                                         │ │
│  │  # Use custom configuration folder                     │ │
│  │  python scheduler.py --mode once --job assign \        │ │
│  │    --config-folder /path/to/custom/config              │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  PARAMETER DETAILS                                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  --config-folder (optional)                            │ │
│  │  ├─ Default: 'config'                                  │ │
│  │  ├─ Purpose: Specify configuration directory           │ │
│  │  └─ Example: --config-folder /opt/scheduler/config     │ │
│  │                                                         │ │
│  │  --mode (required)                                      │ │
│  │  ├─ Options: 'daemon', 'once'                          │ │
│  │  ├─ daemon: Continuous operation with scheduled jobs   │ │
│  │  └─ once: Single job execution and exit                │ │
│  │                                                         │ │
│  │  --job (required for 'once' mode)                      │ │
│  │  ├─ Options: 'assign', 'dayplan'                       │ │
│  │  ├─ assign: Run assignment solver (Stage 1)            │ │
│  │  └─ dayplan: Run day planning solver (Stage 2)         │ │
│  │                                                         │ │
│  │  --date (optional for 'dayplan' job)                   │ │
│  │  ├─ Format: YYYY-MM-DD                                 │ │
│  │  ├─ Default: Today's date                              │ │
│  │  └─ Example: --date 2024-07-15                         │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 6.3 Execution Modes Deep Dive

```
┌─────────────────────────────────────────────────────────────┐
│                     EXECUTION MODES                        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ONCE MODE (Single Execution)                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  ASSIGNMENT JOB FLOW                                    │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ 1. Load configurations                              │ │ │
│  │  │ 2. Initialize AssignAppointmentJob                  │ │ │
│  │  │ 3. Load appointment data                            │ │ │
│  │  │ 4. Load provider data                               │ │ │
│  │  │ 5. Create planning entities                         │ │ │
│  │  │ 6. Run Timefold solver                              │ │ │
│  │  │ 7. Process results                                  │ │ │
│  │  │ 8. Return summary statistics                        │ │ │
│  │  │ 9. Exit application                                 │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  DAYPLAN JOB FLOW                                       │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ 1. Load configurations                              │ │ │
│  │  │ 2. Initialize DayPlanJob                            │ │ │
│  │  │ 3. Load scheduled appointments for target date      │ │ │
│  │  │ 4. Generate time slots                              │ │ │
│  │  │ 5. Create time slot assignments                     │ │ │
│  │  │ 6. Run Timefold solver                              │ │ │
│  │  │ 7. Optimize routes and timing                       │ │ │
│  │  │ 8. Return BatchAssignmentResult                     │ │ │
│  │  │ 9. Exit application                                 │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  DAEMON MODE (Continuous Operation)                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  SCHEDULED OPERATIONS                                   │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Daily at 02:00 AM                                   │ │ │
│  │  │ ├─ Run assignment job                               │ │ │
│  │  │ ├─ Process all pending appointments                 │ │ │
│  │  │ ├─ Assign providers and dates                       │ │ │
│  │  │ └─ Log results and continue                         │ │ │
│  │  │                                                     │ │ │
│  │  │ Daily at 06:00 AM                                   │ │ │
│  │  │ ├─ Run day planning job for today                   │ │ │
│  │  │ ├─ Assign time slots and routes                     │ │ │
│  │  │ ├─ Optimize daily schedules                         │ │ │
│  │  │ └─ Log results and continue                         │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  DAEMON CHARACTERISTICS                                 │ │
│  │  ├─ Warm solver state (keeps optimization context)     │ │ │
│  │  ├─ Event-driven updates (reacts to data changes)      │ │ │
│  │  ├─ Graceful shutdown handling (Ctrl+C)                │ │ │
│  │  ├─ Continuous logging and monitoring                  │ │ │
│  │  └─ Memory-efficient operation                         │ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 6.4 Warm Solver and State Management

```
┌─────────────────────────────────────────────────────────────┐
│                   WARM SOLVER CONCEPT                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  COLD SOLVER vs WARM SOLVER                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  COLD SOLVER (Once Mode)                                │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ 1. Load all data from scratch                       │ │ │
│  │  │ 2. Initialize solver state                          │ │ │
│  │  │ 3. Run optimization                                 │ │ │
│  │  │ 4. Return results                                   │ │ │
│  │  │ 5. Discard solver state                             │ │ │
│  │  │ 6. Exit                                             │ │ │
│  │  │                                                     │ │ │
│  │  │ Pros: Simple, clean state                           │ │ │
│  │  │ Cons: Slow startup, no incremental updates         │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  WARM SOLVER (Daemon Mode)                              │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ 1. Load initial data                                │ │ │
│  │  │ 2. Initialize solver state                          │ │ │
│  │  │ 3. Keep solver in memory                            │ │ │
│  │  │ 4. Process incremental updates                      │ │ │
│  │  │    ├─ New appointments                              │ │ │
│  │  │    ├─ Provider availability changes                 │ │ │
│  │  │    ├─ Appointment cancellations                     │ │ │
│  │  │    └─ Configuration updates                         │ │ │
│  │  │ 5. Continuous optimization                          │ │ │
│  │  │ 6. Event-driven re-optimization                     │ │ │
│  │  │                                                     │ │ │
│  │  │ Pros: Fast updates, real-time optimization         │ │ │
│  │  │ Cons: Memory usage, state management complexity    │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  DAEMON MODE IMPLEMENTATION                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  def __init__(self, config_folder, daemon_mode=False):  │ │
│  │    # Load configurations                                │ │
│  │    ConfigRegistry.load_configurations(config_folder)    │ │
│  │                                                         │ │
│  │    # Initialize jobs with daemon mode flag             │ │
│  │    self.assign_job = AssignAppointmentJob(              │ │
│  │      config_folder, daemon_mode=daemon_mode             │ │
│  │    )                                                    │ │
│  │    self.day_plan_job = DayPlanJob(                      │ │
│  │      config_folder, daemon_mode=daemon_mode             │ │
│  │    )                                                    │ │
│  │                                                         │ │
│  │    # Daemon mode enables:                               │ │
│  │    # - Persistent solver state                          │ │
│  │    # - Incremental problem updates                      │ │
│  │    # - Event-driven re-optimization                     │ │
│  │    # - Memory-efficient warm starts                     │ │
│  │    self.daemon_mode = daemon_mode                       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 6.5 Result Processing and Output

```
┌─────────────────────────────────────────────────────────────┐
│                    RESULT PROCESSING                       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ASSIGNMENT JOB RESULTS                                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Return Type: dict                                      │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ {                                                   │ │ │
│  │  │   'summary': {                                      │ │ │
│  │  │     'total_appointments': 25,                       │ │ │
│  │  │     'assigned_appointments': 23,                    │ │ │
│  │  │     'unassigned_appointments': 2,                   │ │ │
│  │  │     'assignment_rate': 0.92                         │ │ │
│  │  │   },                                                │ │ │
│  │  │   'processing_time': 45.67,                         │ │ │
│  │  │   'score': '0hard/-150soft',                        │ │ │
│  │  │   'assignments': [                                  │ │ │
│  │  │     {                                               │ │ │
│  │  │       'appointment_id': 'APT001',                   │ │ │
│  │  │       'provider_id': 'PROV001',                     │ │ │
│  │  │       'provider_name': 'John Smith',                │ │ │
│  │  │       'assigned_date': '2024-07-01',                │ │ │
│  │  │       'service_type': 'skilled_nursing'             │ │ │
│  │  │     },                                              │ │ │
│  │  │     # ... more assignments                          │ │ │
│  │  │   ],                                                │ │ │
│  │  │   'unassigned': [                                   │ │ │
│  │  │     {                                               │ │ │
│  │  │       'appointment_id': 'APT025',                   │ │ │
│  │  │       'reason': 'No provider with required skills' │ │ │
│  │  │     }                                               │ │ │
│  │  │   ]                                                 │ │ │
│  │  │ }                                                   │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  DAYPLAN JOB RESULTS                                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Return Type: BatchAssignmentResult                     │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ BatchAssignmentResult(                              │ │ │
│  │  │   total_appointments=15,                            │ │ │
│  │  │   assigned_appointments=14,                         │ │ │
│  │  │   unassigned_appointments=1,                        │ │ │
│  │  │   processing_time_seconds=23.45,                    │ │ │
│  │  │   score='0hard/-75soft',                            │ │ │
│  │  │   assignments=[                                     │ │ │
│  │  │     TimeSlotAssignment(                             │ │ │
│  │  │       id='TSA001',                                  │ │ │
│  │  │       scheduled_appointment=ScheduledAppointment,   │ │ │
│  │  │       time_slot=TimeSlot(start_time='08:00'),       │ │ │
│  │  │       visit_order=1                                 │ │ │
│  │  │     ),                                              │ │ │
│  │  │     # ... more time slot assignments               │ │ │
│  │  │   ],                                                │ │ │
│  │  │   optimization_metrics={                            │ │ │
│  │  │     'total_travel_time_minutes': 45,                │ │ │
│  │  │     'route_efficiency_percentage': 92.5,            │ │ │
│  │  │     'average_appointments_per_provider': 6.2        │ │ │
│  │  │   }                                                 │ │ │
│  │  │ )                                                   │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 7. Service Integration and Event Architecture

### 7.1 Hybrid Architecture Overview

The CareAXL Scheduling Engine implements a hybrid architecture combining synchronous service operations with asynchronous event processing:

```
┌─────────────────────────────────────────────────────────────┐
│                   HYBRID ARCHITECTURE                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  SYNCHRONOUS LAYER (Immediate Operations)                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  External Services          Service Clients             │ │
│  │  ┌─────────────────┐       ┌─────────────────────────┐   │ │
│  │  │ Staff Service   │◄──────┤ StaffServiceClient      │   │ │
│  │  │ - Providers     │       │ - get_providers()       │   │ │
│  │  │ - Availability  │       │ - get_provider(id)      │   │ │
│  │  │ - Skills        │       │ - update_availability() │   │ │
│  │  └─────────────────┘       └─────────────────────────┘   │ │
│  │                                                         │ │
│  │  ┌─────────────────┐       ┌─────────────────────────┐   │ │
│  │  │ Patient Service │◄──────┤ PatientServiceClient    │   │ │
│  │  │ - Patients      │       │ - get_patients()        │   │ │
│  │  │ - Locations     │       │ - get_patient(id)       │   │ │
│  │  │ - Preferences   │       │ - update_preferences()  │   │ │
│  │  └─────────────────┘       └─────────────────────────┘   │ │
│  │                                                         │ │
│  │  ┌─────────────────┐       ┌─────────────────────────┐   │ │
│  │  │ Appointment Svc │◄──────┤ AppointmentServiceClient│   │ │
│  │  │ - Appointments  │       │ - get_appointments()    │   │ │
│  │  │ - Scheduling    │       │ - create_appointment()  │   │ │
│  │  │ - Status        │       │ - update_status()       │   │ │
│  │  └─────────────────┘       └─────────────────────────┘   │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  SCHEDULING ENGINE                                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ SchedulerService                                        │ │
│  │ ├─ process_request(SchedulerRequest) → SchedulerResponse │ │
│  │ ├─ Immediate data operations                            │ │
│  │ ├─ Synchronous optimization runs                        │ │
│  │ └─ Direct API responses                                 │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  ASYNCHRONOUS LAYER (Background Processing)                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Event Bus (RabbitMQ)                                   │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Priority Queues:                                    │ │ │
│  │  │ ├─ CRITICAL: Emergency re-scheduling                │ │ │
│  │  │ ├─ HIGH: Provider availability changes              │ │ │
│  │  │ ├─ NORMAL: New appointments                         │ │ │
│  │  │ └─ LOW: Optimization improvements                   │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  Event Handlers                                         │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ OptimizationEventHandler                            │ │ │
│  │  │ ├─ handle_provider_change()                         │ │ │
│  │  │ ├─ handle_appointment_update()                      │ │ │
│  │  │ ├─ handle_schedule_optimization()                   │ │ │
│  │  │ └─ trigger_warm_solver_update()                     │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 7.2 Service Client Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                   SERVICE CLIENT SYSTEM                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  SERVICE CLIENT FACTORY PATTERN                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  ServiceClientFactory                                   │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ def __init__(self, config: Dict[str, ServiceConfig])│ │ │
│  │  │   self.config = config                              │ │ │
│  │  │                                                     │ │ │
│  │  │ def get_staff_client() -> StaffServiceClient:       │ │ │
│  │  │   return StaffServiceClient(                        │ │ │
│  │  │     self.config['staff_service']                    │ │ │
│  │  │   )                                                 │ │ │
│  │  │                                                     │ │ │
│  │  │ def get_patient_client() -> PatientServiceClient:   │ │ │
│  │  │   return PatientServiceClient(                      │ │ │
│  │  │     self.config['patient_service']                  │ │ │
│  │  │   )                                                 │ │ │
│  │  │                                                     │ │ │
│  │  │ def get_appointment_client():                       │ │ │
│  │  │   return AppointmentServiceClient(                  │ │ │
│  │  │     self.config['appointment_service']              │ │ │
│  │  │   )                                                 │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  MOCK vs PRODUCTION CLIENTS                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  DEVELOPMENT (Mock Clients)                             │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ MockStaffServiceClient                              │ │ │
│  │  │ ├─ Uses demo data from data_loader                  │ │ │
│  │  │ ├─ No external dependencies                         │ │ │
│  │  │ ├─ Fast response times                              │ │ │
│  │  │ └─ Predictable test data                            │ │ │
│  │  │                                                     │ │ │
│  │  │ MockPatientServiceClient                            │ │ │
│  │  │ MockAppointmentServiceClient                        │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  PRODUCTION (Real Clients)                              │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ StaffServiceClient                                  │ │ │
│  │  │ ├─ HTTP requests to Staff Service API               │ │ │
│  │  │ ├─ Authentication and authorization                 │ │ │
│  │  │ ├─ Error handling and retries                       │ │ │
│  │  │ └─ Response caching                                 │ │ │
│  │  │                                                     │ │ │
│  │  │ PatientServiceClient                                │ │ │
│  │  │ AppointmentServiceClient                            │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 7.3 Event System and RabbitMQ Integration

```
┌─────────────────────────────────────────────────────────────┐
│                    EVENT SYSTEM ARCHITECTURE               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  EVENT TYPES AND PRIORITIES                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  EventType Enumeration                                  │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ # Provider Events                                   │ │ │
│  │  │ PROVIDER_CREATED                                    │ │ │
│  │  │ PROVIDER_UPDATED                                    │ │ │
│  │  │ PROVIDER_TERMINATED                                 │ │ │
│  │  │ PROVIDER_AVAILABILITY_CHANGED                       │ │ │
│  │  │ PROVIDER_SKILL_CHANGED                              │ │ │
│  │  │                                                     │ │ │
│  │  │ # Patient Events                                    │ │ │
│  │  │ PATIENT_CREATED                                     │ │ │
│  │  │ PATIENT_UPDATED                                     │ │ │
│  │  │ PATIENT_ADDRESS_CHANGED                             │ │ │
│  │  │ PATIENT_PREFERENCE_CHANGED                          │ │ │
│  │  │                                                     │ │ │
│  │  │ # Appointment Events                                │ │ │
│  │  │ APPOINTMENT_CREATED                                 │ │ │
│  │  │ APPOINTMENT_UPDATED                                 │ │ │
│  │  │ APPOINTMENT_CANCELLED                               │ │ │
│  │  │ APPOINTMENT_RESCHEDULED                             │ │ │
│  │  │                                                     │ │ │
│  │  │ # Optimization Events                               │ │ │
│  │  │ SCHEDULE_OPTIMIZATION_REQUESTED                     │ │ │
│  │  │ SCHEDULE_OPTIMIZATION_COMPLETED                     │ │ │
│  │  │ CONSTRAINT_VIOLATION_DETECTED                       │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  EventPriority Levels                                   │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ CRITICAL: Emergency situations                      │ │ │
│  │  │ ├─ Provider termination                             │ │ │
│  │  │ ├─ Appointment cancellation                         │ │ │
│  │  │ └─ System failures                                  │ │ │
│  │  │                                                     │ │ │
│  │  │ HIGH: Important changes                             │ │ │
│  │  │ ├─ Provider availability changes                    │ │ │
│  │  │ ├─ Patient address changes                          │ │ │
│  │  │ └─ Appointment rescheduling                         │ │ │
│  │  │                                                     │ │ │
│  │  │ NORMAL: Regular operations                          │ │ │
│  │  │ ├─ New appointments                                 │ │ │
│  │  │ ├─ Provider updates                                 │ │ │
│  │  │ └─ Patient preference changes                       │ │ │
│  │  │                                                     │ │ │
│  │  │ LOW: Optimization improvements                      │ │ │
│  │  │ ├─ Schedule optimization requests                   │ │ │
│  │  │ ├─ Performance tuning                               │ │ │
│  │  │ └─ Analytics updates                                │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 7.4 Event Processing Flow

```
┌─────────────────────────────────────────────────────────────┐
│                   EVENT PROCESSING FLOW                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  EVENT PUBLICATION                                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  External Service Change                                │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Staff Service: Provider availability updated        │ │ │
│  │  │ ├─ Provider ID: PROV001                             │ │ │
│  │  │ ├─ New availability: Mon-Fri 8AM-5PM                │ │ │
│  │  │ └─ Effective date: 2024-07-01                       │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  Event Publication                                      │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ await publish_event_rabbitmq(                       │ │ │
│  │  │   EventType.PROVIDER_AVAILABILITY_CHANGED,          │ │ │
│  │  │   {                                                 │ │ │
│  │  │     'provider_id': 'PROV001',                       │ │ │
│  │  │     'old_availability': {...},                      │ │ │
│  │  │     'new_availability': {...},                      │ │ │
│  │  │     'effective_date': '2024-07-01',                 │ │ │
│  │  │     'affected_appointments': ['APT001', 'APT002']   │ │ │
│  │  │   },                                                │ │ │
│  │  │   priority=EventPriority.HIGH,                      │ │ │
│  │  │   source='staff_service',                           │ │ │
│  │  │   correlation_id='CHANGE_001'                       │ │ │
│  │  │ )                                                   │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  EVENT PROCESSING                                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  RabbitMQ Queue Processing                              │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ 1. Event received in HIGH priority queue            │ │ │
│  │  │ 2. Event deserialized and validated                │ │ │
│  │  │ 3. Route to appropriate handler                     │ │ │
│  │  │ 4. Execute handler logic                            │ │ │
│  │  │ 5. Update warm solver state                         │ │ │
│  │  │ 6. Acknowledge event processing                     │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  Handler Execution                                      │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ OptimizationEventHandler.handle_provider_change()   │ │ │
│  │  │                                                     │ │ │
│  │  │ 1. Identify affected appointments                   │ │ │
│  │  │ 2. Check constraint violations                      │ │ │
│  │  │ 3. Update provider availability facts               │ │ │
│  │  │ 4. Trigger incremental re-optimization              │ │ │
│  │  │ 5. Notify affected stakeholders                     │ │ │
│  │  │ 6. Log optimization results                         │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 7.5 Service Integration Patterns

```
┌─────────────────────────────────────────────────────────────┐
│                SERVICE INTEGRATION PATTERNS                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  REQUEST-RESPONSE PATTERN (Synchronous)                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  API Request                                            │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ POST /api/v1/schedule/assign                        │ │ │
│  │  │ {                                                   │ │ │
│  │  │   "service_type": "skilled_nursing",                │ │ │
│  │  │   "target_date": "2024-07-01",                      │ │ │
│  │  │   "appointment_ids": ["APT001", "APT002"]           │ │ │
│  │  │ }                                                   │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  Scheduler Service Processing                           │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ 1. Validate request                                 │ │ │
│  │  │ 2. Fetch providers via StaffServiceClient           │ │ │
│  │  │ 3. Fetch appointments via AppointmentServiceClient  │ │ │
│  │  │ 4. Run assignment optimization                      │ │ │
│  │  │ 5. Return immediate results                         │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  API Response                                           │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ {                                                   │ │ │
│  │  │   "success": true,                                  │ │ │
│  │  │   "processing_time": 23.45,                         │ │ │
│  │  │   "assignments": [                                  │ │ │
│  │  │     {                                               │ │ │
│  │  │       "appointment_id": "APT001",                   │ │ │
│  │  │       "provider_id": "PROV001",                     │ │ │
│  │  │       "assigned_date": "2024-07-01"                 │ │ │
│  │  │     }                                               │ │ │
│  │  │   ]                                                 │ │ │
│  │  │ }                                                   │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  PUBLISH-SUBSCRIBE PATTERN (Asynchronous)                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Event Publication                                      │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ # From external service                             │ │ │
│  │  │ publish_event(                                      │ │ │
│  │  │   'APPOINTMENT_CREATED',                            │ │ │
│  │  │   appointment_data,                                 │ │ │
│  │  │   priority='NORMAL'                                 │ │ │
│  │  │ )                                                   │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  Background Processing                                  │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ 1. Event queued in RabbitMQ                         │ │ │
│  │  │ 2. Scheduler service processes when available       │ │ │
│  │  │ 3. Incremental optimization update                  │ │ │
│  │  │ 4. Warm solver state updated                        │ │ │
│  │  │ 5. No immediate response required                   │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 8. Daily Solver Operations

### 8.1 Daily Solver Execution Flow

The day plan solver runs daily to assign specific time slots and optimize visit orders for providers' daily schedules:

```
┌─────────────────────────────────────────────────────────────┐
│                   DAILY SOLVER EXECUTION                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  DAILY EXECUTION SCHEDULE                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  06:00 AM - Daily Day Plan Execution                    │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Target: Today's date                                │ │ │
│  │  │ Input: Assignments from previous night's run        │ │ │
│  │  │ Goal: Optimize time slots and visit orders          │ │ │
│  │  │ Output: Complete daily schedules for all providers  │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  STEP-BY-STEP PROCESS                                   │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ 1. Load Today's Assignments                         │ │ │
│  │  │    ├─ Query assignments for target date             │ │ │
│  │  │    ├─ Filter by provider availability               │ │ │
│  │  │    └─ Group by provider                             │ │ │
│  │  │                                                     │ │ │
│  │  │ 2. Generate Time Slots                              │ │ │
│  │  │    ├─ Create hourly slots (8 AM - 5 PM)             │ │ │
│  │  │    ├─ Account for provider shift patterns           │ │ │
│  │  │    └─ Reserve break times                           │ │ │
│  │  │                                                     │ │ │
│  │  │ 3. Create Planning Entities                         │ │ │
│  │  │    ├─ Transform assignments to ScheduledAppointments│ │ │
│  │  │    ├─ Create TimeSlotAssignment entities            │ │ │
│  │  │    └─ Set up value ranges                           │ │ │
│  │  │                                                     │ │ │
│  │  │ 4. Run Timefold Optimization                        │ │ │
│  │  │    ├─ Apply day planning constraints                │ │ │
│  │  │    ├─ Optimize for travel time                      │ │ │
│  │  │    ├─ Sequence healthcare tasks                     │ │ │
│  │  │    └─ Minimize route inefficiencies                 │ │ │
│  │  │                                                     │ │ │
│  │  │ 5. Process and Store Results                        │ │ │
│  │  │    ├─ Extract optimized time slots                  │ │ │
│  │  │    ├─ Calculate visit orders                        │ │ │
│  │  │    ├─ Generate route summaries                      │ │ │
│  │  │    └─ Update appointment statuses                   │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 8.2 Time Slot Assignment Process

```
┌─────────────────────────────────────────────────────────────┐
│                  TIME SLOT ASSIGNMENT PROCESS              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  INPUT: Provider's Daily Assignments                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Provider: Sarah Johnson (RN)    Date: July 1, 2024    │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Assignment 1: Patient A (Wound Care)               │ │ │
│  │  │ ├─ Duration: 45 minutes                             │ │ │
│  │  │ ├─ Location: 123 Main St                            │ │ │
│  │  │ ├─ Timing: Flexible                                 │ │ │
│  │  │ └─ Priority: Normal                                 │ │ │
│  │  │                                                     │ │ │
│  │  │ Assignment 2: Patient B (Medication)               │ │ │
│  │  │ ├─ Duration: 30 minutes                             │ │ │
│  │  │ ├─ Location: 456 Oak Ave                            │ │ │
│  │  │ ├─ Timing: Must be at 8:00 AM (Timed)              │ │ │
│  │  │ └─ Priority: High                                   │ │ │
│  │  │                                                     │ │ │
│  │  │ Assignment 3: Patient C (Assessment)               │ │ │
│  │  │ ├─ Duration: 60 minutes                             │ │ │
│  │  │ ├─ Location: 789 Pine Rd                            │ │ │
│  │  │ ├─ Timing: Flexible                                 │ │ │
│  │  │ └─ Priority: Normal                                 │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  TIME SLOT GENERATION                                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Available Time Slots (8 AM - 5 PM)                    │ │
│  │  ┌─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┐ │ │
│  │  │8:00 │9:00 │10:00│11:00│12:00│1:00 │2:00 │3:00 │4:00 │ │ │
│  │  │ AM  │ AM  │ AM  │ AM  │ PM  │ PM  │ PM  │ PM  │ PM  │ │ │
│  │  └─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┘ │ │
│  │                                                         │ │
│  │  Constraint Considerations:                             │ │
│  │  ├─ Patient B MUST be at 8:00 AM (hard constraint)     │ │ │
│  │  ├─ Travel time between locations (soft constraint)    │ │ │
│  │  ├─ Lunch break around 12:00 PM (soft constraint)      │ │ │
│  │  └─ Minimize total travel time (soft constraint)       │ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  OPTIMIZED SCHEDULE OUTPUT                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Provider: Sarah Johnson (RN)    Date: July 1, 2024    │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │  8:00 AM - Patient B (Medication) [TIMED]          │ │ │
│  │  │  ├─ Location: 456 Oak Ave                           │ │ │
│  │  │  ├─ Duration: 30 minutes                            │ │ │
│  │  │  └─ Visit Order: 1                                  │ │ │
│  │  │                                                     │ │ │
│  │  │  8:45 AM - Travel (15 minutes)                     │ │ │
│  │  │  ├─ From: 456 Oak Ave                               │ │ │
│  │  │  └─ To: 123 Main St                                 │ │ │
│  │  │                                                     │ │ │
│  │  │  9:00 AM - Patient A (Wound Care)                  │ │ │
│  │  │  ├─ Location: 123 Main St                           │ │ │
│  │  │  ├─ Duration: 45 minutes                            │ │ │
│  │  │  └─ Visit Order: 2                                  │ │ │
│  │  │                                                     │ │ │
│  │  │ 10:00 AM - Travel (30 minutes)                     │ │ │
│  │  │  ├─ From: 123 Main St                               │ │ │
│  │  │  └─ To: 789 Pine Rd                                 │ │ │
│  │  │                                                     │ │ │
│  │  │ 10:30 AM - Patient C (Assessment)                  │ │ │
│  │  │  ├─ Location: 789 Pine Rd                           │ │ │
│  │  │  ├─ Duration: 60 minutes                            │ │ │
│  │  │  └─ Visit Order: 3                                  │ │ │
│  │  │                                                     │ │ │
│  │  │ 12:00 PM - Lunch Break (30 minutes)                │ │ │
│  │  │                                                     │ │ │
│  │  │ Optimization Results:                               │ │ │
│  │  │ ├─ Total travel time: 45 minutes                    │ │ │
│  │  │ ├─ Route efficiency: 88%                            │ │ │
│  │  │ ├─ All constraints satisfied                        │ │ │
│  │  │ └─ Provider utilization: 75%                        │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 8.3 Visit Order Optimization

```
┌─────────────────────────────────────────────────────────────┐
│                   VISIT ORDER OPTIMIZATION                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  OPTIMIZATION FACTORS                                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  GEOGRAPHIC OPTIMIZATION                                │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Goal: Minimize total travel time                    │ │ │
│  │  │                                                     │ │ │
│  │  │ Algorithm: Nearest Neighbor with Constraints        │ │ │
│  │  │ ┌─────────────────────────────────────────────────┐ │ │ │
│  │  │ │ 1. Start with timed appointments (fixed)        │ │ │ │
│  │  │ │ 2. For each remaining appointment:              │ │ │ │
│  │  │ │    ├─ Calculate travel time from current location│ │ │ │
│  │  │ │    ├─ Consider appointment duration              │ │ │ │
│  │  │ │    ├─ Check time slot availability               │ │ │ │
│  │  │ │    └─ Select nearest feasible appointment       │ │ │ │
│  │  │ │ 3. Repeat until all appointments scheduled      │ │ │ │
│  │  │ └─────────────────────────────────────────────────┘ │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  HEALTHCARE TASK SEQUENCING                             │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Goal: Optimize clinical workflow                    │ │ │
│  │  │                                                     │ │ │
│  │  │ Sequencing Rules:                                   │ │ │
│  │  │ ├─ Medication administration early in day           │ │ │
│  │  │ ├─ Assessments before treatments                    │ │ │
│  │  │ ├─ Wound care after assessments                     │ │ │
│  │  │ ├─ IV therapy in controlled time windows            │ │ │
│  │  │ └─ Follow-up visits after primary care             │ │ │
│  │  │                                                     │ │ │
│  │  │ Implementation:                                     │ │ │
│  │  │ ├─ Soft constraints with priority weights           │ │ │
│  │  │ ├─ Task dependency modeling                         │ │ │
│  │  │ └─ Clinical best practice integration               │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  BREAK TIME MANAGEMENT                                  │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Goal: Ensure adequate rest periods                  │ │ │
│  │  │                                                     │ │ │
│  │  │ Break Requirements:                                 │ │ │
│  │  │ ├─ Lunch break: 30-60 minutes around midday        │ │ │
│  │  │ ├─ Short breaks: 15 minutes every 3 hours          │ │ │
│  │  │ ├─ Travel buffer: 5-10 minutes between appointments │ │ │
│  │  │ └─ Documentation time: 10 minutes after each visit │ │ │
│  │  │                                                     │ │ │
│  │  │ Optimization Strategy:                              │ │ │
│  │  │ ├─ Schedule breaks during natural gaps              │ │ │
│  │  │ ├─ Use travel time for short breaks                 │ │ │
│  │  │ └─ Cluster appointments to minimize fragmentation   │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 8.4 Route Planning and Travel Time Optimization

```
┌─────────────────────────────────────────────────────────────┐
│                 ROUTE PLANNING OPTIMIZATION                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  TRAVEL TIME CALCULATION                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  BASIC MODEL (Fallback)                                 │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ def calculate_travel_time(from_location, to_location):│ │ │
│  │  │   # Calculate straight-line distance                │ │ │
│  │  │   distance = haversine_distance(from_location,      │ │ │
│  │  │                                 to_location)        │ │ │
│  │  │                                                     │ │ │
│  │  │   # Determine area type                             │ │ │
│  │  │   if is_urban_area(from_location):                  │ │ │
│  │  │     speed = 25  # mph in urban areas                │ │ │
│  │  │   elif is_suburban_area(from_location):             │ │ │
│  │  │     speed = 35  # mph in suburban areas             │ │ │
│  │  │   else:                                             │ │ │
│  │  │     speed = 45  # mph in rural areas                │ │ │
│  │  │                                                     │ │ │
│  │  │   # Apply time-of-day factors                       │ │ │
│  │  │   if is_rush_hour(current_time):                    │ │ │
│  │  │     speed *= 0.6  # 40% slower during rush hour    │ │ │
│  │  │                                                     │ │ │
│  │  │   return (distance / speed) * 60  # minutes         │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  ADVANCED MODEL (Google Maps Integration)               │ │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ def calculate_real_travel_time(from_loc, to_loc,    │ │ │
│  │  │                               departure_time):      │ │ │
│  │  │   # Use Google Maps Routes API                      │ │ │
│  │  │   request = {                                       │ │ │
│  │  │     'origin': from_loc,                             │ │ │
│  │  │     'destination': to_loc,                          │ │ │
│  │  │     'departure_time': departure_time,               │ │ │
│  │  │     'traffic_model': 'best_guess',                  │ │ │
│  │  │     'units': 'metric'                               │ │ │
│  │  │   }                                                 │ │ │
│  │  │                                                     │ │ │
│  │  │   response = google_maps_api.directions(request)    │ │ │
│  │  │   return response['duration_in_traffic']['value']   │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ROUTE OPTIMIZATION ALGORITHM                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  TRAVELING SALESMAN PROBLEM (TSP) VARIANT               │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Problem: Find optimal visit order for daily route  │ │ │
│  │  │                                                     │ │ │
│  │  │ Constraints:                                        │ │ │
│  │  │ ├─ Timed appointments are fixed                     │ │ │
│  │  │ ├─ Provider shift hours                             │ │ │
│  │  │ ├─ Appointment durations                            │ │ │
│  │  │ ├─ Break time requirements                          │ │ │
│  │  │ └─ Healthcare task sequencing                       │ │ │
│  │  │                                                     │ │ │
│  │  │ Algorithm: Constraint-aware nearest neighbor        │ │ │
│  │  │ ┌─────────────────────────────────────────────────┐ │ │ │
│  │  │ │ 1. Place timed appointments at fixed times      │ │ │ │
│  │  │ │ 2. Identify time windows between fixed points   │ │ │ │
│  │  │ │ 3. For each time window:                        │ │ │ │
│  │  │ │    ├─ Calculate available time                  │ │ │ │
│  │  │ │    ├─ Find appointments that fit                │ │ │ │
│  │  │ │    ├─ Select nearest appointment                 │ │ │ │
│  │  │ │    └─ Update remaining time                     │ │ │ │
│  │  │ │ 4. Handle remaining appointments                │ │ │ │
│  │  │ │ 5. Optimize micro-routes within windows         │ │ │ │
│  │  │ └─────────────────────────────────────────────────┘ │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 9. Adding New Constraints

### 9.1 Constraint Development Process

Adding new constraints to the system follows a structured approach to ensure maintainability and proper integration:

```
┌─────────────────────────────────────────────────────────────┐
│                 CONSTRAINT DEVELOPMENT PROCESS             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  STEP 1: ANALYSIS AND DESIGN                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Business Requirement Analysis                          │ │
│  │  ├─ Identify the business rule or optimization goal     │ │
│  │  ├─ Determine constraint type (hard vs soft)            │ │
│  │  ├─ Define constraint scope (assignment vs day planning)│ │
│  │  ├─ Identify affected entities and data                 │ │
│  │  └─ Estimate performance impact                         │ │
│  │                                                         │ │
│  │  Technical Design                                       │ │
│  │  ├─ Choose constraint ID (C017, C018, etc.)             │ │
│  │  ├─ Define configuration parameters                     │ │
│  │  ├─ Identify required data sources                      │ │
│  │  ├─ Design scoring mechanism                            │ │
│  │  └─ Plan testing strategy                               │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  STEP 2: IMPLEMENTATION                                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Create Constraint File                                 │ │
│  │  ├─ Follow naming convention: cXXX_stage_description.py │ │
│  │  ├─ Implement constraint function with @with_config     │ │
│  │  ├─ Add comprehensive documentation                     │ │
│  │  ├─ Include real-world examples                         │ │
│  │  └─ Handle edge cases and error conditions              │ │
│  │                                                         │ │
│  │  Update Configuration                                   │ │
│  │  ├─ Add constraint parameters to service configs        │ │
│  │  ├─ Add feature toggle to scheduler.yml                 │ │
│  │  ├─ Define default values                               │ │
│  │  └─ Document configuration options                      │ │
│  │                                                         │ │
│  │  Register Constraint                                    │ │
│  │  ├─ Import in appropriate coordinator file              │ │
│  │  ├─ Add to constraint registration function             │ │
│  │  ├─ Implement conditional activation logic              │ │
│  │  └─ Test constraint loading                             │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  STEP 3: TESTING AND VALIDATION                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Unit Testing                                           │ │
│  │  ├─ Test constraint logic with sample data              │ │
│  │  ├─ Verify configuration injection                      │ │
│  │  ├─ Test edge cases and error handling                  │ │
│  │  └─ Validate scoring calculations                       │ │
│  │                                                         │ │
│  │  Integration Testing                                    │ │
│  │  ├─ Test with full solver execution                     │ │
│  │  ├─ Verify constraint interaction                       │ │
│  │  ├─ Test performance impact                             │ │
│  │  └─ Validate solution quality                           │ │
│  │                                                         │ │
│  │  Production Validation                                  │ │
│  │  ├─ Deploy with feature toggle disabled                │ │
│  │  ├─ Enable for subset of data                           │ │
│  │  ├─ Monitor performance metrics                         │ │
│  │  └─ Gradually roll out to full dataset                 │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 9.2 Constraint Implementation Template

```
┌─────────────────────────────────────────────────────────────┐
│                 CONSTRAINT IMPLEMENTATION TEMPLATE         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  FILE: c017_asgn_example_constraint.py                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  """                                                    │ │
│  │  Example Constraint (C017)                              │ │
│  │                                                         │ │
│  │  This constraint demonstrates the standard pattern      │ │
│  │  for implementing new constraints in the system.        │ │
│  │                                                         │ │
│  │  Business Rule: [Describe the business requirement]     │ │
│  │  Constraint Type: [Hard/Soft]                           │ │
│  │  Stage: [Assignment/Day Planning]                       │ │
│  │  """                                                    │ │
│  │                                                         │ │
│  │  from timefold.solver.score import (                    │ │
│  │      HardSoftScore, ConstraintFactory, Constraint       │ │
│  │  )                                                      │ │
│  │  from loguru import logger                              │ │
│  │  from typing import Optional                            │ │
│  │                                                         │ │
│  │  from model.planning_models import AppointmentAssignment│ │
│  │  from constraints.base_constraints import with_config   │ │
│  │                                                         │ │
│  │                                                         │ │
│  │  @with_config('skilled_nursing', return_type='list')    │ │
│  │  def example_constraint_group(                          │ │
│  │      factory: ConstraintFactory,                        │ │
│  │      **kwargs                                           │ │
│  │  ):                                                     │ │
│  │      """Return all constraints in this group."""       │ │
│  │      if factory is None:                                │ │
│  │          return []                                      │ │
│  │                                                         │ │
│  │      return [                                           │ │
│  │          example_business_rule(factory, **kwargs),      │ │
│  │          example_optimization_goal(factory, **kwargs)   │ │
│  │      ]                                                  │ │
│  │                                                         │ │
│  │                                                         │ │
│  │  @with_config('skilled_nursing')                        │ │
│  │  def example_business_rule(                             │ │
│  │      constraint_factory: ConstraintFactory,             │ │
│  │      core_config=None,                                  │ │
│  │      service_config=None,                               │ │
│  │      combined_config=None,                              │ │
│  │      **kwargs                                           │ │
│  │  ) -> Constraint:                                       │ │
│  │      """                                                │ │
│  │      HARD CONSTRAINT: Example business rule.            │ │
│  │                                                         │ │
│  │      Real-world scenario: [Describe specific example]   │ │
│  │      """                                                │ │
│  │      # Handle Timefold validation calls                 │ │
│  │      if constraint_factory is None:                     │ │
│  │          return constraint_factory.for_each(            │ │
│  │              AppointmentAssignment                      │ │
│  │          ).filter(lambda x: False).as_constraint(       │ │
│  │              "no_op_constraint"                         │ │
│  │          )                                              │ │
│  │                                                         │ │
│  │      # Get configuration parameters                     │ │
│  │      enable_feature = core_config.get(                  │ │
│  │          'enable_example_constraint', True              │ │
│  │      )                                                  │ │
│  │      threshold_value = service_config.get(              │ │
│  │          'example_threshold', 10                        │ │
│  │      )                                                  │ │
│  │                                                         │ │
│  │      if not enable_feature:                             │ │
│  │          logger.info("Example constraint disabled")     │ │
│  │          return constraint_factory.for_each(            │ │
│  │              AppointmentAssignment                      │ │
│  │          ).filter(lambda x: False).as_constraint(       │ │
│  │              "disabled_example_constraint"              │ │
│  │          )                                              │ │
│  │                                                         │ │
│  │      def violates_business_rule(assignment):            │ │
│  │          """Check if assignment violates business rule."""│ │
│  │          if not assignment.provider:                    │ │
│  │              return False  # Unassigned is OK           │ │
│  │                                                         │ │
│  │          # Implement business rule logic here           │ │
│  │          # Example: Check some condition               │ │
│  │          return some_condition_check(assignment)        │ │
│  │                                                         │ │
│  │      return (constraint_factory                         │ │
│  │              .for_each(AppointmentAssignment)           │ │
│  │              .filter(violates_business_rule)            │ │
│  │              .penalize(HardSoftScore.ONE_HARD)          │ │
│  │              .as_constraint("Example business rule"))   │ │
│  │                                                         │ │
│  │                                                         │ │
│  │  @with_config('skilled_nursing')                        │ │
│  │  def example_optimization_goal(                         │ │
│  │      constraint_factory: ConstraintFactory,             │ │
│  │      service_config=None,                               │ │
│  │      **kwargs                                           │ │
│  │  ) -> Constraint:                                       │ │
│  │      """                                                │ │
│  │      SOFT CONSTRAINT: Example optimization goal.        │ │
│  │                                                         │ │
│  │      Goal: [Describe optimization objective]            │ │
│  │      """                                                │ │
│  │      if constraint_factory is None:                     │ │
│  │          return constraint_factory.for_each(            │ │
│  │              AppointmentAssignment                      │ │
│  │          ).filter(lambda x: False).as_constraint(       │ │
│  │              "no_op_constraint"                         │ │
│  │          )                                              │ │
│  │                                                         │ │
│  │      # Get optimization parameters                      │ │
│  │      weight = service_config.get(                       │ │
│  │          'example_optimization_weight', 0.5             │ │
│  │      )                                                  │ │
│  │                                                         │ │
│  │      def calculate_penalty(assignment):                 │ │
│  │          """Calculate optimization penalty."""          │ │
│  │          if not assignment.provider:                    │ │
│  │              return 0                                   │ │
│  │                                                         │ │
│  │          # Implement optimization logic                 │ │
│  │          penalty = calculate_optimization_penalty(      │ │
│  │              assignment                                 │ │
│  │          )                                              │ │
│  │          return int(penalty * weight)                   │ │
│  │                                                         │ │
│  │      return (constraint_factory                         │ │
│  │              .for_each(AppointmentAssignment)           │ │
│  │              .filter(lambda a: a.provider is not None)  │ │
│  │              .penalize(HardSoftScore.ONE_SOFT,          │ │
│  │                       calculate_penalty)                │ │
│  │              .as_constraint("Example optimization"))    │ │
│  │                                                         │ │
│  │                                                         │ │
│  │  def some_condition_check(assignment):                  │ │
│  │      """Helper function for business rule logic."""     │ │
│  │      # Implement specific condition checking            │ │
│  │      return False                                       │ │
│  │                                                         │ │
│  │                                                         │ │
│  │  def calculate_optimization_penalty(assignment):        │ │
│  │      """Helper function for optimization penalty."""    │ │
│  │      # Implement penalty calculation logic              │ │
│  │      return 0.0                                         │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 10. External System Integration

### 10.1 Integration Architecture Overview

The CareAXL Scheduling Engine is designed to integrate seamlessly with external systems through multiple integration patterns:

```
┌─────────────────────────────────────────────────────────────┐
│                 EXTERNAL SYSTEM INTEGRATION                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  INTEGRATION PATTERNS                                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  SYNCHRONOUS INTEGRATION (REST APIs)                    │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │                                                     │ │ │
│  │  │  External System          Scheduler Engine          │ │ │
│  │  │  ┌─────────────────┐     ┌─────────────────────────┐ │ │ │
│  │  │  │ CareAXL Staff   │────▶│ StaffServiceClient      │ │ │ │
│  │  │  │ Service         │     │ - GET /providers        │ │ │ │
│  │  │  │                 │     │ - GET /availability     │ │ │ │
│  │  │  │ REST API        │     │ - PUT /assignments      │ │ │ │
│  │  │  └─────────────────┘     └─────────────────────────┘ │ │ │
│  │  │                                                     │ │ │
│  │  │  ┌─────────────────┐     ┌─────────────────────────┐ │ │ │
│  │  │  │ CareAXL Patient │────▶│ PatientServiceClient    │ │ │ │
│  │  │  │ Service         │     │ - GET /patients         │ │ │ │
│  │  │  │                 │     │ - GET /locations        │ │ │ │
│  │  │  │ REST API        │     │ - GET /preferences      │ │ │ │
│  │  │  └─────────────────┘     └─────────────────────────┘ │ │ │
│  │  │                                                     │ │ │
│  │  │  ┌─────────────────┐     ┌─────────────────────────┐ │ │ │
│  │  │  │ CareAXL Appt    │────▶│ AppointmentServiceClient│ │ │ │
│  │  │  │ Service         │     │ - GET /appointments     │ │ │ │
│  │  │  │                 │     │ - POST /appointments    │ │ │ │
│  │  │  │ REST API        │     │ - PUT /schedules        │ │ │ │
│  │  │  └─────────────────┘     └─────────────────────────┘ │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  ASYNCHRONOUS INTEGRATION (Message Queues)              │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │                                                     │ │ │
│  │  │  External Systems         Message Bus               │ │ │
│  │  │  ┌─────────────────┐     ┌─────────────────────────┐ │ │ │
│  │  │  │ Staff Service   │────▶│ RabbitMQ                │ │ │ │
│  │  │  │ Patient Service │────▶│                         │ │ │ │
│  │  │  │ Appt Service    │────▶│ Event Queues:           │ │ │ │
│  │  │  │ Notification    │────▶│ ├─ CRITICAL              │ │ │ │
│  │  │  │ Service         │     │ ├─ HIGH                  │ │ │ │
│  │  │  │ Analytics       │     │ ├─ NORMAL                │ │ │ │
│  │  │  │ Service         │     │ └─ LOW                   │ │ │ │
│  │  │  └─────────────────┘     └─────────────────────────┘ │ │ │
│  │  │                                   │                 │ │ │
│  │  │                                   ▼                 │ │ │
│  │  │                         ┌─────────────────────────┐ │ │ │
│  │  │                         │ Scheduler Engine        │ │ │ │
│  │  │                         │ Event Handlers          │ │ │ │
│  │  │                         │ ├─ Provider changes      │ │ │ │
│  │  │                         │ ├─ Appointment updates   │ │ │ │
│  │  │                         │ ├─ Patient changes       │ │ │ │
│  │  │                         │ └─ Optimization triggers │ │ │ │
│  │  │                         └─────────────────────────┘ │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 10.2 API Integration Endpoints

```
┌─────────────────────────────────────────────────────────────┐
│                    API INTEGRATION ENDPOINTS               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  SCHEDULER ENGINE API (Inbound)                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  POST /api/v1/schedule/assign                           │ │
│  │  ├─ Trigger assignment optimization                     │ │
│  │  ├─ Request body: service_type, date_range, filters     │ │
│  │  └─ Response: assignment results and statistics         │ │
│  │                                                         │ │
│  │  POST /api/v1/schedule/dayplan                          │ │
│  │  ├─ Trigger day planning optimization                   │ │
│  │  ├─ Request body: target_date, provider_ids             │ │
│  │  └─ Response: time slot assignments and routes          │ │
│  │                                                         │ │
│  │  GET /api/v1/schedule/status                            │ │
│  │  ├─ Get current optimization status                     │ │
│  │  └─ Response: solver state, queue status, metrics      │ │
│  │                                                         │ │
│  │  POST /api/v1/schedule/optimize                         │ │
│  │  ├─ Request immediate optimization                      │ │
│  │  ├─ Request body: optimization_type, priority           │ │
│  │  └─ Response: optimization job ID and status            │ │
│  │                                                         │ │
│  │  GET /api/v1/providers                                  │ │
│  │  ├─ Get provider information                            │ │
│  │  └─ Response: provider list with availability           │ │
│  │                                                         │ │
│  │  GET /api/v1/appointments                               │ │
│  │  ├─ Get appointment information                         │ │
│  │  └─ Response: appointment list with assignments        │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  EXTERNAL SERVICE APIs (Outbound)                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Staff Service API                                      │ │
│  │  ├─ GET /api/staff/providers                            │ │
│  │  │   └─ Fetch all active providers                      │ │
│  │  ├─ GET /api/staff/providers/{id}                       │ │
│  │  │   └─ Fetch specific provider details                 │ │
│  │  ├─ GET /api/staff/availability/{provider_id}           │ │
│  │  │   └─ Fetch provider availability                     │ │
│  │  └─ PUT /api/staff/assignments                          │ │
│  │      └─ Update provider assignments                     │ │
│  │                                                         │ │
│  │  Patient Service API                                    │ │
│  │  ├─ GET /api/patients                                   │ │
│  │  │   └─ Fetch patient information                       │ │
│  │  ├─ GET /api/patients/{id}/location                     │ │
│  │  │   └─ Fetch patient location                          │ │
│  │  └─ GET /api/patients/{id}/preferences                  │ │
│  │      └─ Fetch patient preferences                       │ │
│  │                                                         │ │
│  │  Appointment Service API                                │ │
│  │  ├─ GET /api/appointments                               │ │
│  │  │   └─ Fetch appointment requests                      │ │
│  │  ├─ POST /api/appointments                              │ │
│  │  │   └─ Create new appointment                          │ │
│  │  ├─ PUT /api/appointments/{id}/schedule                 │ │
│  │  │   └─ Update appointment schedule                     │ │
│  │  └─ PUT /api/appointments/{id}/status                   │ │
│  │      └─ Update appointment status                       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 10.3 Event-Driven Integration Flow

```
┌─────────────────────────────────────────────────────────────┐
│                EVENT-DRIVEN INTEGRATION FLOW               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  TYPICAL INTEGRATION SCENARIO                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  1. EXTERNAL SYSTEM CHANGE                              │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ Staff Service: Provider availability updated    │ │ │
│  │     │ ├─ Provider ID: PROV001                         │ │ │
│  │     │ ├─ Old schedule: Mon-Fri 9AM-5PM                │ │ │
│  │     │ ├─ New schedule: Mon-Fri 8AM-6PM                │ │ │
│  │     │ └─ Effective: Immediately                       │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  2. EVENT PUBLICATION                                   │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ Staff Service publishes event:                  │ │ │
│  │     │                                                 │ │ │
│  │     │ Event: PROVIDER_AVAILABILITY_CHANGED            │ │ │
│  │     │ Priority: HIGH                                  │ │ │
│  │     │ Data: {                                         │ │ │
│  │     │   provider_id: "PROV001",                       │ │ │
│  │     │   old_availability: {...},                      │ │ │
│  │     │   new_availability: {...},                      │ │ │
│  │     │   affected_dates: ["2024-07-01", "2024-07-02"]  │ │ │
│  │     │ }                                               │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  3. EVENT PROCESSING                                    │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ Scheduler Engine receives event                 │ │ │
│  │     │                                                 │ │ │
│  │     │ OptimizationEventHandler.handle_provider_change()│ │ │
│  │     │ ├─ Identify affected appointments               │ │ │
│  │     │ ├─ Check for constraint violations              │ │ │
│  │     │ ├─ Update warm solver state                     │ │ │
│  │     │ ├─ Trigger incremental re-optimization          │ │ │
│  │     │ └─ Publish optimization results                 │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  4. OPTIMIZATION EXECUTION                              │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ Incremental Optimization Process:               │ │ │
│  │     │                                                 │ │ │
│  │     │ 1. Load affected appointments                   │ │ │
│  │     │ 2. Update provider availability facts           │ │ │
│  │     │ 3. Run constraint validation                    │ │ │
│  │     │ 4. Re-optimize affected assignments             │ │ │
│  │     │ 5. Update day plans if necessary                │ │ │
│  │     │ 6. Calculate impact metrics                     │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  5. RESULT PROPAGATION                                  │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ Update External Systems:                        │ │ │
│  │     │                                                 │ │ │
│  │     │ ├─ Appointment Service: Updated schedules       │ │ │
│  │     │ ├─ Staff Service: New assignments               │ │ │
│  │     │ ├─ Notification Service: Provider alerts        │ │ │
│  │     │ └─ Analytics Service: Optimization metrics      │ │ │
│  │     │                                                 │ │ │
│  │     │ Publish Events:                                 │ │ │
│  │     │ ├─ SCHEDULE_OPTIMIZATION_COMPLETED              │ │ │
│  │     │ ├─ PROVIDER_ASSIGNMENTS_UPDATED                 │ │ │
│  │     │ └─ OPTIMIZATION_METRICS_AVAILABLE               │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 10.4 Integration Best Practices

```
┌─────────────────────────────────────────────────────────────┐
│                 INTEGRATION BEST PRACTICES                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ERROR HANDLING AND RESILIENCE                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Circuit Breaker Pattern                                │ │
│  │  ├─ Monitor external service health                     │ │
│  │  ├─ Fail fast when services are down                    │ │
│  │  ├─ Automatic recovery detection                        │ │
│  │  └─ Graceful degradation with cached data               │ │
│  │                                                         │ │
│  │  Retry Logic                                            │ │
│  │  ├─ Exponential backoff for transient failures         │ │
│  │  ├─ Maximum retry limits                                │ │
│  │  ├─ Different strategies per error type                 │ │
│  │  └─ Dead letter queues for failed events               │ │
│  │                                                         │ │
│  │  Timeout Management                                     │ │
│  │  ├─ Appropriate timeouts for each service              │ │
│  │  ├─ Connection pooling and keep-alive                  │ │
│  │  ├─ Request cancellation support                       │ │
│  │  └─ Monitoring of response times                       │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  DATA CONSISTENCY AND SYNCHRONIZATION                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Event Ordering                                         │ │
│  │  ├─ Use event timestamps for ordering                   │ │
│  │  ├─ Handle out-of-order events gracefully               │ │
│  │  ├─ Implement idempotent event processing               │ │
│  │  └─ Use correlation IDs for event tracking              │ │
│  │                                                         │ │
│  │  Data Validation                                        │ │
│  │  ├─ Validate all incoming data                          │ │
│  │  ├─ Schema versioning for API compatibility             │ │
│  │  ├─ Data transformation and normalization               │ │
│  │  └─ Conflict resolution strategies                      │ │
│  │                                                         │ │
│  │  Cache Management                                       │ │
│  │  ├─ Cache frequently accessed data                      │ │
│  │  ├─ Implement cache invalidation strategies             │ │
│  │  ├─ Use cache-aside pattern for consistency             │ │
│  │  └─ Monitor cache hit rates and performance             │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  MONITORING AND OBSERVABILITY                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Metrics Collection                                     │ │
│  │  ├─ API response times and success rates               │ │
│  │  ├─ Event processing latency                           │ │
│  │  ├─ Queue depths and processing rates                  │ │
│  │  └─ Optimization performance metrics                   │ │
│  │                                                         │ │
│  │  Logging Strategy                                       │ │
│  │  ├─ Structured logging with correlation IDs            │ │
│  │  ├─ Log levels appropriate for each environment        │ │
│  │  ├─ Sensitive data masking                             │ │
│  │  └─ Centralized log aggregation                        │ │
│  │                                                         │ │
│  │  Health Checks                                          │ │
│  │  ├─ Service health endpoints                           │ │
│  │  ├─ Dependency health monitoring                       │ │
│  │  ├─ Automated alerting on failures                     │ │
│  │  └─ Dashboard visualization                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## Conclusion

This comprehensive guide provides a detailed understanding of the CareAXL Scheduling Engine's architecture, implementation, and integration patterns. The system's modular design, constraint-based optimization, and hybrid service architecture enable it to handle complex healthcare scheduling requirements while maintaining flexibility for future enhancements.

Key takeaways:

1. **Two-Stage Architecture**: Separates strategic decisions (provider/date assignment) from tactical decisions (time slot/route optimization)
2. **Constraint-Driven Design**: Uses Timefold's constraint solver with configurable business rules
3. **Hybrid Integration**: Combines synchronous APIs for immediate operations with asynchronous events for background processing
4. **Configuration Management**: Hierarchical configuration system supporting multiple service types
5. **Extensibility**: Clear patterns for adding new constraints and integrating with external systems

The system is designed for production use in healthcare environments where reliability, performance, and compliance are critical requirements.
