services:
  # FastAPI Web Server - for external API testing
  caxl_api:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DEBUG=True
      - HOST=0.0.0.0
      - PORT=8000
      - PROJECT_NAME=caxl-scheduling-engine
      - API_V1_STR=/api/v1
      - CORS_ORIGINS=["*"]
      - CORS_ALLOW_CREDENTIALS=True
      - CORS_ALLOW_METHODS=["*"]
      - CORS_ALLOW_HEADERS=["*"]
      - CORS_EXPOSE_HEADERS=["*"]
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - PYTHONPATH=/app
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
    command: python -m uvicorn src.api.app:create_app --host 0.0.0.0 --port 8000 --reload --factory
