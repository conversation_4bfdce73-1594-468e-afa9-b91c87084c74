# Provider Assignment Edge Cases

**Purpose**: Test provider assignment scenarios including continuity of care, care teams, and provider changes
**Best for**: Testing provider-patient relationship constraints and care continuity
**Complexity**: High

## Features Demonstrated

- Same provider assignment for episode continuity
- Different provider assignments when needed
- Care team coordination
- Provider unavailability handling
- Provider preference matching
- Episode continuity optimization

## Data Overview

- **Providers**: 5 (different roles and specialties)
- **Patients**: 6 (with ongoing care episodes)
- **Appointments**: 15 (multiple per episode)
- **Geographic Coverage**: New York Metro Area

## Test Dimensions Covered

- **provider_assignment**: same_provider, different_providers, episode_continuity, care_teams, provider_changes
- **continuity_of_care**: episode_continuity, provider_consistency
- **preferences**: provider_preferences, patient_preferences

## Usage

```bash
# Copy this scenario to main data folder
cp -r data/scenarios/provider_assignment_edge_cases/* data/

# Run assignment job
python -m appointment_scheduler.jobs.assign_appointments

# Run day plan job  
python -m appointment_scheduler.jobs.day_plan
```

## Expected Outcomes

- Appointments within same episode should prefer same provider
- Care team members should be coordinated appropriately
- Provider preferences should be respected when possible
- Patient preferences for specific providers should be honored
