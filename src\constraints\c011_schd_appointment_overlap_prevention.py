"""
Appointment Overlap Prevention Constraint (C011)

This constraint prevents providers from being double-booked for the same time slot.
This is a HARD constraint that must be satisfied for valid assignments.
"""

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint, Joiners

from model.planning_models import TimeSlotAssignment
from model.domain import AppointmentData

# --- Public Utility Functions (moved from utils) ---
def get_appointment_duration_minutes(appointment: AppointmentData) -> int:
    """
    Get the duration of an appointment in minutes.
    
    Args:
        appointment: AppointmentData object
    
    Returns:
        int: Duration in minutes
    """
    return appointment.duration_min


def no_double_booking(constraint_factory: ConstraintFactory) -> Constraint:
    """Provider cannot be double-booked for the same time slot."""
    # Handle Timefold validation calls where factory is None
    if constraint_factory is None:
        return constraint_factory.for_each(TimeSlotAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (constraint_factory
            .for_each(TimeSlotAssignment)
            .join(TimeSlotAssignment,
                  Joiners.equal(lambda assignment: assignment.time_slot),
                  Joiners.equal(lambda assignment: assignment.scheduled_appointment.provider))
            .filter(lambda assignment1, assignment2: assignment1.id != assignment2.id)
            .penalize(HardSoftScore.ONE_HARD, lambda assignment1, assignment2: 1)
            .as_constraint("No double booking"))


def appointment_overlap_prevention_constraints(factory: ConstraintFactory):
    """Return all appointment overlap prevention constraints."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return []
        
    return [
        no_double_booking(factory),
    ]
