service_type: hospital_at_home
required_skills:
  - "Registered Nurse (RN)"
  - "Critical Care Nursing"
  - "IV Therapy"
  - "Vital Signs Monitoring"
  - "Emergency Response"
  - "Patient Education"

geographic_radius_miles: 20.0
max_daily_appointments_per_provider: 4
max_weekly_hours_per_provider: 32

# Constraint weights (0.0 to 1.0)
continuity_weight: 0.85
workload_balance_weight: 0.8
geographic_clustering_weight: 0.7
patient_preference_weight: 0.7
capacity_threshold_percentage: 0.90

# Role hierarchy configuration
enable_role_hierarchy: true
role_hierarchy:
  "RN": ["LPN", "CNA"]
  "LPN": ["CNA"]
  "CNA": []  # No lower-level roles can perform CNA work

role_hierarchy_penalties:
  "RN->LPN": 15
  "RN->CNA": 25
  "LPN->CNA": 20

# Service-specific settings
visit_duration_minutes: 90
requires_initial_assessment: true
allows_weekend_visits: true
emergency_response_time_hours: 1

# Geographic clustering settings
cluster_radius_miles: 8.0
max_cluster_size: 4
prefer_same_day_clustering: true

# Continuity of care settings
continuity_priority_bonus: 120
continuity_threshold_days: 45  # Days to consider for continuity
existing_relationship_bonus: 60

# Workload balancing settings
target_daily_appointments: 3
workload_variance_tolerance: 1
overtime_penalty_multiplier: 2.0

# Patient preference settings
preferred_provider_bonus: 35
preferred_time_bonus: 25
preferred_location_bonus: 30

# =============================================================================
# SERVICE-SPECIFIC AVAILABILITY CONFIGURATION
# =============================================================================

# Service-specific availability settings
service_availability:
  # Service-specific blackout periods
  blackout_periods:
    - name: "Hospital at Home Training"
      start_date: "06-15"
      end_date: "06-17"
      reason: "staff_training"
      severity: "soft"
      description: "Annual Hospital at Home staff training"

    - name: "Emergency Response Drill"
      start_date: "09-10"
      end_date: "09-12"
      reason: "emergency_preparation"
      severity: "soft"
      description: "Emergency response system testing"
  
  # Service-specific holidays (state-specific examples)
  holidays:
    - name: "Texas Independence Day"
      date: "03-02"
      type: "state"
      state: "TX"
      description: "Texas state holiday"

    - name: "San Jacinto Day"
      date: "04-21"
      type: "state"
      state: "TX"
      description: "Texas state holiday commemorating the Battle of San Jacinto"

    - name: "Cesar Chavez Day"
      date: "03-31"
      type: "state"
      state: "CA"
      description: "California state holiday"

    - name: "Native American Day"
      date: "09-23"
      type: "state"
      state: "CA"
      description: "California state holiday"
  
  # Service-specific weekend coverage (24/7 service)
  weekend_coverage:
    enabled: true
    saturday_coverage: true
    sunday_coverage: true
    weekend_penalty: 1 