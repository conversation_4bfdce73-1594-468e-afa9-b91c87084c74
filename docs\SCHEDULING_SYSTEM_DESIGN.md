# CareAXL Scheduling Engine: System Design & Timefold Integration

## 1. Timefold Concepts

**Timefold** is an open-source constraint solver for planning and optimization problems. It is used to assign resources (e.g., staff, time slots) to tasks (e.g., appointments) while satisfying hard constraints and optimizing soft constraints.

- **Purpose:** Automate complex scheduling, assignment, and routing problems.
- **General Mechanism:** Uses domain models annotated with special decorators to define planning entities, variables, and constraints. The solver searches for the best solution by minimizing constraint violations.
- **Warm-Start Support:** Timefold can reuse previous solutions (warm solver) for incremental changes, enabling real-time, event-driven optimization without full reloads.

---

## 2. Timefold Annotations (Python Decorators) with Examples

| Decorator | Purpose | Example |
|-----------|---------|---------|
| `@planning_id` | Unique identifier for planning objects | `@planning_id def get_id(self): return self.id` |
| `@planning_entity` | Marks a class as a planning entity (subject to change) | `@planning_entity class AppointmentAssignment: ...` |
| `@planning_solution` | Marks a class as the solution container | `@planning_solution class AppointmentSchedule: ...` |
| `@planning_variable` | Marks a field as a planning variable | `@planning_variable def provider(self): ...` |
| `@value_range_provider` | Provides possible values for a planning variable | `@value_range_provider def available_providers(self): ...` |
| `@problem_fact_collection_property` | Marks a field as a collection of immutable facts | `@problem_fact_collection_property def appointments(self): ...` |
| `@planning_entity_collection_property` | Marks a field as a collection of planning entities | `@planning_entity_collection_property def assignments(self): ...` |
| `@planning_score` | Marks a field as the solution score | `@planning_score def score(self): ...` |

### Scoring: Hard vs. Soft Constraints

- **Hard Constraint:** Must never be violated (e.g., provider must have required skill).
- **Soft Constraint:** Prefer not to violate, but allowed if necessary (e.g., minimize travel time).

**Example:**

```python
# Hard constraint: Provider must have required skill
if not provider.has_skill(appointment.required_skill):
    score.add_hard(-1)

# Soft constraint: Prefer same provider for continuity
if appointment.previous_provider == provider:
    score.add_soft(1)
```

---

## 3. Project Design Overview

### Two-Stage Design

```
+-------------------+      +-------------------+      +-------------------+
| Input Data:       |      | Stage 1:          |      | Stage 2:          |
| Appointments,     |----->| Assignment Solver |----->| Dayplan Solver    |
| Providers,        |      |                   |      |                   |
| Configs           |      |                   |      |                   |
+-------------------+      +-------------------+      +-------------------+
                                                              |
                                                              v
                                                    +-------------------+
                                                    | Output:           |
                                                    | Final Schedule    |
                                                    +-------------------+
```

- **Stage 1 (Assignment Solver):** Assigns provider and date to each appointment.
- **Stage 2 (Dayplan Solver):** Assigns time slots and routes for each day's appointments.

**Data Flow:**  
`Input Data → Assignment Solver → Intermediate Assignments → Dayplan Solver → Final Schedule`

---

## 4. Domain Model Explanation with Diagrams

### Key Classes

- **Appointment:** Patient request for care.
- **AppointmentAssignment:** Assignment of provider and date.
- **TimeSlotAssignment:** Assignment of time slot to scheduled appointment.
- **AppointmentSchedule:** Solution for assignment stage.
- **DaySchedule:** Solution for day planning stage.

### UML Class Diagram (Text)

```
+-------------------+      +------------------------+
|   Appointment     |<>----| AppointmentAssignment  |
+-------------------+      +------------------------+
        |                           |
        |                           v
        |                  +---------------------+
        |                  | TimeSlotAssignment |
        |                  +---------------------+
        |                           |
        v                           v
+-------------------+      +---------------------+
| AppointmentSchedule|      |    DaySchedule     |
+-------------------+      +---------------------+
```

---

## 5. scheduler.py Execution Guide

### Structure

- **AppointmentScheduler:** Main class for running jobs.
- **Jobs:** `AssignAppointmentJob`, `DayPlanJob`
- **Modes:** `once` (single run), `daemon` (continuous, warm solver)

### How to Execute

```bash
python scheduler.py --mode once --job assign
python scheduler.py --mode once --job dayplan --date 2024-07-01
python scheduler.py --mode daemon
```

#### Parameters

- `--mode`: `once` (single run) or `daemon` (continuous)
- `--job`: `assign`, `dayplan` (required for `once` mode)
- `--date`: Target date for dayplan job (optional)
- `--config-folder`: Custom config path (optional)

#### Daemon Mode

- Runs as a warm solver, keeping state in memory.
- Reacts to events (e.g., staff change) without full reload.

---

## 6. Configuration Design

- **Core Config:** `config/scheduler.yml`
- **Service-Specific Configs:**  
  - `config/behavioral_care.yml`
  - `config/hospital_at_home.yml`
  - `config/pcs.yml`
  - `config/skilled_nursing.yml`

**How Config is Injected:**

- Loaded at startup by `ConfigRegistry`.
- Injected into constraints via `@with_config` decorator.
- Thread-local context allows runtime service override.

---

## 7. Key Configuration Parameters

| Parameter         | Purpose                                      |
|-------------------|----------------------------------------------|
| rolling_window    | Days to consider for assignment              |
| reassign_window   | Days to allow reassignment                   |
| max_retry         | Max retries for failed assignments           |
| enable_*          | Feature toggles for constraints              |
| traffic_integration | Real-time traffic API settings             |

**Effect:**  
These parameters control solver scope, optimization aggressiveness, and dynamic scheduling behavior.

---

## 8. Constraints Overview

### Assignment Constraints (`assignment_constraints.py`)

- **Hard:**
  - Provider skill validation
  - Date-based availability
  - Timed visit date assignment
- **Soft:**
  - Workload balance
  - Geographic clustering
  - Patient preference matching
  - Provider capacity management
  - Continuity of care

### Dayplan Constraints (`day_constraints.py`)

- **Hard:**
  - Timeslot availability
  - Appointment overlap prevention
  - Flexible appointment timing
- **Soft:**
  - Timed appointment pinning
  - Healthcare task sequencing
  - Route optimization (if enabled)

#### How to Add a New Constraint

1. Add logic in the appropriate file (e.g., `assignment_constraints.py`).
2. Use `@with_config` to access configs.
3. Register the constraint in the `define_constraints` function.
4. Test with sample data.

**Example (Assignment):**

```python
# In assignment_constraints.py
def male_clinician_in_crime_area(constraint_factory):
    # ... logic ...
    return [constraint_factory.from_...]
```

**Example (Dayplan):**

```python
# In day_constraints.py
def google_maps_travel_time_constraint(constraint_factory):
    # ... call Google Maps API ...
    return [constraint_factory.from_...]
```

---

## 9. Service and Event Architecture

### Service Layer (Synchronous Operations)

The system includes a service layer for immediate operations:

- **SchedulerService:** Main service orchestrator (`src/services/scheduler_service.py`)
- **Service Clients:** HTTP clients for external services (`src/api/service_clients.py`)
  - `StaffServiceClient`: Provider management
  - `PatientServiceClient`: Patient data
  - `AppointmentServiceClient`: Appointment CRUD
- **Mock Clients:** For development and testing

### Event System (Asynchronous Operations)

The system includes an event system for background processing:

- **RabbitMQ Integration:** Production event processing (`src/events/rabbitmq_integration.py`)
- **Mock Event Bus:** For development and testing
- **Event Types:** Appointment, provider, patient, optimization, and notification events
- **Priority Queues:** Critical, high, normal, and low priority processing

### Event Types by Service

| Service           | Event Name                        | Scheduler Action                        |
|-------------------|-----------------------------------|-----------------------------------------|
| Staff Service     | PROVIDER_CREATED                  | Add new provider, update assignments    |
| Staff Service     | PROVIDER_UPDATED                  | Update provider info, revalidate        |
| Staff Service     | PROVIDER_TERMINATED               | Unassign/cancel future appointments     |
| Staff Service     | PROVIDER_AVAILABILITY_CHANGED     | Reassign affected appointments          |
| Staff Service     | PROVIDER_SKILL_CHANGED            | Revalidate assignments                  |
| Patient Service   | PATIENT_CREATED                   | Add new patient, update assignments     |
| Patient Service   | PATIENT_UPDATED                   | Update patient info, revalidate         |
| Patient Service   | PATIENT_ADDRESS_CHANGED           | Recalculate geographic constraints      |
| Patient Service   | PATIENT_PREFERENCE_CHANGED        | Re-optimize for preferences             |
| Appointment Svc   | APPOINTMENT_CREATED               | Assign provider and date                |
| Appointment Svc   | APPOINTMENT_UPDATED               | Re-optimize assignment                  |
| Appointment Svc   | APPOINTMENT_CANCELLED             | Release slot, update provider schedule  |
| Appointment Svc   | APPOINTMENT_RESCHEDULED           | Reassign provider/date                  |

### Event Flow Diagram

```
+-------------------+   PROVIDER_*   +-------------------+   SCHEDULER ACTIONS   +----------------------+
|  Staff Service    |--------------->|   RabbitMQ/Event  |--------------------->|  Scheduler Engine    |
+-------------------+                |   Bus             |                     |  (Reacts to events)  |
|                   |                +-------------------+                     +----------------------+
|                   |                |                   |                     |                      |
|                   |                |                   |                     |                      |
|  Patient Service  |---PATIENT_*--->|                   |-------------------->|                      |
+-------------------+                |                   |                     |                      |
|                   |                |                   |                     |                      |
| Appointment Svc   |--APPOINTMENT_*>|                   |-------------------->|                      |
+-------------------+                +-------------------+                     +----------------------+
```

**Legend:**
- `PROVIDER_*` = All provider-related events (created, updated, terminated, availability, skill)
- `PATIENT_*` = All patient-related events (created, updated, address, preference)
- `APPOINTMENT_*` = All appointment-related events (created, updated, cancelled, rescheduled)

### Service Integration

The system provides service clients for external CareAXL microservices:

```python
# Example service usage
from src.services.scheduler_service import SchedulerService, SchedulerRequest, ServiceType

scheduler_service = SchedulerService(use_mock_services=True, use_rabbitmq=True)

request = SchedulerRequest(
    operation="get_providers",
    data={},
    service_type=ServiceType.SKILLED_NURSING
)
response = scheduler_service.process_request(request)
```

### Event Publishing

Events can be published for background processing:

```python
# Example event publishing
from src.events.rabbitmq_integration import publish_event_rabbitmq, EventType, EventPriority

await publish_event_rabbitmq(
    EventType.SCHEDULE_OPTIMIZATION_REQUESTED,
    {
        'optimization_type': 'assignment',
        'service_type': 'skilled_nursing',
        'target_date': '2024-01-15'
    },
    priority=EventPriority.HIGH
)
```

---

## 10. Project Structure

```
caxl-scheduling-engine/
├── scheduler.py                   # Main scheduler orchestration
├── assign_appointments.py         # Assignment job implementation
├── day_plan.py                    # Day planning job implementation
├── config/                        # Configuration files
│   ├── scheduler.yml              # Global scheduler configuration
│   ├── skilled_nursing.yml        # Skilled nursing service config
│   ├── behavioral_care.yml        # Behavioral care service config
│   ├── hospital_at_home.yml       # Hospital at home service config
│   └── pcs.yml                    # Personal care services config
├── src/                           # Source code
│   ├── model/                     # Domain models and planning models
│   │   ├── domain.py              # Core domain models
│   │   └── planning_models.py     # Timefold optimization models
│   ├── constraints/               # Constraint implementations
│   │   ├── base_constraints.py    # Base constraint classes
│   │   ├── assignment_constraints.py  # Assignment stage constraints
│   │   ├── day_constraints.py     # Day planning stage constraints
│   │   ├── config_registry.py     # Configuration registry
│   │   └── [individual constraint files]
│   ├── api/                       # API layer
│   │   ├── app.py                 # FastAPI application setup
│   │   ├── models.py              # API data models
│   │   ├── routes.py              # API endpoints
│   │   └── service_clients.py     # Service clients for external services
│   ├── services/                  # Service layer
│   │   └── scheduler_service.py   # Main scheduler service
│   ├── events/                    # Event system
│   │   ├── rabbitmq_integration.py # RabbitMQ integration
│   │   └── event_logger.py        # File-based event logging
│   └── data/                      # Data files and utilities
│       ├── data_loader.py         # Data loading utilities
│       ├── appointments.yml       # Appointment data
│       ├── providers.yml          # Provider data
│       ├── consumers.yml          # Consumer data
│       └── scenarios/             # Test scenarios
├── tests/                         # Test suite
├── docs/                          # Documentation
└── logs/                          # Application logs
```

---

## 11. Key Principles

- **Decoupled and Reactive:** Services and event queues are loosely coupled.
- **Real-Time Responsiveness:** Warm solver enables instant reaction to changes.
- **Cache-Based Warm Solvers:** No need to reload all data for every change.
- **No Full Data Reload:** Only affected facts/entities are updated.

---

## 12. Implementation Status

✅ **Completed**
- Two-stage optimization system (Assignment + Dayplan)
- Timefold integration with constraint solving
- Service layer with synchronous operations
- Event system with RabbitMQ integration
- Service clients for external microservices
- Configuration management with thread-local context
- File-based event logging
- Mock implementations for development

🔄 **In Progress**
- Production RabbitMQ deployment
- Real service integration

📋 **Planned**
- Performance testing and optimization
- Production deployment configuration

---

This document provides a complete, modular, and clear explanation of the implemented system, with diagrams and code snippets for each section. All features documented here are actually implemented in the codebase. 