2025-06-26 16:32:13,519 - __main__ - INFO - Loading data from scenario: geographic_clustering
2025-06-26 16:32:13,521 - caxl_scheduling_engine.data.data_loader - INFO - DataLoader initialized: file-based
2025-06-26 16:32:13,521 - caxl_scheduling_engine.data.data_loader - INFO - Starting data loading process...
2025-06-26 16:32:13,522 - caxl_scheduling_engine.data.data_loader - INFO - === STAGE 1: Loading Provider Data ===
2025-06-26 16:32:13,523 - caxl_scheduling_engine.data.data_loader - INFO - Reading provider data from: src\caxl_scheduling_engine\data\scenarios\geographic_clustering\providers.yml
2025-06-26 16:32:13,551 - caxl_scheduling_engine.data.data_loader - INFO -    - <PERSON>, <PERSON> (RN) - Skills: medication_management, wound_care, assessment
2025-06-26 16:32:13,554 - caxl_scheduling_engine.data.data_loader - INFO -    - <PERSON>, <PERSON> (LPN) - Skills: medication_administration, vital_signs, basic_care
2025-06-26 16:32:13,555 - caxl_scheduling_engine.data.data_loader - INFO -    - <PERSON>'<PERSON>, CNA (CNA) - Skills: personal_care, mobility_assistance, housekeeping
2025-06-26 16:32:13,556 - caxl_scheduling_engine.data.data_loader - INFO -    - David Thompson, RN (RN) - Skills: medication_management, wound_care, assessment
2025-06-26 16:32:13,557 - caxl_scheduling_engine.data.data_loader - INFO - === STAGE 2: Loading Consumer Data ===
2025-06-26 16:32:13,557 - caxl_scheduling_engine.data.data_loader - INFO - Reading consumer data from: src\caxl_scheduling_engine\data\scenarios\geographic_clustering\consumers.yml
2025-06-26 16:32:13,580 - caxl_scheduling_engine.data.data_loader - INFO -    - James Anderson - Episode: episode-dt-001
2025-06-26 16:32:13,580 - caxl_scheduling_engine.data.data_loader - INFO -    - Emily Chen - Episode: episode-dt-002
2025-06-26 16:32:13,580 - caxl_scheduling_engine.data.data_loader - INFO -    - Michael Rodriguez - Episode: episode-dt-003
2025-06-26 16:32:13,581 - caxl_scheduling_engine.data.data_loader - INFO -    - Sarah Johnson - Episode: episode-dt-004
2025-06-26 16:32:13,581 - caxl_scheduling_engine.data.data_loader - INFO -    - Robert Wilson - Episode: episode-mt-001
2025-06-26 16:32:13,581 - caxl_scheduling_engine.data.data_loader - INFO -    - Lisa Thompson - Episode: episode-mt-002
2025-06-26 16:32:13,582 - caxl_scheduling_engine.data.data_loader - INFO -    - David Kim - Episode: episode-mt-003
2025-06-26 16:32:13,582 - caxl_scheduling_engine.data.data_loader - INFO -    - Carmen Rodriguez - Episode: episode-mt-004
2025-06-26 16:32:13,582 - caxl_scheduling_engine.data.data_loader - INFO -    - Patricia O'Connor - Episode: episode-um-001
2025-06-26 16:32:13,583 - caxl_scheduling_engine.data.data_loader - INFO -    - Thomas Brown - Episode: episode-um-002
2025-06-26 16:32:13,583 - caxl_scheduling_engine.data.data_loader - INFO -    - Jennifer Davis - Episode: episode-um-003
2025-06-26 16:32:13,584 - caxl_scheduling_engine.data.data_loader - INFO -    - William Garcia - Episode: episode-um-004
2025-06-26 16:32:13,584 - caxl_scheduling_engine.data.data_loader - INFO - === STAGE 3: Loading Appointment Data ===
2025-06-26 16:32:13,585 - caxl_scheduling_engine.data.data_loader - INFO - Reading appointment data from: src\caxl_scheduling_engine\data\scenarios\geographic_clustering\appointments.yml
2025-06-26 16:32:13,603 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 60min - 2025-06-25
2025-06-26 16:32:13,604 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 45min - 2025-06-25
2025-06-26 16:32:13,604 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 75min - 2025-06-25
2025-06-26 16:32:13,605 - caxl_scheduling_engine.data.data_loader - INFO -    - URGENT None - 90min - 2025-06-25
2025-06-26 16:32:13,605 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 60min - 2025-06-25
2025-06-26 16:32:13,606 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 60min - 2025-06-25
2025-06-26 16:32:13,607 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 45min - 2025-06-25
2025-06-26 16:32:13,607 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 60min - 2025-06-25
2025-06-26 16:32:13,608 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 75min - 2025-06-25
2025-06-26 16:32:13,608 - caxl_scheduling_engine.data.data_loader - INFO -    - URGENT None - 90min - 2025-06-25
2025-06-26 16:32:13,608 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 90min - 2025-06-25
2025-06-26 16:32:13,609 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 60min - 2025-06-25
2025-06-26 16:32:13,609 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 90min - 2025-06-25
2025-06-26 16:32:13,609 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 60min - 2025-06-25
2025-06-26 16:32:13,609 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 45min - 2025-06-25
2025-06-26 16:32:13,610 - caxl_scheduling_engine.data.data_loader - INFO - === DATA LOADING SUMMARY ===
2025-06-26 16:32:13,610 - caxl_scheduling_engine.data.data_loader - INFO - Total records loaded:
2025-06-26 16:32:13,610 - caxl_scheduling_engine.data.data_loader - INFO -    - Providers: 4
2025-06-26 16:32:13,611 - caxl_scheduling_engine.data.data_loader - INFO -    - Consumers: 12
2025-06-26 16:32:13,612 - caxl_scheduling_engine.data.data_loader - INFO -    - Appointments: 15
2025-06-26 16:32:13,612 - caxl_scheduling_engine.data.data_loader - INFO - All data loaded successfully!
2025-06-26 16:32:13,613 - __main__ - INFO - Data loaded successfully: 4 providers, 12 consumers, 15 appointments
2025-06-26 16:32:13,614 - __main__ - INFO - Running assignment solver...
2025-06-26 16:32:13,614 - __main__ - ERROR - Assignment test failed: Scheduler configuration file not found: D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml
Please create the configuration file at D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml with required settings.
See the documentation for configuration format and examples.
2025-06-26 16:32:13,615 - __main__ - ERROR - Assignment test failed: Scheduler configuration file not found: D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml
Please create the configuration file at D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml with required settings.
See the documentation for configuration format and examples.
2025-06-26 16:32:13,615 - __main__ - INFO - Running dayplan solver...
2025-06-26 16:32:13,616 - __main__ - ERROR - Dayplan test failed: Scheduler configuration file not found: D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml
Please create the configuration file at D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml with required settings.
See the documentation for configuration format and examples.
2025-06-26 16:32:13,617 - __main__ - ERROR - Dayplan test failed: Scheduler configuration file not found: D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml
Please create the configuration file at D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml with required settings.
See the documentation for configuration format and examples.
