consumers:
  # Consumer requiring exact morning time (EST)
  - id: "time-consumer-001"
    name: "<PERSON>"
    location:
      latitude: 40.7580
      longitude: -73.9855
      city: "New York"
      state: "NY"
      address: "123 Park Avenue, Apt 4B, New York, NY 10016"
      timezone: "America/New_York"
    care_episode_id: "episode-time-001"
    consumer_preferences:
      preferred_days: [ "monday", "wednesday", "friday" ]
      preferred_hours: [ "09:00", "10:00" ]  # Exact 1-hour window
      strict_timing: true
      language: "English"

  # Consumer with flexible afternoon window (EST)
  - id: "time-consumer-002"
    name: "<PERSON> Martinez"
    location:
      latitude: 40.7505
      longitude: -73.9934
      city: "New York"
      state: "NY"
      address: "456 Madison Avenue, Suite 8, New York, NY 10022"
      timezone: "America/New_York"
    care_episode_id: "episode-time-002"
    consumer_preferences:
      preferred_days: [ "tuesday", "thursday" ]
      preferred_hours: [ "13:00", "17:00" ]  # Flexible 4-hour window
      strict_timing: false
      language: "Spanish"

  # Consumer requiring early morning care (EST)
  - id: "time-consumer-003"
    name: "<PERSON>"
    location:
      latitude: 40.7829
      longitude: -73.9654
      city: "New York"
      state: "NY"
      address: "789 5th Avenue, Apt 12C, New York, NY 10065"
      timezone: "America/New_York"
    care_episode_id: "episode-time-003"
    consumer_preferences:
      preferred_days: [ "monday", "tuesday", "wednesday", "thursday", "friday" ]
      preferred_hours: [ "06:30", "08:00" ]  # Early morning window
      strict_timing: true
      language: "English"

  # Consumer in different timezone (CST)
  - id: "time-consumer-004"
    name: "James Anderson"
    location:
      latitude: 41.8781
      longitude: -87.6298
      city: "Chicago"
      state: "IL"
      address: "123 State Street, Chicago, IL 60601"
      timezone: "America/Chicago"
    care_episode_id: "episode-time-004"
    consumer_preferences:
      preferred_days: [ "monday", "wednesday", "friday" ]
      preferred_hours: [ "15:00", "18:00" ]  # 3 PM - 6 PM CST
      strict_timing: false
      language: "English"

  # Consumer with evening preference (CST)
  - id: "time-consumer-005"
    name: "Linda Garcia"
    location:
      latitude: 41.8781
      longitude: -87.6298
      city: "Chicago"
      state: "IL"
      address: "456 Michigan Avenue, Chicago, IL 60611"
      timezone: "America/Chicago"
    care_episode_id: "episode-time-005"
    consumer_preferences:
      preferred_days: [ "tuesday", "thursday" ]
      preferred_hours: [ "19:00", "21:00" ]  # Evening window
      strict_timing: true
      language: "Spanish"

  # Consumer with weekend preference (PST)
  - id: "time-consumer-006"
    name: "Patricia Lee"
    location:
      latitude: 34.0522
      longitude: -118.2437
      city: "Los Angeles"
      state: "CA"
      address: "789 Hollywood Boulevard, Los Angeles, CA 90028"
      timezone: "America/Los_Angeles"
    care_episode_id: "episode-time-006"
    consumer_preferences:
      preferred_days: [ "saturday", "sunday" ]
      preferred_hours: [ "10:00", "16:00" ]  # Weekend window
      strict_timing: false
      language: "English"
