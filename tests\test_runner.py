#!/usr/bin/env python3
"""
Comprehensive test runner for appointment scheduler scenarios.

This script:
1. Iterates through each subfolder under data/scenarios
2. Loads data using data_loader.py
3. Executes the appropriate job (assignment or dayplan) using scheduler.py
4. Stores output logs inside the corresponding scenario folder
"""

import argparse
import logging
import sys
import time
from datetime import date, datetime
from pathlib import Path
from typing import Dict, Any, List

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from caxl_scheduling_engine.data.data_loader import DataLoader
from caxl_scheduling_engine.scheduler import AppointmentScheduler

logger = logging.getLogger(__name__)


class ScenarioTestRunner:
    """Test runner for appointment scheduler scenarios."""

    def __init__(self, scenarios_dir: str = "src/caxl_scheduling_engine/data/scenarios", output_logs: bool = True):
        self.scenarios_dir = Path(scenarios_dir)
        self.output_logs = output_logs
        self.results = []

        if not self.scenarios_dir.exists():
            raise FileNotFoundError(f"Scenarios directory not found: {self.scenarios_dir}")

    def discover_scenarios(self) -> List[Path]:
        """Discover all scenario folders."""
        scenarios = []
        for item in self.scenarios_dir.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                # Check if it has the required YAML files
                required_files = ['providers.yml', 'consumers.yml', 'appointments.yml']
                if all((item / file).exists() for file in required_files):
                    scenarios.append(item)
                else:
                    logger.warning(f"Skipping {item.name}: missing required YAML files")

        return sorted(scenarios)

    def setup_logging_for_scenario(self, scenario_name: str) -> str:
        """Setup logging to capture output for a specific scenario."""
        if not self.output_logs:
            return ""

        log_file = self.scenarios_dir / scenario_name / f"test_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        # Create file handler
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        # Add to root logger
        root_logger = logging.getLogger()
        root_logger.addHandler(file_handler)

        return str(log_file)

    def cleanup_logging(self):
        """Remove file handlers from logger."""
        root_logger = logging.getLogger()
        handlers_to_remove = [h for h in root_logger.handlers if isinstance(h, logging.FileHandler)]
        for handler in handlers_to_remove:
            root_logger.removeHandler(handler)
            handler.close()

    def load_scenario_data(self, scenario_path: Path) -> Dict[str, Any]:
        """Load data from a specific scenario using the data loader."""
        logger.info(f"Loading data from scenario: {scenario_path.name}")

        try:
            # Create data loader for the specific scenario
            data_loader = DataLoader(str(scenario_path))
            data = data_loader.load_all_data()

            logger.info(
                f"Data loaded successfully: {len(data['providers'])} providers, {len(data['consumers'])} consumers, {len(data['appointments'])} appointments")
            return data

        except Exception as e:
            logger.error(f"Failed to load data from scenario {scenario_path.name}: {e}")
            raise

    def run_assignment_test(self, scenario_path: Path, scenario_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run assignment solver test for a scenario."""
        logger.info("Running assignment solver...")

        try:
            # Create scheduler
            scheduler = AppointmentScheduler(config_folder="src/caxl_scheduling_engine/config", daemon_mode=False)

            # Temporarily patch the create_demo_data function to use scenario data
            import caxl_scheduling_engine.data.data_loader as data_loader_module
            original_create_demo_data = data_loader_module.create_demo_data

            def scenario_create_demo_data():
                return scenario_data

            data_loader_module.create_demo_data = scenario_create_demo_data

            start_time = time.time()
            result = scheduler.run_once("assign")
            end_time = time.time()

            # Restore original function
            data_loader_module.create_demo_data = original_create_demo_data

            return {
                "success": True,
                "job_type": "assignment",
                "processing_time": end_time - start_time,
                "result": result
            }

        except Exception as e:
            logger.error(f"Assignment test failed: {e}")
            return {
                "success": False,
                "job_type": "assignment",
                "error": str(e),
                "processing_time": 0
            }

    def run_dayplan_test(self, scenario_path: Path, scenario_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run dayplan solver test for a scenario."""
        logger.info("Running dayplan solver...")

        try:
            # Create scheduler
            scheduler = AppointmentScheduler(config_folder="src/caxl_scheduling_engine/config", daemon_mode=False)

            # Temporarily patch the create_demo_data function to use scenario data
            import caxl_scheduling_engine.data.data_loader as data_loader_module
            original_create_demo_data = data_loader_module.create_demo_data

            def scenario_create_demo_data():
                return scenario_data

            data_loader_module.create_demo_data = scenario_create_demo_data

            start_time = time.time()
            result = scheduler.run_once("dayplan", target_date=date.today())
            end_time = time.time()

            # Restore original function
            data_loader_module.create_demo_data = original_create_demo_data

            return {
                "success": True,
                "job_type": "dayplan",
                "processing_time": end_time - start_time,
                "result": result.__dict__ if hasattr(result, '__dict__') else result
            }

        except Exception as e:
            logger.error(f"Dayplan test failed: {e}")
            return {
                "success": False,
                "job_type": "dayplan",
                "error": str(e),
                "processing_time": 0
            }

    def run_scenario_tests(self, scenario_path: Path) -> Dict[str, Any]:
        """Run all tests for a single scenario."""
        scenario_name = scenario_path.name
        logger.info(f"Testing scenario: {scenario_name}")

        # Setup logging
        log_file = self.setup_logging_for_scenario(scenario_name)

        scenario_result = {
            "scenario_name": scenario_name,
            "scenario_path": str(scenario_path),
            "log_file": log_file,
            "timestamp": datetime.now().isoformat(),
            "tests": []
        }

        try:
            # Load scenario data
            scenario_data = self.load_scenario_data(scenario_path)

            # Run assignment test
            assignment_result = self.run_assignment_test(scenario_path, scenario_data)
            scenario_result["tests"].append(assignment_result)

            if assignment_result["success"]:
                logger.info(f"Assignment test passed ({assignment_result['processing_time']:.2f}s)")
            else:
                logger.error(f"Assignment test failed: {assignment_result.get('error', 'Unknown error')}")

            # Run dayplan test
            dayplan_result = self.run_dayplan_test(scenario_path, scenario_data)
            scenario_result["tests"].append(dayplan_result)

            if dayplan_result["success"]:
                logger.info(f"Dayplan test passed ({dayplan_result['processing_time']:.2f}s)")
            else:
                logger.error(f"Dayplan test failed: {dayplan_result.get('error', 'Unknown error')}")

            # Overall scenario success
            scenario_result["success"] = all(test["success"] for test in scenario_result["tests"])

        except Exception as e:
            logger.error(f"Scenario failed: {e}")
            scenario_result["success"] = False
            scenario_result["error"] = str(e)

        finally:
            # Cleanup logging
            self.cleanup_logging()

        return scenario_result

    def run_all_scenarios(self) -> Dict[str, Any]:
        """Run tests for all discovered scenarios."""
        logger.info("Starting comprehensive scenario testing")

        scenarios = self.discover_scenarios()
        logger.info(f"Discovered {len(scenarios)} scenarios to test")

        summary = {
            "total_scenarios": len(scenarios),
            "successful_scenarios": 0,
            "failed_scenarios": 0,
            "total_processing_time": 0,
            "scenarios": [],
            "timestamp": datetime.now().isoformat()
        }

        for i, scenario_path in enumerate(scenarios, 1):
            logger.info(f"Processing scenario {i}/{len(scenarios)}: {scenario_path.name}")

            start_time = time.time()
            scenario_result = self.run_scenario_tests(scenario_path)
            end_time = time.time()

            scenario_result["total_time"] = end_time - start_time
            summary["scenarios"].append(scenario_result)
            summary["total_processing_time"] += scenario_result["total_time"]

            if scenario_result["success"]:
                summary["successful_scenarios"] += 1
                logger.info(f"Scenario {scenario_path.name} completed successfully")
            else:
                summary["failed_scenarios"] += 1
                logger.error(f"Scenario {scenario_path.name} failed")

        logger.info("All scenarios completed")
        return summary

    def print_summary(self, summary: Dict[str, Any]):
        """Print a summary of test results."""
        logger.info("=== TEST SUMMARY ===")
        logger.info(f"Total scenarios: {summary['total_scenarios']}")
        logger.info(f"Successful: {summary['successful_scenarios']}")
        logger.info(f"Failed: {summary['failed_scenarios']}")
        logger.info(f"Total processing time: {summary['total_processing_time']:.2f}s")

        if summary['failed_scenarios'] > 0:
            logger.info("Failed scenarios:")
            for scenario in summary['scenarios']:
                if not scenario['success']:
                    logger.info(f"  - {scenario['scenario_name']}: {scenario.get('error', 'Unknown error')}")


def main():
    """Main entry point for the test runner."""
    parser = argparse.ArgumentParser(description='Scenario Test Runner')
    parser.add_argument('--scenarios-dir', default='src/caxl_scheduling_engine/data/scenarios',
                        help='Path to scenarios directory')
    parser.add_argument('--no-logs', action='store_true',
                        help='Disable output log files')
    parser.add_argument('--scenario', help='Run specific scenario only')

    args = parser.parse_args()

    # Setup basic logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        runner = ScenarioTestRunner(args.scenarios_dir, output_logs=not args.no_logs)
        summary = None

        if args.scenario:
            # Run specific scenario
            scenario_path = Path(args.scenarios_dir) / args.scenario
            if not scenario_path.exists():
                logger.error(f"Scenario {args.scenario} not found")
                sys.exit(1)

            logger.info(f"Running single scenario: {args.scenario}")
            result = runner.run_scenario_tests(scenario_path)
            summary = {
                "total_scenarios": 1,
                "successful_scenarios": 1 if result["success"] else 0,
                "failed_scenarios": 0 if result["success"] else 1,
                "total_processing_time": result.get("total_time", 0),
                "scenarios": [result],
                "timestamp": datetime.now().isoformat()
            }
            runner.print_summary(summary)
        else:
            # Run all scenarios
            summary = runner.run_all_scenarios()
            runner.print_summary(summary)

        # Exit with error code if any scenarios failed
        if summary and summary.get('failed_scenarios', 0) > 0:
            sys.exit(1)

    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
