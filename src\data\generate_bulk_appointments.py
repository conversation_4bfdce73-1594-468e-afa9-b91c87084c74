import uuid
import random
import yaml
from datetime import datetime, timedelta, time, date

# Skills pool for random selection
SKILLS = [
    "assessment", "medication_management", "wound_care", "iv_therapy", "personal_care",
    "mobility_assistance", "housekeeping", "meal_assistance", "vital_signs"
]

# Name pools for random generation
FIRST_NAMES = [
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
]
LAST_NAMES = [
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
]

# Generate a random name
random_name = lambda: f"{random.choice(FIRST_NAMES)} {random.choice(LAST_NAMES)}"

# Generate a random address
random_address = lambda i: f"{100 + i} Example Ave, New York, NY 10001"

# Generate a random date in a range
start_date = datetime.strptime("2025-06-27", "%Y-%m-%d")
random_date = lambda: (start_date + timedelta(days=random.randint(0, 6))).strftime("%Y-%m-%d")

# Generate timing constraints
def generate_timing():
    timing = {
        "is_timed_visit": False,
        "preferred_time": None,
        "time_flexibility_minutes": 15,
        "earliest_start": None,
        "latest_end": None
    }
    
    # 20% chance of being a timed visit
    if random.random() < 0.2:
        timing["is_timed_visit"] = True
        hour = random.randint(8, 16)
        minute = random.choice([0, 15, 30, 45])
        timing["preferred_time"] = f"{hour:02d}:{minute:02d}"
        timing["time_flexibility_minutes"] = random.choice([5, 10, 15, 30])
    
    return timing

# Generate relationships
def generate_relationships():
    relationships = {
        "care_episode_id": None,
        "related_appointment_ids": [],
        "prerequisite_appointment_ids": [],
        "sequence_order": None,
        "same_provider_required": False
    }
    
    # 15% chance of being part of a care episode
    if random.random() < 0.15:
        relationships["care_episode_id"] = f"episode-{random.randint(1000, 9999)}"
        relationships["same_provider_required"] = random.choice([True, False])
    
    # 10% chance of having sequence order
    if random.random() < 0.1:
        relationships["sequence_order"] = random.randint(1, 5)
    
    return relationships

# Generate pinning
def generate_pinning():
    pinning = {
        "is_pinned": False,
        "pin_provider": False,
        "pin_date": False,
        "pin_time": False,
        "pin_reason": None
    }
    
    # 5% chance of being pinned
    if random.random() < 0.05:
        pinning["is_pinned"] = True
        pinning["pin_reason"] = random.choice(["in_progress", "completed", "patient_request"])
        pinning["pin_provider"] = random.choice([True, False])
        pinning["pin_date"] = random.choice([True, False])
        pinning["pin_time"] = random.choice([True, False])
    
    return pinning

# Generate 100 appointments
bulk_appointments = []
for i in range(1, 101):
    appointment = {
        "id": str(uuid.uuid4()),
        "consumer_id": str(uuid.uuid4()),  # Will be linked to actual consumer IDs
        "appointment_date": random_date(),
        "required_skills": random.sample(SKILLS, k=random.randint(1, 3)),
        "duration_min": random.choice([30, 45, 60, 75, 90, 120]),
        "urgent": random.choice([True, False]),
        "active": True,
        "status": "PENDING_TO_ASSIGN",
        "location": {
            "latitude": round(random.uniform(40.70, 40.80), 5),
            "longitude": round(random.uniform(-74.02, -73.95), 5),
            "address": random_address(i),
            "city": "New York",
            "state": "NY",
            "country": "USA",
            "zip_code": "10001"
        },
        "priority": random.choice(["low", "normal", "high", "urgent"]),
        "task_points": random.choice([1, 2, 3, 4, 5]),
        "required_role": random.choice([None, "RN", "LPN", "CNA", "PT", "OT"]),
        "timing": generate_timing(),
        "relationships": generate_relationships(),
        "pinning": generate_pinning(),
        "properties": {}
    }
    bulk_appointments.append(appointment)

# Load the existing YAML
try:
    with open("appointments.yml", "r", encoding="utf-8") as f:
        data = yaml.safe_load(f)
    # Handle empty file or None
    if data is None:
        data = {"appointments": []}
except FileNotFoundError:
    data = {"appointments": []}

# Append the new appointments
if "appointments" in data:
    data["appointments"].extend(bulk_appointments)
else:
    data["appointments"] = bulk_appointments

# Write back to the YAML file
with open("appointments.yml", "w", encoding="utf-8") as f:
    yaml.dump(data, f, sort_keys=False, allow_unicode=True)

print("Appended 100 bulk appointments to appointments.yml") 