services:
  # Scheduler Daemon Service - runs scheduling jobs in daemon mode
  caxl_scheduler:
    build:
      context: .
      dockerfile: Dockerfile.dev
    environment:
      - ENVIRONMENT=development
      - DEBUG=True
      - LOG_LEVEL=INFO
      - PYTHONPATH=/app
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
    command: python scheduler.py --mode daemon
    restart: unless-stopped
