2025-06-26 16:32:13,423 - __main__ - INFO - Loading data from scenario: continuity_of_care
2025-06-26 16:32:13,424 - caxl_scheduling_engine.data.data_loader - INFO - DataLoader initialized: file-based
2025-06-26 16:32:13,425 - caxl_scheduling_engine.data.data_loader - INFO - Starting data loading process...
2025-06-26 16:32:13,425 - caxl_scheduling_engine.data.data_loader - INFO - === STAGE 1: Loading Provider Data ===
2025-06-26 16:32:13,425 - caxl_scheduling_engine.data.data_loader - INFO - Reading provider data from: src\caxl_scheduling_engine\data\scenarios\continuity_of_care\providers.yml
2025-06-26 16:32:13,435 - caxl_scheduling_engine.data.data_loader - INFO -    - Dr. <PERSON>, R<PERSON> (RN) - Skills: medication_management, diabetes_management, assessment
2025-06-26 16:32:13,440 - caxl_scheduling_engine.data.data_loader - INFO -    - Dr<PERSON> <PERSON>, <PERSON><PERSON> (RN) - Skills: wound_care, assessment, medication_management
2025-06-26 16:32:13,440 - caxl_scheduling_engine.data.data_loader - INFO -    - <PERSON> <PERSON>, <PERSON><PERSON> (CNA) - Skills: personal_care, mobility_assistance, housekeeping
2025-06-26 16:32:13,441 - caxl_scheduling_engine.data.data_loader - INFO - === STAGE 2: Loading Consumer Data ===
2025-06-26 16:32:13,441 - caxl_scheduling_engine.data.data_loader - INFO - Reading consumer data from: src\caxl_scheduling_engine\data\scenarios\continuity_of_care\consumers.yml
2025-06-26 16:32:13,454 - caxl_scheduling_engine.data.data_loader - INFO -    - Margaret Smith - Episode: episode-diabetes-001
2025-06-26 16:32:13,454 - caxl_scheduling_engine.data.data_loader - INFO -    - Robert Johnson - Episode: episode-wound-001
2025-06-26 16:32:13,454 - caxl_scheduling_engine.data.data_loader - INFO -    - Carmen Rodriguez - Episode: episode-mobility-001
2025-06-26 16:32:13,455 - caxl_scheduling_engine.data.data_loader - INFO -    - David Kim - Episode: episode-diabetes-002
2025-06-26 16:32:13,455 - caxl_scheduling_engine.data.data_loader - INFO -    - Lisa Thompson - Episode: episode-wound-002
2025-06-26 16:32:13,455 - caxl_scheduling_engine.data.data_loader - INFO -    - Thomas Brown - Episode: episode-mobility-002
2025-06-26 16:32:13,455 - caxl_scheduling_engine.data.data_loader - INFO - === STAGE 3: Loading Appointment Data ===
2025-06-26 16:32:13,456 - caxl_scheduling_engine.data.data_loader - INFO - Reading appointment data from: src\caxl_scheduling_engine\data\scenarios\continuity_of_care\appointments.yml
2025-06-26 16:32:13,493 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 60min - 2025-06-25
2025-06-26 16:32:13,494 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 45min - 2025-06-25
2025-06-26 16:32:13,496 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 60min - 2025-06-25
2025-06-26 16:32:13,496 - caxl_scheduling_engine.data.data_loader - INFO -    - URGENT None - 90min - 2025-06-25
2025-06-26 16:32:13,497 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 75min - 2025-06-25
2025-06-26 16:32:13,498 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 60min - 2025-06-25
2025-06-26 16:32:13,499 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 45min - 2025-06-25
2025-06-26 16:32:13,500 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 90min - 2025-06-25
2025-06-26 16:32:13,501 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 60min - 2025-06-25
2025-06-26 16:32:13,502 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 75min - 2025-06-25
2025-06-26 16:32:13,503 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 60min - 2025-06-25
2025-06-26 16:32:13,504 - caxl_scheduling_engine.data.data_loader - INFO -    - Regular None - 45min - 2025-06-25
2025-06-26 16:32:13,504 - caxl_scheduling_engine.data.data_loader - INFO - === DATA LOADING SUMMARY ===
2025-06-26 16:32:13,505 - caxl_scheduling_engine.data.data_loader - INFO - Total records loaded:
2025-06-26 16:32:13,506 - caxl_scheduling_engine.data.data_loader - INFO -    - Providers: 3
2025-06-26 16:32:13,507 - caxl_scheduling_engine.data.data_loader - INFO -    - Consumers: 6
2025-06-26 16:32:13,507 - caxl_scheduling_engine.data.data_loader - INFO -    - Appointments: 12
2025-06-26 16:32:13,508 - caxl_scheduling_engine.data.data_loader - INFO - All data loaded successfully!
2025-06-26 16:32:13,509 - __main__ - INFO - Data loaded successfully: 3 providers, 6 consumers, 12 appointments
2025-06-26 16:32:13,510 - __main__ - INFO - Running assignment solver...
2025-06-26 16:32:13,511 - __main__ - ERROR - Assignment test failed: Scheduler configuration file not found: D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml
Please create the configuration file at D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml with required settings.
See the documentation for configuration format and examples.
2025-06-26 16:32:13,512 - __main__ - ERROR - Assignment test failed: Scheduler configuration file not found: D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml
Please create the configuration file at D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml with required settings.
See the documentation for configuration format and examples.
2025-06-26 16:32:13,513 - __main__ - INFO - Running dayplan solver...
2025-06-26 16:32:13,514 - __main__ - ERROR - Dayplan test failed: Scheduler configuration file not found: D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml
Please create the configuration file at D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml with required settings.
See the documentation for configuration format and examples.
2025-06-26 16:32:13,515 - __main__ - ERROR - Dayplan test failed: Scheduler configuration file not found: D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml
Please create the configuration file at D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml with required settings.
See the documentation for configuration format and examples.
