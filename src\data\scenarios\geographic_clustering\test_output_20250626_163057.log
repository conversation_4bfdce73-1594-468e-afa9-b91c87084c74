2025-06-26 16:30:57,315 - __main__ - INFO - Loading data from scenario: geographic_clustering
2025-06-26 16:30:57,315 - caxl_scheduling_engine.data.data_loader - INFO - DataLoader initialized: file-based
2025-06-26 16:30:57,315 - caxl_scheduling_engine.data.data_loader - INFO - Starting data loading process...
2025-06-26 16:30:57,316 - caxl_scheduling_engine.data.data_loader - INFO - === STAGE 1: Loading Provider Data ===
2025-06-26 16:30:57,316 - caxl_scheduling_engine.data.data_loader - INFO - Reading provider data from: src\caxl_scheduling_engine\data\scenarios\geographic_clustering\providers.yml
2025-06-26 16:30:57,326 - caxl_scheduling_engine.data.data_loader - INFO -    - <PERSON>, R<PERSON> (RN) - Skills: medication_management, wound_care, assessment
2025-06-26 16:30:57,361 - caxl_scheduling_engine.data.data_loader - INFO -    - <PERSON>, <PERSON><PERSON> (LPN) - Skills: medication_administration, vital_signs, basic_care
2025-06-26 16:30:57,362 - caxl_scheduling_engine.data.data_loader - INFO -    - <PERSON>, <PERSON><PERSON> (CNA) - Skills: personal_care, mobility_assistance, housekeeping
2025-06-26 16:30:57,362 - caxl_scheduling_engine.data.data_loader - INFO -    - David Thompson, RN (RN) - Skills: medication_management, wound_care, assessment
2025-06-26 16:30:57,363 - caxl_scheduling_engine.data.data_loader - INFO - === STAGE 2: Loading Consumer Data ===
2025-06-26 16:30:57,363 - caxl_scheduling_engine.data.data_loader - INFO - Reading consumer data from: src\caxl_scheduling_engine\data\scenarios\geographic_clustering\consumers.yml
2025-06-26 16:30:57,380 - caxl_scheduling_engine.data.data_loader - INFO -    - James Anderson - Episode: episode-dt-001
2025-06-26 16:30:57,381 - caxl_scheduling_engine.data.data_loader - INFO -    - Emily Chen - Episode: episode-dt-002
2025-06-26 16:30:57,381 - caxl_scheduling_engine.data.data_loader - INFO -    - Michael Rodriguez - Episode: episode-dt-003
2025-06-26 16:30:57,381 - caxl_scheduling_engine.data.data_loader - INFO -    - Sarah Johnson - Episode: episode-dt-004
2025-06-26 16:30:57,382 - caxl_scheduling_engine.data.data_loader - INFO -    - Robert Wilson - Episode: episode-mt-001
2025-06-26 16:30:57,382 - caxl_scheduling_engine.data.data_loader - INFO -    - Lisa Thompson - Episode: episode-mt-002
2025-06-26 16:30:57,383 - caxl_scheduling_engine.data.data_loader - INFO -    - David Kim - Episode: episode-mt-003
2025-06-26 16:30:57,383 - caxl_scheduling_engine.data.data_loader - INFO -    - Carmen Rodriguez - Episode: episode-mt-004
2025-06-26 16:30:57,383 - caxl_scheduling_engine.data.data_loader - INFO -    - Patricia O'Connor - Episode: episode-um-001
2025-06-26 16:30:57,384 - caxl_scheduling_engine.data.data_loader - INFO -    - Thomas Brown - Episode: episode-um-002
2025-06-26 16:30:57,384 - caxl_scheduling_engine.data.data_loader - INFO -    - Jennifer Davis - Episode: episode-um-003
2025-06-26 16:30:57,384 - caxl_scheduling_engine.data.data_loader - INFO -    - William Garcia - Episode: episode-um-004
2025-06-26 16:30:57,385 - caxl_scheduling_engine.data.data_loader - INFO - === STAGE 3: Loading Appointment Data ===
2025-06-26 16:30:57,385 - caxl_scheduling_engine.data.data_loader - INFO - Reading appointment data from: src\caxl_scheduling_engine\data\scenarios\geographic_clustering\appointments.yml
2025-06-26 16:30:57,446 - caxl_scheduling_engine.data.data_loader - INFO - === DATA LOADING SUMMARY ===
2025-06-26 16:30:57,446 - caxl_scheduling_engine.data.data_loader - INFO - Total records loaded:
2025-06-26 16:30:57,446 - caxl_scheduling_engine.data.data_loader - INFO -    - Providers: 4
2025-06-26 16:30:57,446 - caxl_scheduling_engine.data.data_loader - INFO -    - Consumers: 12
2025-06-26 16:30:57,447 - caxl_scheduling_engine.data.data_loader - INFO -    - Appointments: 15
2025-06-26 16:30:57,447 - caxl_scheduling_engine.data.data_loader - INFO - All data loaded successfully!
2025-06-26 16:30:57,447 - __main__ - INFO - Data loaded successfully: 4 providers, 12 consumers, 15 appointments
2025-06-26 16:30:57,447 - __main__ - INFO - Running assignment solver...
2025-06-26 16:30:57,448 - __main__ - ERROR - Assignment test failed: Scheduler configuration file not found: D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml
Please create the configuration file at D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml with required settings.
See the documentation for configuration format and examples.
2025-06-26 16:30:57,448 - __main__ - ERROR - Assignment test failed: Scheduler configuration file not found: D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml
Please create the configuration file at D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml with required settings.
See the documentation for configuration format and examples.
2025-06-26 16:30:57,448 - __main__ - INFO - Running dayplan solver...
2025-06-26 16:30:57,449 - __main__ - ERROR - Dayplan test failed: Scheduler configuration file not found: D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml
Please create the configuration file at D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml with required settings.
See the documentation for configuration format and examples.
2025-06-26 16:30:57,450 - __main__ - ERROR - Dayplan test failed: Scheduler configuration file not found: D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml
Please create the configuration file at D:\Work\Scheduler\caxl-scheduling-engine\src\src\caxl_scheduling_engine\config\scheduler.yml with required settings.
See the documentation for configuration format and examples.
