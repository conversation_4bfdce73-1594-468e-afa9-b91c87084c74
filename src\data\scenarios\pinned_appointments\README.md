# Pinned Appointments Scenarios

**Purpose**: Test appointment pinning behavior where certain appointments should not be reassigned
**Best for**: Testing constraint handling and assignment stability
**Complexity**: Medium

## Features Demonstrated

- Appointments pinned to specific providers
- Appointments pinned to specific time slots
- Different pin reasons and their priorities
- Unpinnable vs non-unpinnable appointments
- Cascade effects of pinned appointments on scheduling

## Data Overview

- **Providers**: 3 (different specialties)
- **Patients**: 6 (various pinning requirements)
- **Appointments**: 10 (mix of pinned and unpinned)
- **Geographic Coverage**: New York Metro Area

## Test Dimensions Covered

- **provider_assignment**: pinned_assignments, provider_consistency
- **time_matching**: pinned_time_slots, strict_timing
- **continuity_of_care**: episode_continuity, care_consistency
- **preferences**: provider_preferences, patient_preferences

## Special Test Cases

1. **Provider Pinning**: Appointments that must use specific provider
2. **Time Slot Pinning**: Appointments that must occur at specific times
3. **Episode Continuity**: Appointments requiring same provider within episode
4. **Unpinnable Constraints**: Some pins can be overridden, others cannot

## Usage

```bash
# Copy this scenario to main data folder
cp -r data/scenarios/pinned_appointments/* data/

# Run assignment job
python -m appointment_scheduler.jobs.assign_appointments

# Run day plan job  
python -m appointment_scheduler.jobs.day_plan
```

## Expected Outcomes

- Pinned appointments should maintain their assignments
- Non-pinned appointments should work around pinned constraints
- System should respect pin priorities and reasons
- Some appointments may remain unassigned due to pinning conflicts
