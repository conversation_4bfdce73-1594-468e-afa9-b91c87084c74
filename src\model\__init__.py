# Auto-generated to make this directory a Python package.

"""
Domain models for healthcare scheduling optimization.
"""

from .domain import (
    Provider,
    Consumer,
    ServiceConfig,
    SchedulerConfig,
    Location,
    Geofence,
    DateSpecificProviderAvailability,
    ProviderAvailability,
    ShiftPattern,
    ProviderCapacity,
    ProviderPreferences,
    ConsumerPreferences,
    AppointmentStatus,
    AppointmentPinning,
    AppointmentTiming,
    AppointmentRelationships,
    AppointmentData,
    ScheduledAppointment,
    TimeSlotAssignment,
    DaySchedule,
    Holiday,
    BlackoutPeriod,
    WeekendCoverage,
    GlobalAvailability,
    ServiceAvailability,
    GeographicCluster,
    AssignmentResult,
    BatchAssignmentResult
)

__all__ = [
    'Provider',
    'Consumer',
    'ServiceConfig',
    'SchedulerConfig',
    'Location',
    'Geofence',
    'DateSpecificProviderAvailability',
    'ProviderAvailability',
    'ShiftPattern',
    'ProviderCapacity',
    'ProviderPreferences',
    'ConsumerPreferences',
    'AppointmentStatus',
    'AppointmentPinning',
    'AppointmentTiming',
    'AppointmentRelationships',
    'AppointmentData',
    'ScheduledAppointment',
    'TimeSlotAssignment',
    'DaySchedule',
    'Holiday',
    'BlackoutPeriod',
    'WeekendCoverage',
    'GlobalAvailability',
    'ServiceAvailability',
    'GeographicCluster',
    'AssignmentResult',
    'BatchAssignmentResult'
]
