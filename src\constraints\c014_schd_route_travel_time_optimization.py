"""
Route Travel Time Optimization Constraints for Day Planning (C014)

This module provides route travel time optimization constraints for the day plan stage.
Using simplified constraint patterns to avoid Timefold API issues.
"""

from datetime import date, time
from math import radians, sin, cos, sqrt, asin
from typing import Dict, List, Any, Optional
import requests
from timefold.solver.score import ConstraintFactory, HardSoftScore, Constraint, Joiners

from constraints.base_constraints import with_config
from constraints.config_registry import ConfigRegistry
from model.domain import Location, AppointmentData
from model.planning_models import TimeSlotAssignment

INSUFFICIENT_TRAVEL_TIME = "insufficientTravelTime"

# --- Public Travel Utility Functions (moved from utils) ---
def _get_traffic_config() -> Dict[str, Any]:
    """Get traffic configuration from config registry."""
    try:
        scheduler_config = ConfigRegistry.get_core_config('scheduler')
        if scheduler_config and 'traffic_integration' in scheduler_config:
            return scheduler_config['traffic_integration']
        return {}
    except Exception:
        return {}


def _get_traffic_model_config() -> Dict[str, Any]:
    """Get traffic model configuration from config registry."""
    try:
        scheduler_config = ConfigRegistry.get_core_config('scheduler')
        if scheduler_config and 'traffic_model' in scheduler_config:
            return scheduler_config['traffic_model']
        return {}
    except Exception:
        return {}


def _calculate_distance_haversine(loc1: Location, loc2: Location) -> float:
    """Calculate distance between two locations in miles using Haversine formula."""
    if loc1 is None or loc2 is None:
        return 0.0

    # Convert decimal degrees to radians
    lat1, lon1 = radians(loc1.latitude), radians(loc1.longitude)
    lat2, lon2 = radians(loc2.latitude), radians(loc2.longitude)

    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
    c = 2 * asin(sqrt(a))

    # Radius of Earth in miles
    r = 3956
    return c * r


def _is_urban_area(location: Location) -> bool:
    """Determine if a location is in an urban area based on configurable city list."""
    if location.city is None:
        return False

    # Get configurable urban cities from configuration
    traffic_model = _get_traffic_model_config()
    urban_cities = traffic_model.get('urban_cities', [
        "New York", "Los Angeles", "Chicago", "Houston", "Phoenix",
        "Philadelphia", "San Antonio", "San Diego", "Dallas", "San Jose"
    ])

    return location.city in urban_cities


def _get_speed_factor(location: Location, target_time: Optional[time] = None,
                      target_date: Optional[date] = None) -> float:
    """Calculate speed factor based on location, time, and weather conditions."""
    if location is None:
        return 1.0

    # Get traffic configuration
    traffic_config = _get_traffic_config()
    traffic_model = _get_traffic_model_config()
    
    # Base speed factor
    base_speed_factor = 1.0

    # Time-based adjustments
    time_factor = 1.0
    if target_time:
        hour = target_time.hour
        # Rush hour adjustments
        if 7 <= hour <= 9 or 16 <= hour <= 18:
            time_factor = traffic_config.get('patterns', {}).get('time_of_day_factors', {}).get('rush_hour_morning', 0.7)
        elif 22 <= hour or hour <= 6:
            time_factor = traffic_config.get('patterns', {}).get('time_of_day_factors', {}).get('night_multiplier', 1.2)

    # Urban area adjustments
    urban_factor = 1.0
    if _is_urban_area(location):
        urban_factor = traffic_model.get('speed_factors', {}).get('urban_speed_mph', 25) / 30.0  # Normalize to 30 mph

    # Calculate final speed factor
    final_factor = base_speed_factor * time_factor * urban_factor
    
    # Ensure factor is within reasonable bounds
    min_factor = 0.5
    max_factor = 1.5
    
    return max(min_factor, min(max_factor, final_factor))


def _get_google_maps_route_time(origin: Location, destination: Location, target_time: Optional[time] = None,
                                target_date: Optional[date] = None) -> Optional[int]:
    """Get travel time in minutes using Google Maps Routes API."""
    try:
        traffic_config = _get_traffic_config()
        google_config = traffic_config.get('google_maps', {})

        if not google_config.get('enabled', False) or not google_config.get('use_routes_api', False):
            return None

        api_key = google_config.get('api_key', '')
        base_url = google_config.get('base_url', 'https://routes.googleapis.com/directions/v2')

        if api_key == '' or origin is None or destination is None:
            return None

        # Build Routes API request
        url = f"{base_url}:computeRoutes"
        headers = {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': api_key,
            'X-Goog-FieldMask': 'routes.duration,routes.durationInTraffic,routes.staticDuration'
        }

        request_body = {
            "origin": {
                "location": {
                    "latLng": {
                        "latitude": origin.latitude,
                        "longitude": origin.longitude
                    }
                }
            },
            "destination": {
                "location": {
                    "latLng": {
                        "latitude": destination.latitude,
                        "longitude": destination.longitude
                    }
                }
            },
            "travelMode": "DRIVE",
        }

        response = requests.post(url, headers=headers, json=request_body, timeout=10)
        response.raise_for_status()
        result = response.json()

        routes = result.get('routes', [])
        if not routes:
            return None

        # Extract travel time in minutes
        travel_time_minutes = routes[0]['durationInTraffic'] / 60
        return int(travel_time_minutes)
    except Exception as e:
        from loguru import logger
        logger.debug(f"Google Maps API error: {e}")
        return None


def _get_google_weather_data(location: Location, target_time: Optional[time] = None,
                             target_date: Optional[date] = None) -> Optional[Dict[str, Any]]:
    """Get weather data using Google Weather API."""
    try:
        traffic_config = _get_traffic_config()
        weather_config = traffic_config.get('google_weather', {})

        if not weather_config.get('enabled', False):
            return None

        api_key = weather_config.get('api_key', '')
        base_url = weather_config.get('base_url', 'https://weather.googleapis.com/v1')

        if api_key == '' or location is None:
            return None

        # Build Weather API request
        url = f"{base_url}/currentConditions"
        params = {
            'location': f"{location.latitude},{location.longitude}",
            'key': api_key
        }

        response = requests.get(url, params=params, timeout=5)
        response.raise_for_status()
        result = response.json()

        # Extract relevant weather data
        if 'data' in result and 'currentConditions' in result['data']:
            conditions = result['data']['currentConditions']
            return {
                'temperature': conditions.get('temperature'),
                'humidity': conditions.get('humidity'),
                'precipitation': conditions.get('precipitation'),
                'wind_speed': conditions.get('windSpeed'),
                'weather_code': conditions.get('weatherCode')
            }
        return None
    except Exception as e:
        from loguru import logger
        logger.debug(f"Google Weather API error: {e}")
        return None


def calculate_distance(loc1: Location, loc2: Location) -> float:
    """
    Calculate distance between two locations in miles using Haversine formula.
    
    Args:
        loc1: First location
        loc2: Second location
    
    Returns:
        float: Distance in miles
    """
    return _calculate_distance_haversine(loc1, loc2)


def calculate_travel_time(origin: Location, destination: Location, 
                         target_time: Optional[time] = None,
                         target_date: Optional[date] = None) -> int:
    """
    Calculate travel time between two locations in minutes.
    
    Args:
        origin: Origin location
        destination: Destination location
        target_time: Target time for traffic calculation
        target_date: Target date for traffic calculation
    
    Returns:
        int: Travel time in minutes
    """
    if origin is None or destination is None:
        return 0

    # Try Google Maps API first
    google_time = _get_google_maps_route_time(origin, destination, target_time, target_date)
    if google_time is not None:
        return google_time

    # Fallback to distance-based calculation
    distance_miles = calculate_distance(origin, destination)
    
    # Get speed factor based on location and time
    speed_factor = _get_speed_factor(origin, target_time, target_date)
    
    # Calculate travel time (assuming 30 mph base speed)
    base_speed_mph = 30.0
    adjusted_speed_mph = base_speed_mph * speed_factor
    
    if adjusted_speed_mph <= 0:
        return 0
    
    travel_time_minutes = (distance_miles / adjusted_speed_mph) * 60
    return int(travel_time_minutes)


def calculate_travel_time_between_appointments(appointment1: AppointmentData, appointment2: AppointmentData,
                                              target_time: Optional[time] = None,
                                              target_date: Optional[date] = None) -> int:
    """
    Calculate travel time between two appointments.
    
    Args:
        appointment1: First appointment
        appointment2: Second appointment
        target_time: Target time for traffic calculation
        target_date: Target date for traffic calculation
    
    Returns:
        int: Travel time in minutes
    """
    if appointment1.location is None or appointment2.location is None:
        return 0
    
    return calculate_travel_time(appointment1.location, appointment2.location, target_time, target_date)


def is_sufficient_travel_time(appointment1: AppointmentData, appointment2: AppointmentData,
                             min_travel_time_minutes: int = 30) -> bool:
    """
    Check if there's sufficient travel time between two appointments.
    
    Args:
        appointment1: First appointment
        appointment2: Second appointment
        min_travel_time_minutes: Minimum required travel time in minutes
    
    Returns:
        bool: True if sufficient travel time, False otherwise
    """
    travel_time = calculate_travel_time_between_appointments(appointment1, appointment2)
    return travel_time >= min_travel_time_minutes


@with_config('scheduler')
def travel_time_consideration(constraint_factory: ConstraintFactory, core_config=None, service_config=None, combined_config=None, **kwargs) -> Constraint:
    """Ensure sufficient travel time between consecutive appointments."""
    # Handle Timefold validation calls where factory is None
    if constraint_factory is None:
        return constraint_factory.for_each(TimeSlotAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (
        constraint_factory.for_each(TimeSlotAssignment)
        .join(TimeSlotAssignment,
              Joiners.equal(lambda a: a.scheduled_appointment.provider))
        .filter(lambda assignment1, assignment2: (
            assignment1.id != assignment2.id and
            assignment1.time_slot is not None and
            assignment2.time_slot is not None and
            _insufficient_travel_time(assignment1, assignment2)))
        .penalize(HardSoftScore.ONE_HARD, lambda assignment1, assignment2: 
                 _insufficient_travel_time(assignment1, assignment2))
        .as_constraint(INSUFFICIENT_TRAVEL_TIME)
    )


def _insufficient_travel_time(assignment1: TimeSlotAssignment, assignment2: TimeSlotAssignment) -> bool:
    """Check if there's insufficient travel time between two appointments."""
    # Get appointment data
    appointment1 = getattr(assignment1, 'scheduled_appointment', None)
    appointment2 = getattr(assignment2, 'scheduled_appointment', None)
    
    if appointment1 is None or appointment2 is None:
        return False
    
    # Get time slots
    time_slot1 = getattr(assignment1, 'time_slot', None)
    time_slot2 = getattr(assignment2, 'time_slot', None)
    
    if time_slot1 is None or time_slot2 is None:
        return False
    
    # Calculate travel time between appointments
    travel_time = calculate_travel_time_between_appointments(
        appointment1.appointment_data,
        appointment2.appointment_data,
        target_time=time_slot1,
        target_date=getattr(appointment1, 'assigned_date', None)
    )
    
    # Check if travel time exceeds available time between appointments
    # This is a simplified check - in a real implementation, you'd need more sophisticated logic
    return travel_time > 30  # 30 minutes threshold


def _calculate_travel_time_penalty(assignment1: TimeSlotAssignment, assignment2: TimeSlotAssignment) -> int:
    """Calculate penalty for insufficient travel time."""
    if _insufficient_travel_time(assignment1, assignment2):
        return 1
    return 0


def route_travel_time_optimization_constraints(factory: ConstraintFactory):
    """Return all route travel time optimization constraints."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return []
        
    return [
        travel_time_consideration(factory),
    ]
