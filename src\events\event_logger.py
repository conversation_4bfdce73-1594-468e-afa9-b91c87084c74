"""
File-based Event Logging System

This module provides persistent event storage using JSON files with rotation,
compression, and replay capabilities for the CareAXL Scheduling Engine.
"""

import json
import os
import gzip
import shutil
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Iterator
from pathlib import Path
from loguru import logger
import threading
from dataclasses import dataclass, asdict
from enum import Enum

from .event_bus import Event, EventType, EventPriority


@dataclass
class EventLogEntry:
    """Event log entry with metadata."""
    event: Event
    logged_at: datetime
    file_path: str
    line_number: int


class EventLogRotation:
    """Event log rotation configuration."""
    
    def __init__(self, 
                 max_file_size_mb: int = 100,
                 max_files: int = 10,
                 compress_old_files: bool = True,
                 retention_days: int = 30):
        self.max_file_size_mb = max_file_size_mb
        self.max_files = max_files
        self.compress_old_files = compress_old_files
        self.retention_days = retention_days


class FileEventLogger:
    """File-based event logger with rotation and compression."""
    
    def __init__(self, 
                 log_directory: str = "logs/events",
                 rotation: Optional[EventLogRotation] = None,
                 enable_compression: bool = True):
        self.log_directory = Path(log_directory)
        self.rotation = rotation or EventLogRotation()
        self.enable_compression = enable_compression
        self.current_file: Optional[Path] = None
        self.current_file_size = 0
        self.lock = threading.Lock()
        self.logger = logger.bind(component="FileEventLogger")
        
        # Ensure log directory exists
        self.log_directory.mkdir(parents=True, exist_ok=True)
        
        # Initialize current file
        self._initialize_current_file()
        
        self.logger.info(f"File event logger initialized at {self.log_directory}")
    
    def _initialize_current_file(self) -> None:
        """Initialize the current log file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.current_file = self.log_directory / f"events_{timestamp}.jsonl"
        self.current_file_size = 0
        
        # Create file with header
        with open(self.current_file, 'w', encoding='utf-8') as f:
            f.write("# Event Log File\n")
            f.write(f"# Created: {datetime.now().isoformat()}\n")
            f.write(f"# Format: JSON Lines (one JSON object per line)\n")
            f.write("#\n")
        
        self.logger.info(f"Initialized log file: {self.current_file}")
    
    def _should_rotate(self) -> bool:
        """Check if the current file should be rotated."""
        if not self.current_file or not self.current_file.exists():
            return True
        
        # Check file size
        current_size_mb = self.current_file.stat().st_size / (1024 * 1024)
        if current_size_mb >= self.rotation.max_file_size_mb:
            return True
        
        return False
    
    def _rotate_file(self) -> None:
        """Rotate the current log file."""
        if not self.current_file or not self.current_file.exists():
            return
        
        old_file = self.current_file
        
        # Compress old file if enabled
        if self.rotation.compress_old_files and self.enable_compression:
            compressed_file = old_file.with_suffix('.jsonl.gz')
            with open(old_file, 'rb') as f_in:
                with gzip.open(compressed_file, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            old_file.unlink()  # Remove uncompressed file
            self.logger.info(f"Compressed log file: {compressed_file}")
        else:
            self.logger.info(f"Rotated log file: {old_file}")
        
        # Clean up old files
        self._cleanup_old_files()
        
        # Initialize new file
        self._initialize_current_file()
    
    def _cleanup_old_files(self) -> None:
        """Clean up old log files based on retention policy."""
        cutoff_date = datetime.now() - timedelta(days=self.rotation.retention_days)
        
        # Get all log files (including compressed)
        log_files = list(self.log_directory.glob("events_*.jsonl*"))
        
        # Sort by modification time
        log_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
        
        # Keep only the most recent files within retention period
        files_to_keep = []
        for log_file in log_files:
            file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
            if file_time > cutoff_date:
                files_to_keep.append(log_file)
            else:
                log_file.unlink()
                self.logger.info(f"Deleted old log file: {log_file}")
        
        # Also limit by max_files
        if len(files_to_keep) > self.rotation.max_files:
            for old_file in files_to_keep[self.rotation.max_files:]:
                old_file.unlink()
                self.logger.info(f"Deleted excess log file: {old_file}")
    
    def log_event(self, event: Event) -> EventLogEntry:
        """Log an event to the current file."""
        with self.lock:
            # Check if rotation is needed
            if self._should_rotate():
                self._rotate_file()
            
            # Ensure current file exists
            if not self.current_file:
                self._initialize_current_file()
            
            # At this point, current_file should be set
            assert self.current_file is not None
            
            # Prepare log entry
            log_entry = {
                'logged_at': datetime.now().isoformat(),
                'event': event.to_dict()
            }
            
            # Write to file
            with open(self.current_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
            
            # Update file size
            self.current_file_size = self.current_file.stat().st_size
            
            # Create log entry object
            entry = EventLogEntry(
                event=event,
                logged_at=datetime.now(),
                file_path=str(self.current_file),
                line_number=self._get_line_count()
            )
            
            self.logger.debug(f"Logged event {event.id} to {self.current_file}")
            return entry
    
    def _get_line_count(self) -> int:
        """Get the current line count in the log file."""
        if not self.current_file or not self.current_file.exists():
            return 0
        
        with open(self.current_file, 'r', encoding='utf-8') as f:
            return sum(1 for _ in f)
    
    def replay_events(self, 
                     start_date: Optional[datetime] = None,
                     end_date: Optional[datetime] = None,
                     event_types: Optional[List[EventType]] = None,
                     correlation_id: Optional[str] = None) -> Iterator[Event]:
        """Replay events from log files."""
        self.logger.info("Starting event replay")
        
        # Get all log files
        log_files = list(self.log_directory.glob("events_*.jsonl*"))
        log_files.sort(key=lambda f: f.stat().st_mtime)
        
        for log_file in log_files:
            self.logger.debug(f"Processing log file: {log_file}")
            
            # Handle compressed files
            if log_file.suffix == '.gz':
                with gzip.open(log_file, 'rt', encoding='utf-8') as f:
                    yield from self._process_log_file(f, start_date, end_date, event_types, correlation_id)
            else:
                with open(log_file, 'r', encoding='utf-8') as f:
                    yield from self._process_log_file(f, start_date, end_date, event_types, correlation_id)
        
        self.logger.info("Event replay completed")
    
    def _process_log_file(self, file_handle, start_date, end_date, event_types, correlation_id):
        """Process a single log file and yield matching events."""
        for line_num, line in enumerate(file_handle, 1):
            # Skip comments and empty lines
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            try:
                # Parse log entry
                log_entry = json.loads(line)
                logged_at = datetime.fromisoformat(log_entry['logged_at'])
                event_data = log_entry['event']
                
                # Apply filters
                if start_date and logged_at < start_date:
                    continue
                if end_date and logged_at > end_date:
                    continue
                
                # Reconstruct event
                event = Event.from_dict(event_data)
                
                # Apply event filters
                if event_types and event.type not in event_types:
                    continue
                if correlation_id and event.correlation_id != correlation_id:
                    continue
                
                yield event
                
            except (json.JSONDecodeError, KeyError, ValueError) as e:
                self.logger.warning(f"Error parsing log entry at line {line_num}: {e}")
                continue
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about logged events."""
        stats = {
            'log_directory': str(self.log_directory),
            'current_file': str(self.current_file) if self.current_file else None,
            'current_file_size_mb': round(self.current_file_size / (1024 * 1024), 2) if self.current_file_size else 0,
            'total_files': 0,
            'total_events': 0,
            'events_by_type': {},
            'events_by_date': {},
            'compressed_files': 0
        }
        
        # Count files and events
        log_files = list(self.log_directory.glob("events_*.jsonl*"))
        stats['total_files'] = len(log_files)
        
        for log_file in log_files:
            if log_file.suffix == '.gz':
                stats['compressed_files'] += 1
                with gzip.open(log_file, 'rt', encoding='utf-8') as f:
                    stats = self._count_events_in_file(f, stats)
            else:
                with open(log_file, 'r', encoding='utf-8') as f:
                    stats = self._count_events_in_file(f, stats)
        
        return stats
    
    def _count_events_in_file(self, file_handle, stats):
        """Count events in a file and update statistics."""
        for line in file_handle:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            try:
                log_entry = json.loads(line)
                event_data = log_entry['event']
                event = Event.from_dict(event_data)
                
                # Count total events
                stats['total_events'] += 1
                
                # Count by event type
                event_type = event.type.value
                stats['events_by_type'][event_type] = stats['events_by_type'].get(event_type, 0) + 1
                
                # Count by date
                date_str = event.timestamp.date().isoformat()
                stats['events_by_date'][date_str] = stats['events_by_date'].get(date_str, 0) + 1
                
            except (json.JSONDecodeError, KeyError, ValueError):
                continue
        
        return stats
    
    def export_events(self, 
                     output_file: str,
                     start_date: Optional[datetime] = None,
                     end_date: Optional[datetime] = None,
                     event_types: Optional[List[EventType]] = None,
                     format: str = 'json') -> None:
        """Export events to a file in various formats."""
        self.logger.info(f"Exporting events to {output_file}")
        
        events = list(self.replay_events(start_date, end_date, event_types))
        
        if format.lower() == 'json':
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump([event.to_dict() for event in events], f, indent=2, ensure_ascii=False)
        elif format.lower() == 'csv':
            import csv
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['id', 'type', 'priority', 'timestamp', 'source', 'correlation_id', 'data'])
                for event in events:
                    writer.writerow([
                        event.id,
                        event.type.value,
                        event.priority.value,
                        event.timestamp.isoformat(),
                        event.source,
                        event.correlation_id or '',
                        json.dumps(event.data)
                    ])
        else:
            raise ValueError(f"Unsupported export format: {format}")
        
        self.logger.info(f"Exported {len(events)} events to {output_file}")


# Global event logger instance
_event_logger: Optional[FileEventLogger] = None


def initialize_event_logger(log_directory: str = "logs/events",
                          rotation: Optional[EventLogRotation] = None,
                          enable_compression: bool = True) -> None:
    """Initialize the global event logger."""
    global _event_logger
    _event_logger = FileEventLogger(log_directory, rotation, enable_compression)


def get_event_logger() -> FileEventLogger:
    """Get the global event logger instance."""
    if _event_logger is None:
        raise RuntimeError("Event logger not initialized. Call initialize_event_logger() first.")
    return _event_logger


def log_event(event: Event) -> EventLogEntry:
    """Log an event using the global event logger."""
    logger = get_event_logger()
    return logger.log_event(event)


def replay_events(start_date: Optional[datetime] = None,
                 end_date: Optional[datetime] = None,
                 event_types: Optional[List[EventType]] = None,
                 correlation_id: Optional[str] = None) -> Iterator[Event]:
    """Replay events using the global event logger."""
    logger = get_event_logger()
    return logger.replay_events(start_date, end_date, event_types, correlation_id)


def get_event_statistics() -> Dict[str, Any]:
    """Get event statistics using the global event logger."""
    logger = get_event_logger()
    return logger.get_statistics() 