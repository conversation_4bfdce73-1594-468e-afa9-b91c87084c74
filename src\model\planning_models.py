"""
Timefold-specific planning entities and solutions.

This module contains the Timefold/OptaPy decorators and annotations for the optimization solver.
The domain models in domain.py remain framework-agnostic.
"""

from dataclasses import dataclass, field
from datetime import date, time
from typing import List, Optional, Annotated

from timefold.solver.domain import (
    PlanningId, PlanningVariable, PlanningEntityCollectionProperty,
    ProblemFactCollectionProperty, ValueRangeProvider, PlanningScore,
    planning_entity, planning_solution
)
from timefold.solver.score import HardSoftScore

from .domain import (
    AppointmentData, Provider
)


# Timefold-specific planning entities
@planning_entity
@dataclass
class AppointmentAssignment:
    """Planning entity representing the assignment of an appointment to a provider and date."""
    id: Annotated[str, PlanningId]
    appointment_data: AppointmentData
    provider: Annotated[Optional[Provider], PlanningVariable] = field(default=None)
    assigned_date: Annotated[Optional[date], PlanningVariable] = field(default=None)

    def __str__(self):
        if self.provider is None or self.assigned_date is None:
            return f"{self.appointment_data.id} -> unassigned"
        return f"{self.appointment_data.id} -> {self.provider.name} on {self.assigned_date}"


@planning_entity
@dataclass
class TimeSlotAssignment:
    """Planning entity for assigning time slots to scheduled appointments."""
    id: Annotated[str, PlanningId]
    scheduled_appointment: 'ScheduledAppointment'  # Use planning version
    time_slot: Annotated[Optional[time], PlanningVariable] = field(default=None)

    def __str__(self):
        if self.time_slot is None:
            return f"{self.scheduled_appointment} -> no time assigned"
        return f"{self.scheduled_appointment.appointment_data.id} -> {self.scheduled_appointment.provider.name} at {self.time_slot}"


@dataclass
class ScheduledAppointment:
    """Planning version of scheduled appointment for day planning."""
    id: str
    appointment_data: AppointmentData
    provider: Provider
    assigned_date: date
    assigned_time: Optional[time] = None  # Will be assigned by DayPlan job

    def __str__(self):
        time_str = f" at {self.assigned_time}" if self.assigned_time else " (time TBD)"
        return f"{self.appointment_data.id} -> {self.provider.name} on {self.assigned_date}{time_str}"


@dataclass
class DateRange:
    """Represents a range of dates for the rolling window."""
    start_date: date
    end_date: date
    
    def __post_init__(self):
        if self.start_date > self.end_date:
            raise ValueError("Start date must be before or equal to end date")
    
    def contains(self, date_to_check: date) -> bool:
        """Check if a date is within this range (inclusive)."""
        return self.start_date <= date_to_check <= self.end_date
    
    def get_all_dates(self) -> List[date]:
        """Get all dates in the range (inclusive)."""
        from datetime import timedelta
        dates = []
        current = self.start_date
        while current <= self.end_date:
            dates.append(current)
            current += timedelta(days=1)
        return dates
    
    def __len__(self) -> int:
        """Return the number of days in the range (inclusive)."""
        from datetime import timedelta
        return (self.end_date - self.start_date).days + 1
    
    def __iter__(self):
        """Make DateRange iterable to work with Timefold's ValueRangeProvider."""
        return iter(self.get_all_dates())
    
    def __getitem__(self, index):
        """Support indexing for Timefold compatibility."""
        return self.get_all_dates()[index]


# Timefold-specific planning solutions
@planning_solution
@dataclass
class AppointmentSchedule:
    """Solution representing the complete appointment schedule."""
    id: str
    providers: Annotated[List[Provider],
    ProblemFactCollectionProperty,
    ValueRangeProvider]
    available_dates: Annotated[List[date],
    ProblemFactCollectionProperty,
    ValueRangeProvider]
    appointment_assignments: Annotated[List[AppointmentAssignment],
    PlanningEntityCollectionProperty]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)

    def get_provider_assignments(self, provider: Provider) -> List[AppointmentAssignment]:
        """Get all appointments assigned to a specific provider."""
        return [assignment for assignment in self.appointment_assignments
                if assignment.provider == provider]

    def get_provider_daily_workload(self, provider: Provider, target_date: date) -> int:
        """Calculate provider's workload for a specific date."""
        assignments = self.get_provider_assignments(provider)
        return len([a for a in assignments if a.assigned_date == target_date])


@planning_solution
@dataclass
class DaySchedule:
    """Solution representing the daily time slot assignments."""
    id: str
    date: date
    time_slots: Annotated[List[time],
    ProblemFactCollectionProperty,
    ValueRangeProvider]
    scheduled_appointments: Annotated[List[ScheduledAppointment],
    ProblemFactCollectionProperty]
    time_assignments: Annotated[List[TimeSlotAssignment],
    PlanningEntityCollectionProperty]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)
