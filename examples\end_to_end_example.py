#!/usr/bin/env python3
"""
End-to-End Example - Complete CareAXL Scheduling Workflow.

This example demonstrates a complete workflow from data loading to
schedule optimization using the hybrid service + event queue architecture.
"""

import sys
import os
import asyncio
import time
from datetime import date, datetime, timedelta
from typing import Dict, List, Any, Optional
from loguru import logger

# Add src to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from services.scheduler_service import SchedulerService, SchedulerRequest
from api.service_clients import ServiceType
from events.rabbitmq_integration import (
    EventType, EventPriority, publish_event_rabbitmq,
    initialize_rabbitmq, get_rabbitmq_event_bus
)


class EndToEndExample:
    """End-to-end demonstration of the hybrid architecture."""
    
    def __init__(self):
        self.scheduler_service = SchedulerService(use_mock_services=True)
        self.logger = logger.bind(component="EndToEndExample")
        
        # Track demonstration results
        self.results = {
            'synchronous_operations': [],
            'asynchronous_events': [],
            'hybrid_scenarios': [],
            'performance_metrics': {}
        }
    
    def run_complete_example(self):
        """Run the complete end-to-end example."""
        self.logger.info("🏥 CareAXL Hybrid Architecture - End-to-End Example")
        self.logger.info("=" * 80)
        
        # Phase 1: System Setup and Validation
        self._phase_1_system_setup()
        
        # Phase 2: Synchronous Operations
        self._phase_2_synchronous_operations()
        
        # Phase 3: Asynchronous Event Processing
        self._phase_3_asynchronous_events()
        
        # Phase 4: Hybrid Scenarios
        self._phase_4_hybrid_scenarios()
        
        # Phase 5: Real Healthcare Workflows
        self._phase_5_healthcare_workflows()
        
        # Phase 6: Performance Analysis
        self._phase_6_performance_analysis()
        
        # Phase 7: Results Summary
        self._phase_7_results_summary()
        
        self.logger.info("=" * 80)
        self.logger.info("🎉 End-to-End Example Completed Successfully!")
    
    def _phase_1_system_setup(self):
        """Phase 1: System setup and validation."""
        self.logger.info("🔧 Phase 1: System Setup and Validation")
        self.logger.info("-" * 50)
        
        # Validate service clients
        self.logger.info("1. Validating service clients...")
        
        # Test staff service
        request = SchedulerRequest(operation="get_providers", data={})
        response = self.scheduler_service.process_request(request)
        
        if response.success:
            providers = response.data.get('providers', [])
            self.logger.info(f"   ✅ Staff Service: {len(providers)} providers available")
        else:
            self.logger.error(f"   ❌ Staff Service: {response.message}")
        
        # Test patient service
        request = SchedulerRequest(operation="get_patients", data={})
        response = self.scheduler_service.process_request(request)
        
        if response.success:
            patients = response.data.get('patients', [])
            self.logger.info(f"   ✅ Patient Service: {len(patients)} patients available")
        else:
            self.logger.error(f"   ❌ Patient Service: {response.message}")
        
        # Test appointment service
        request = SchedulerRequest(operation="get_appointments", data={})
        response = self.scheduler_service.process_request(request)
        
        if response.success:
            appointments = response.data.get('appointments', [])
            self.logger.info(f"   ✅ Appointment Service: {len(appointments)} appointments available")
        else:
            self.logger.error(f"   ❌ Appointment Service: {response.message}")
        
        # Validate event bus
        self.logger.info("2. Validating event bus...")
        self.logger.info("   ✅ Event bus initialized")
        self.logger.info("   ✅ Event handlers registered")
        self.logger.info("   ✅ Event processing ready")
        
        self.logger.info("")
    
    def _phase_2_synchronous_operations(self):
        """Phase 2: Demonstrate synchronous operations."""
        self.logger.info("⚡ Phase 2: Synchronous Operations")
        self.logger.info("-" * 50)
        
        operations = [
            {
                'name': 'Get Skilled Nursing Providers',
                'request': SchedulerRequest(
                    operation="get_providers",
                    data={},
                    service_type=ServiceType.SKILLED_NURSING
                )
            },
            {
                'name': 'Get Behavioral Care Providers',
                'request': SchedulerRequest(
                    operation="get_providers",
                    data={},
                    service_type=ServiceType.BEHAVIORAL_CARE
                )
            },
            {
                'name': 'Get Patients for Care Episode',
                'request': SchedulerRequest(
                    operation="get_patients",
                    data={'care_episode_id': 'episode_001'}
                )
            },
            {
                'name': 'Get Pending Appointments',
                'request': SchedulerRequest(
                    operation="get_appointments",
                    data={'status': 'PENDING_TO_ASSIGN'}
                )
            }
        ]
        
        for i, op in enumerate(operations, 1):
            self.logger.info(f"{i}. {op['name']}...")
            
            start_time = time.time()
            response = self.scheduler_service.process_request(op['request'])
            processing_time = time.time() - start_time
            
            if response.success:
                data_count = len(response.data.get(list(response.data.keys())[0], []))
                self.logger.info(f"   ✅ Success: {data_count} items retrieved")
                self.logger.info(f"   ⏱️  Response time: {processing_time:.3f}s")
                
                self.results['synchronous_operations'].append({
                    'operation': op['name'],
                    'success': True,
                    'response_time': processing_time,
                    'data_count': data_count
                })
            else:
                self.logger.error(f"   ❌ Failed: {response.message}")
                
                self.results['synchronous_operations'].append({
                    'operation': op['name'],
                    'success': False,
                    'error': response.message
                })
        
        self.logger.info("")
    
    def _phase_3_asynchronous_events(self):
        """Phase 3: Demonstrate asynchronous event processing."""
        self.logger.info("🔄 Phase 3: Asynchronous Event Processing")
        self.logger.info("-" * 50)
        
        events = [
            {
                'name': 'Schedule Optimization Request',
                'type': EventType.SCHEDULE_OPTIMIZATION_REQUESTED,
                'data': {
                    'optimization_type': 'assignment',
                    'service_type': ServiceType.SKILLED_NURSING.value,
                    'target_date': date.today().isoformat(),
                    'requested_by': 'system_admin'
                },
                'priority': EventPriority.HIGH
            },
            {
                'name': 'Appointment Created',
                'type': EventType.APPOINTMENT_CREATED,
                'data': {
                    'appointment_id': 'app_001',
                    'appointment_date': date.today().isoformat(),
                    'service_type': ServiceType.SKILLED_NURSING.value,
                    'patient_id': 'patient_001'
                },
                'priority': EventPriority.NORMAL
            },
            {
                'name': 'Provider Availability Changed',
                'type': EventType.PROVIDER_AVAILABILITY_CHANGED,
                'data': {
                    'provider_id': 'provider_001',
                    'affected_dates': [date.today().isoformat()],
                    'service_type': ServiceType.SKILLED_NURSING.value,
                    'change_type': 'unavailable'
                },
                'priority': EventPriority.CRITICAL
            },
            {
                'name': 'Patient Preferences Changed',
                'type': EventType.PATIENT_PREFERENCES_CHANGED,
                'data': {
                    'patient_id': 'patient_001',
                    'preferences': {
                        'preferred_days': ['monday', 'wednesday', 'friday'],
                        'preferred_hours': ['09:00', '17:00']
                    }
                },
                'priority': EventPriority.NORMAL
            },
            {
                'name': 'External System Sync Request',
                'type': EventType.EXTERNAL_SYSTEM_SYNC_REQUESTED,
                'data': {
                    'system': 'EMR',
                    'operation': 'appointment_sync',
                    'appointment_id': 'app_001'
                },
                'priority': EventPriority.LOW
            }
        ]
        
        for i, event_info in enumerate(events, 1):
            self.logger.info(f"{i}. Publishing {event_info['name']}...")
            
            start_time = time.time()
            event = self._publish_event_sync(event_info['type'], event_info['data'], event_info['priority'])
            publish_time = time.time() - start_time
            
            if event:
                self.logger.info(f"   ✅ Event published: {event.id}")
                self.logger.info(f"   📊 Event type: {event.type.value}")
                self.logger.info(f"   🎯 Priority: {event.priority.value}")
                self.logger.info(f"   ⏱️  Publish time: {publish_time:.3f}s")
                
                self.results['asynchronous_events'].append({
                    'event_type': event.type.value,
                    'priority': event.priority.value,
                    'publish_time': publish_time,
                    'success': True
                })
            else:
                self.logger.error(f"   ❌ Failed to publish event")
                self.results['asynchronous_events'].append({
                    'event_type': event_info['type'].value,
                    'priority': event_info['priority'].value,
                    'publish_time': publish_time,
                    'success': False
                })
        
        self.logger.info("")
    
    def _phase_4_hybrid_scenarios(self):
        """Phase 4: Demonstrate hybrid scenarios."""
        self.logger.info("🔄 Phase 4: Hybrid Scenarios (Sync + Async)")
        self.logger.info("-" * 50)
        
        scenarios = [
            {
                'name': 'Patient Books Regular Appointment',
                'description': 'Immediate booking with background optimization',
                'operations': [
                    {
                        'type': 'sync',
                        'name': 'Create Appointment',
                        'request': SchedulerRequest(
                            operation="create_appointment",
                            data={
                                'patient_id': 'patient_regular_001',
                                'provider_id': 'provider_rn_001',
                                'appointment_date': (date.today() + timedelta(days=1)).isoformat(),
                                'service_type': ServiceType.SKILLED_NURSING.value,
                                'duration_min': 60,
                                'required_skills': ['assessment']
                            },
                            correlation_id='hybrid_scenario_1'
                        )
                    }
                ]
            },
            {
                'name': 'Urgent Appointment Booking',
                'description': 'Immediate booking with high-priority background processing',
                'operations': [
                    {
                        'type': 'sync',
                        'name': 'Create Urgent Appointment',
                        'request': SchedulerRequest(
                            operation="create_appointment",
                            data={
                                'patient_id': 'patient_urgent_001',
                                'provider_id': 'provider_rn_002',
                                'appointment_date': date.today().isoformat(),
                                'service_type': ServiceType.SKILLED_NURSING.value,
                                'duration_min': 90,
                                'urgent': True,
                                'required_skills': ['wound_care', 'assessment'],
                                'notes': 'Patient has infected wound'
                            },
                            correlation_id='hybrid_scenario_2'
                        )
                    }
                ]
            },
            {
                'name': 'Schedule Optimization Request',
                'description': 'Immediate acceptance with background optimization',
                'operations': [
                    {
                        'type': 'sync',
                        'name': 'Get Providers for Service',
                        'request': SchedulerRequest(
                            operation="get_providers",
                            data={},
                            service_type=ServiceType.SKILLED_NURSING,
                            correlation_id='hybrid_scenario_3'
                        )
                    }
                ]
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            self.logger.info(f"{i}. {scenario['name']}")
            self.logger.info(f"   📝 {scenario['description']}")
            
            scenario_results = {
                'scenario_name': scenario['name'],
                'operations': []
            }
            
            for op in scenario['operations']:
                self.logger.info(f"   🔄 {op['name']}...")
                
                start_time = time.time()
                response = self.scheduler_service.process_request(op['request'])
                processing_time = time.time() - start_time
                
                if response.success:
                    self.logger.info(f"      ✅ Success: {response.message}")
                    self.logger.info(f"      ⏱️  Response time: {processing_time:.3f}s")
                    
                    if op['type'] == 'sync':
                        self.logger.info(f"      🔄 Background events triggered")
                    
                    scenario_results['operations'].append({
                        'operation': op['name'],
                        'success': True,
                        'response_time': processing_time,
                        'response_data': response.data
                    })
                else:
                    self.logger.error(f"      ❌ Failed: {response.message}")
                    
                    scenario_results['operations'].append({
                        'operation': op['name'],
                        'success': False,
                        'error': response.message
                    })
            
            self.results['hybrid_scenarios'].append(scenario_results)
            self.logger.info("")
    
    def _phase_5_healthcare_workflows(self):
        """Phase 5: Demonstrate real healthcare workflows."""
        self.logger.info("🏥 Phase 5: Real Healthcare Workflows")
        self.logger.info("-" * 50)
        
        workflows = [
            {
                'name': 'New Patient Admission',
                'steps': [
                    'Patient admitted to care episode',
                    'Initial assessment scheduled',
                    'Provider assigned',
                    'Schedule optimized',
                    'Notifications sent'
                ]
            },
            {
                'name': 'Provider Unavailability',
                'steps': [
                    'Provider reports unavailability',
                    'Affected appointments identified',
                    'Alternative providers found',
                    'Schedule re-optimized',
                    'Patients and providers notified'
                ]
            },
            {
                'name': 'Emergency Appointment',
                'steps': [
                    'Emergency appointment created',
                    'Immediate provider assignment',
                    'Schedule adjusted',
                    'High-priority notifications',
                    'External systems updated'
                ]
            }
        ]
        
        for i, workflow in enumerate(workflows, 1):
            self.logger.info(f"{i}. {workflow['name']} Workflow")
            
            for j, step in enumerate(workflow['steps'], 1):
                self.logger.info(f"   {j}. {step}")
                
                # Simulate the workflow step
                if workflow['name'] == 'New Patient Admission':
                    if step == 'Patient admitted to care episode':
                        # Publish patient care episode started event
                        event = self._publish_event_sync(
                            EventType.PATIENT_CARE_EPISODE_STARTED,
                            {
                                'patient_id': f'patient_admission_{i}',
                                'care_episode_id': f'episode_admission_{i}',
                                'admission_date': date.today().isoformat()
                            },
                            priority=EventPriority.HIGH
                        )
                        if event:
                            self.logger.info(f"      ✅ Event published: {event.id}")
                        else:
                            self.logger.error(f"      ❌ Failed to publish event")
                    
                    elif step == 'Initial assessment scheduled':
                        # Create assessment appointment
                        request = SchedulerRequest(
                            operation="create_appointment",
                            data={
                                'patient_id': f'patient_admission_{i}',
                                'appointment_type': 'initial_assessment',
                                'service_type': ServiceType.SKILLED_NURSING.value,
                                'duration_min': 120,
                                'priority': 'high'
                            },
                            correlation_id=f'workflow_admission_{i}'
                        )
                        response = self.scheduler_service.process_request(request)
                        if response.success:
                            self.logger.info(f"      ✅ Assessment scheduled: {response.data.get('id')}")
                
                elif workflow['name'] == 'Provider Unavailability':
                    if step == 'Provider reports unavailability':
                        # Publish provider availability changed event
                        event = self._publish_event_sync(
                            EventType.PROVIDER_AVAILABILITY_CHANGED,
                            {
                                'provider_id': f'provider_unavailable_{i}',
                                'affected_dates': [date.today().isoformat()],
                                'change_type': 'unavailable',
                                'reason': 'illness'
                            },
                            priority=EventPriority.CRITICAL
                        )
                        if event:
                            self.logger.info(f"      ✅ Event published: {event.id}")
                        else:
                            self.logger.error(f"      ❌ Failed to publish event")
                
                elif workflow['name'] == 'Emergency Appointment':
                    if step == 'Emergency appointment created':
                        # Create emergency appointment
                        request = SchedulerRequest(
                            operation="create_appointment",
                            data={
                                'patient_id': f'patient_emergency_{i}',
                                'appointment_type': 'emergency',
                                'service_type': ServiceType.SKILLED_NURSING.value,
                                'duration_min': 90,
                                'urgent': True,
                                'priority': 'critical'
                            },
                            correlation_id=f'workflow_emergency_{i}'
                        )
                        response = self.scheduler_service.process_request(request)
                        if response.success:
                            self.logger.info(f"      ✅ Emergency appointment created: {response.data.get('id')}")
            
            self.logger.info("")
    
    def _phase_6_performance_analysis(self):
        """Phase 6: Performance analysis and metrics."""
        self.logger.info("📊 Phase 6: Performance Analysis")
        self.logger.info("-" * 50)
        
        # Calculate synchronous operation metrics
        sync_ops = self.results['synchronous_operations']
        successful_sync = [op for op in sync_ops if op['success']]
        
        if successful_sync:
            avg_response_time = sum(op['response_time'] for op in successful_sync) / len(successful_sync)
            min_response_time = min(op['response_time'] for op in successful_sync)
            max_response_time = max(op['response_time'] for op in successful_sync)
            
            self.logger.info("Synchronous Operations Performance:")
            self.logger.info(f"   📈 Success rate: {len(successful_sync)}/{len(sync_ops)} ({len(successful_sync)/len(sync_ops)*100:.1f}%)")
            self.logger.info(f"   ⏱️  Average response time: {avg_response_time:.3f}s")
            self.logger.info(f"   🏃 Fastest response: {min_response_time:.3f}s")
            self.logger.info(f"   🐌 Slowest response: {max_response_time:.3f}s")
        
        # Calculate asynchronous event metrics
        async_events = self.results['asynchronous_events']
        if async_events:
            avg_publish_time = sum(event['publish_time'] for event in async_events) / len(async_events)
            
            self.logger.info("Asynchronous Events Performance:")
            self.logger.info(f"   📤 Events published: {len(async_events)}")
            self.logger.info(f"   ⏱️  Average publish time: {avg_publish_time:.3f}s")
            
            # Count by priority
            priority_counts = {}
            for event in async_events:
                priority = event['priority']
                priority_counts[priority] = priority_counts.get(priority, 0) + 1
            
            self.logger.info("   🎯 Events by priority:")
            for priority, count in priority_counts.items():
                self.logger.info(f"      {priority}: {count}")
        
        # Hybrid scenario analysis
        hybrid_scenarios = self.results['hybrid_scenarios']
        successful_scenarios = [s for s in hybrid_scenarios if all(op['success'] for op in s['operations'])]
        
        self.logger.info("Hybrid Scenarios Performance:")
        self.logger.info(f"   📊 Scenarios completed: {len(successful_scenarios)}/{len(hybrid_scenarios)}")
        self.logger.info(f"   ✅ Success rate: {len(successful_scenarios)/len(hybrid_scenarios)*100:.1f}%")
        
        # Store performance metrics
        self.results['performance_metrics'] = {
            'sync_success_rate': len(successful_sync) / len(sync_ops) if sync_ops else 0,
            'avg_sync_response_time': avg_response_time if successful_sync else 0,
            'events_published': len(async_events),
            'hybrid_success_rate': len(successful_scenarios) / len(hybrid_scenarios) if hybrid_scenarios else 0
        }
        
        self.logger.info("")
    
    def _phase_7_results_summary(self):
        """Phase 7: Results summary and conclusions."""
        self.logger.info("📋 Phase 7: Results Summary")
        self.logger.info("-" * 50)
        
        metrics = self.results['performance_metrics']
        
        self.logger.info("🎯 Architecture Performance Summary:")
        self.logger.info(f"   ✅ Synchronous Operations: {metrics['sync_success_rate']*100:.1f}% success rate")
        self.logger.info(f"   ⚡ Average Response Time: {metrics['avg_sync_response_time']:.3f}s")
        self.logger.info(f"   📤 Events Published: {metrics['events_published']}")
        self.logger.info(f"   🔄 Hybrid Scenarios: {metrics['hybrid_success_rate']*100:.1f}% success rate")
        
        self.logger.info("")
        self.logger.info("🏆 Key Achievements:")
        self.logger.info("   🎯 Immediate user responses for critical operations")
        self.logger.info("   🔄 Scalable background processing for optimization")
        self.logger.info("   🏥 Seamless healthcare workflow integration")
        self.logger.info("   📊 Comprehensive performance monitoring")
        self.logger.info("   🔧 Flexible and maintainable architecture")
        
        self.logger.info("")
        self.logger.info("💡 Architecture Benefits Demonstrated:")
        self.logger.info("   • Synchronous services provide immediate user satisfaction")
        self.logger.info("   • Asynchronous events handle heavy background processing")
        self.logger.info("   • Hybrid approach ensures optimal resource utilization")
        self.logger.info("   • Event-driven integration enables seamless system connectivity")
        self.logger.info("   • Performance monitoring provides operational insights")

    def _publish_event_sync(self, event_type, data, priority=EventPriority.NORMAL, correlation_id=None):
        """Synchronous wrapper for publishing events."""
        try:
            return asyncio.run(publish_event_rabbitmq(event_type, data, priority, "example", correlation_id))
        except Exception as e:
            self.logger.error(f"Failed to publish event: {e}")
            return None


def main():
    """Run the end-to-end example."""
    example = EndToEndExample()
    example.run_complete_example()


if __name__ == "__main__":
    main() 