"""
Service Clients for CareAXL Microservices Integration.

This module provides clients for interacting with external CareAXL services
for synchronous operations like data fetching, validation, and immediate responses.
"""

import requests
import json
import urllib.parse
from typing import Dict, List, Optional, Any, Union
from datetime import date, datetime
from loguru import logger
from dataclasses import dataclass
from enum import Enum
import uuid


class ServiceType(Enum):
    """Enumeration of available service types."""
    SKILLED_NURSING = "skilled_nursing"
    BEHAVIORAL_CARE = "behavioral_care"
    HOSPITAL_AT_HOME = "hospital_at_home"
    PCS = "pcs"


@dataclass
class ServiceConfig:
    """Configuration for a service client."""
    base_url: str
    api_key: str
    timeout: int = 30
    retry_attempts: int = 3


class ServiceClientBase:
    """Base class for service clients with common functionality."""
    
    def __init__(self, config: ServiceConfig):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {config.api_key}',
            'Content-Type': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Union[Dict, List[Dict]]:
        """Make HTTP request with retry logic."""
        url = f"{self.config.base_url}{endpoint}"
        
        for attempt in range(self.config.retry_attempts):
            try:
                response = self.session.request(
                    method=method,
                    url=url,
                    json=data,
                    timeout=self.config.timeout
                )
                response.raise_for_status()
                return response.json()
            except requests.exceptions.RequestException as e:
                logger.warning(f"Request failed (attempt {attempt + 1}): {e}")
                if attempt == self.config.retry_attempts - 1:
                    raise
                continue
        return {}  # Fallback return


class StaffServiceClient(ServiceClientBase):
    """Client for Staff Service operations."""
    
    def get_providers(self, service_type: Optional[ServiceType] = None, 
                     available_date: Optional[date] = None) -> List[Dict]:
        """Get providers with optional filtering."""
        params = {}
        if service_type:
            params['service_type'] = service_type.value
        if available_date:
            params['available_date'] = available_date.isoformat()
        
        endpoint = f"/api/providers?{urllib.parse.urlencode(params)}"
        result = self._make_request('GET', endpoint)
        return result if isinstance(result, list) else []
    
    def get_provider(self, provider_id: str) -> Dict:
        """Get specific provider details."""
        result = self._make_request('GET', f"/api/providers/{provider_id}")
        return result if isinstance(result, dict) else {}
    
    def update_provider_availability(self, provider_id: str, availability: Dict) -> Dict:
        """Update provider availability."""
        result = self._make_request('PUT', f"/api/providers/{provider_id}/availability", availability)
        return result if isinstance(result, dict) else {}
    
    def get_provider_schedule(self, provider_id: str, target_date: date) -> Dict:
        """Get provider's schedule for a specific date."""
        params = {'date': target_date.isoformat()}
        endpoint = f"/api/providers/{provider_id}/schedule?{urllib.parse.urlencode(params)}"
        result = self._make_request('GET', endpoint)
        return result if isinstance(result, dict) else {}


class PatientServiceClient(ServiceClientBase):
    """Client for Patient Service operations."""
    
    def get_patients(self, care_episode_id: Optional[str] = None) -> List[Dict]:
        """Get patients with optional filtering."""
        params = {}
        if care_episode_id:
            params['care_episode_id'] = care_episode_id
        
        endpoint = f"/api/patients?{urllib.parse.urlencode(params)}"
        result = self._make_request('GET', endpoint)
        return result if isinstance(result, list) else []
    
    def get_patient(self, patient_id: str) -> Dict:
        """Get specific patient details."""
        result = self._make_request('GET', f"/api/patients/{patient_id}")
        return result if isinstance(result, dict) else {}
    
    def get_patient_preferences(self, patient_id: str) -> Dict:
        """Get patient preferences."""
        result = self._make_request('GET', f"/api/patients/{patient_id}/preferences")
        return result if isinstance(result, dict) else {}
    
    def update_patient_preferences(self, patient_id: str, preferences: Dict) -> Dict:
        """Update patient preferences."""
        result = self._make_request('PUT', f"/api/patients/{patient_id}/preferences", preferences)
        return result if isinstance(result, dict) else {}


class AppointmentServiceClient(ServiceClientBase):
    """Client for Appointment Service operations."""
    
    def get_appointments(self, service_type: Optional[ServiceType] = None,
                        status: Optional[str] = None,
                        target_date: Optional[date] = None) -> List[Dict]:
        """Get appointments with optional filtering."""
        params = {}
        if service_type:
            params['service_type'] = service_type.value
        if status:
            params['status'] = status
        if target_date:
            params['target_date'] = target_date.isoformat()
        
        endpoint = f"/api/appointments?{urllib.parse.urlencode(params)}"
        result = self._make_request('GET', endpoint)
        return result if isinstance(result, list) else []
    
    def get_appointment(self, appointment_id: str) -> Dict:
        """Get specific appointment details."""
        result = self._make_request('GET', f"/api/appointments/{appointment_id}")
        return result if isinstance(result, dict) else {}
    
    def create_appointment(self, appointment_data: Dict) -> Dict:
        """Create a new appointment."""
        result = self._make_request('POST', "/api/appointments", appointment_data)
        return result if isinstance(result, dict) else {}
    
    def update_appointment(self, appointment_id: str, appointment_data: Dict) -> Dict:
        """Update an existing appointment."""
        result = self._make_request('PUT', f"/api/appointments/{appointment_id}", appointment_data)
        return result if isinstance(result, dict) else {}
    
    def cancel_appointment(self, appointment_id: str, reason: str) -> Dict:
        """Cancel an appointment."""
        result = self._make_request('DELETE', f"/api/appointments/{appointment_id}", {'reason': reason})
        return result if isinstance(result, dict) else {}


class ServiceClientFactory:
    """Factory for creating service clients with proper configuration."""
    
    def __init__(self, config: Dict[str, ServiceConfig]):
        self.config = config
    
    def get_staff_client(self) -> StaffServiceClient:
        """Get staff service client."""
        return StaffServiceClient(self.config['staff_service'])
    
    def get_patient_client(self) -> PatientServiceClient:
        """Get patient service client."""
        return PatientServiceClient(self.config['patient_service'])
    
    def get_appointment_client(self) -> AppointmentServiceClient:
        """Get appointment service client."""
        return AppointmentServiceClient(self.config['appointment_service'])


# Mock service clients for development/testing
class MockServiceClientBase:
    """Mock base class for testing without real services."""
    
    def __init__(self, mock_data: Dict[str, Any]):
        self.mock_data = mock_data
        logger.info(f"Initialized mock service client with {len(mock_data)} data items")


class MockStaffServiceClient(MockServiceClientBase):
    """Mock staff service client."""
    
    def get_providers(self, service_type: Optional[ServiceType] = None, 
                     available_date: Optional[date] = None) -> List[Dict]:
        """Mock provider data."""
        providers = self.mock_data.get('providers', [])
        if service_type:
            providers = [p for p in providers if service_type.value in p.get('service_types', [])]
        return providers
    
    def get_provider(self, provider_id: str) -> Dict:
        """Mock provider details."""
        providers = self.mock_data.get('providers', [])
        return next((p for p in providers if p['id'] == provider_id), {})


class MockPatientServiceClient(MockServiceClientBase):
    """Mock patient service client."""
    
    def get_patients(self, care_episode_id: Optional[str] = None) -> List[Dict]:
        """Mock patient data."""
        patients = self.mock_data.get('patients', [])
        if care_episode_id:
            patients = [p for p in patients if p.get('care_episode_id') == care_episode_id]
        return patients


class MockAppointmentServiceClient(MockServiceClientBase):
    """Mock appointment service client."""
    
    def get_appointments(self, service_type: Optional[ServiceType] = None,
                        status: Optional[str] = None,
                        target_date: Optional[date] = None) -> List[Dict]:
        """Mock appointment data."""
        appointments = self.mock_data.get('appointments', [])
        if service_type:
            appointments = [a for a in appointments if a.get('service_type') == service_type.value]
        if status:
            appointments = [a for a in appointments if a.get('status') == status]
        return appointments
    
    def create_appointment(self, appointment_data: Dict) -> Dict:
        """Mock appointment creation."""
        # Simulate creating an appointment
        appointment_id = str(uuid.uuid4())
        created_appointment = {
            'id': appointment_id,
            'status': 'PENDING_TO_ASSIGN',
            **appointment_data
        }
        logger.info(f"Mock appointment created: {appointment_id}")
        return created_appointment
    
    def update_appointment(self, appointment_id: str, appointment_data: Dict) -> Dict:
        """Mock appointment update."""
        # Simulate updating an appointment
        updated_appointment = {
            'id': appointment_id,
            'status': 'UPDATED',
            **appointment_data
        }
        logger.info(f"Mock appointment updated: {appointment_id}")
        return updated_appointment
    
    def cancel_appointment(self, appointment_id: str, reason: str) -> Dict:
        """Mock appointment cancellation."""
        # Simulate cancelling an appointment
        cancelled_appointment = {
            'id': appointment_id,
            'status': 'CANCELLED',
            'cancellation_reason': reason
        }
        logger.info(f"Mock appointment cancelled: {appointment_id}")
        return cancelled_appointment 