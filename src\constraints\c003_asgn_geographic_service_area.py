"""
Geographic Service Area Constraints for Assignment (C003)

This module provides geographic service area constraints for the assignment stage.
Using simplified constraint patterns to avoid Timefold API issues.
"""

from timefold.solver.score import ConstraintFactory, HardSoftScore, Constraint
from loguru import logger
import math

from constraints.base_constraints import with_config
from model.domain import Provider, Location
from model.planning_models import AppointmentAssignment

# Constraint identifiers
GEOGRAPHIC_SERVICE_AREA = "geographic_service_area"
GEOGRAPHIC_PENALTY = "geographic_penalty"
BLACKOUT_ZONE_VIOLATION = "blackout_zone_violation"

# --- Public Travel Utility Functions (moved from utils) ---
def calculate_distance(loc1: Location, loc2: Location) -> float:
    """
    Calculate distance between two locations in miles using Haversine formula.
    
    Args:
        loc1: First location
        loc2: Second location
    
    Returns:
        float: Distance in miles
    """
    if loc1 is None or loc2 is None:
        return 0.0

    # Convert decimal degrees to radians
    lat1, lon1 = math.radians(loc1.latitude), math.radians(loc1.longitude)
    lat2, lon2 = math.radians(loc2.latitude), math.radians(loc2.longitude)

    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat / 2) ** 2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
    c = 2 * math.asin(math.sqrt(a))

    # Radius of Earth in miles
    r = 3956
    return c * r


def is_within_service_radius(provider_location: Location, appointment_location: Location,
                            max_radius_miles: float = 25.0) -> bool:
    """
    Check if appointment location is within service radius of provider location.
    
    Args:
        provider_location: Provider's home location
        appointment_location: Appointment location
        max_radius_miles: Maximum service radius in miles
    
    Returns:
        bool: True if within service radius, False otherwise
    """
    if provider_location is None or appointment_location is None:
        return False
    
    distance = calculate_distance(provider_location, appointment_location)
    return distance <= max_radius_miles


def is_within_service_radius_enhanced(provider: Provider, appointment_location: Location) -> bool:
    """
    Enhanced service radius check that uses provider-specific radius if available.
    
    Args:
        provider: Provider object with home location
        appointment_location: Location of the appointment
    
    Returns:
        bool: True if appointment is within provider's service radius
    """
    if provider.home_location is None or appointment_location is None:
        return False
    
    # Use default radius for now - can be enhanced to use provider-specific radius
    default_radius = 25.0  # miles
    
    return is_within_service_radius(provider.home_location, appointment_location, default_radius)

def geographic_service_area_constraints(factory: ConstraintFactory):
    """Return all geographic service area constraints."""
    if factory is None:
        return []
    
    return [
        geographic_service_area_validation(factory),
        blackout_zone_validation(factory)
    ]

# --- Hard constraints ---
@with_config('behavioral_care', return_type='constraint')
def geographic_service_area(factory: ConstraintFactory, core_config=None, service_config=None, combined_config=None, **kwargs) -> Constraint:
    """Providers must be within their geofence service area for appointments."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (
        factory.for_each(AppointmentAssignment)
        .filter(lambda assignment: (
            assignment.provider is not None and
            assignment.appointment_data.location is not None and
            assignment.provider.home_location is not None and
            not is_within_service_radius_enhanced(assignment.provider, assignment.appointment_data.location)
        ))
        .penalize(HardSoftScore.ONE_HARD, lambda assignment: 1)
        .as_constraint(GEOGRAPHIC_SERVICE_AREA)
    )


# --- Soft constraints ---
@with_config('behavioral_care', return_type='constraint')
def geographic_penalty(factory: ConstraintFactory, core_config=None, service_config=None, combined_config=None, **kwargs) -> Constraint:
    """Penalize assignments for distance beyond max_radius_miles, only if within geofence."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (
        factory.for_each(AppointmentAssignment)
        .filter(lambda assignment: (
            assignment.provider is not None and
            assignment.appointment_data.location is not None and
            assignment.provider.home_location is not None
        ))
        .penalize(HardSoftScore.ONE_SOFT, lambda assignment: 
            _calculate_distance_penalty(assignment.provider, assignment.appointment_data.location))
        .as_constraint(GEOGRAPHIC_PENALTY)
    )

def _is_within_any_geofence(provider, location) -> bool:
    """Check if location is within any of provider's service area geofences."""
    if not hasattr(provider, 'service_areas') or provider.service_areas is None:
        return True  # No geofences defined, assume within area
    
    for geofence in provider.service_areas:
        if geofence.zone_type == "service" and geofence.boundary_wkt:
            if _is_point_in_wkt_polygon(location.latitude, location.longitude, geofence.boundary_wkt):
                return True
    
    return False

def _is_in_blackout_zone(provider, location) -> bool:
    """Check if location is in a blackout zone for the provider."""
    if not hasattr(provider, 'service_areas') or provider.service_areas is None:
        return False  # No geofences defined, no blackout zones
    
    for geofence in provider.service_areas:
        if geofence.zone_type == "blackout" and geofence.boundary_wkt:
            if _is_point_in_wkt_polygon(location.latitude, location.longitude, geofence.boundary_wkt):
                return True
    
    return False

def _calculate_distance_penalty(provider, location) -> int:
    """Calculate penalty based on distance from provider's home location."""
    if provider.home_location is None or location is None:
        return 0
    distance = calculate_distance(provider.home_location, location)
    if distance <= 25.0:  # Within service radius
        return 0
    return int(distance * 1000)  # scale for penalty

# --- Helper functions ---
@with_config('behavioral_care', return_type='constraint')
def _get_service_radius(provider: Provider, appointment_location: Location, core_config=None, service_config=None, combined_config=None, **kwargs) -> float:
    """Get the service radius for a provider at a specific location."""
    # Get base radius from decorator-injected config
    base_radius = 25.0  # Default value
    
    # Apply any location-specific adjustments
    # For now, we'll use the base radius
    # In a real implementation, you might consider:
    # - Population density
    # - Traffic patterns
    # - Provider preferences
    # - Historical data
    
    return base_radius

@with_config('behavioral_care', return_type='constraint')
def _calculate_geographic_penalty(provider: Provider, appointment_location: Location, core_config=None, service_config=None, combined_config=None, **kwargs) -> int:
    """Calculate geographic penalty for provider-appointment assignment."""
    if provider.home_location is None or appointment_location is None:
        return 0
    
    # Calculate distance
    distance = calculate_distance(provider.home_location, appointment_location)
    
    # Get service radius from decorator-injected config
    service_radius = 25.0  # Default value
    
    # If outside service radius, this should be caught by hard constraint
    if distance > service_radius:
        return 0
    
    # Calculate penalty based on distance within service radius
    # Closer appointments get lower penalties
    penalty_factor = distance / service_radius if service_radius > 0 else 1.0
    
    # Scale penalty (0-1000 range)
    return int(penalty_factor * 1000)

@with_config('behavioral_care', return_type='constraint')
def geographic_service_area_validation(factory: ConstraintFactory, core_config=None, service_config=None, combined_config=None, **kwargs) -> Constraint:
    """Validate that providers are within their service area for appointments."""
    return (factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: (
                assignment.provider is not None and
                assignment.appointment_data.location is not None and
                assignment.provider.home_location is not None and
                not _is_within_service_area(assignment.provider, assignment.appointment_data)
            ))
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: 1)
            .as_constraint(GEOGRAPHIC_SERVICE_AREA))

@with_config('behavioral_care', return_type='constraint')
def blackout_zone_validation(factory: ConstraintFactory, core_config=None, service_config=None, combined_config=None, **kwargs) -> Constraint:
    """Validate that appointments are not in blackout zones."""
    return (factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: (
                assignment.provider is not None and
                assignment.appointment_data.location is not None and
                _is_in_blackout_zone(assignment.provider, assignment.appointment_data.location)
            ))
            .penalize(HardSoftScore.ONE_HARD, lambda assignment: 1)
            .as_constraint(BLACKOUT_ZONE_VIOLATION))

def _is_within_service_area(provider, appointment_data) -> bool:
    """Check if provider is within service area for the appointment."""
    try:
        # Get service configuration for geographic radius
        from .config_registry import ConfigRegistry
        service_config = ConfigRegistry.get_current_service_config()
        
        # Default radius if not configured
        max_radius_miles = 25.0
        if service_config and 'geographic_radius_miles' in service_config:
            max_radius_miles = service_config['geographic_radius_miles']
        
        # Check if both locations have coordinates
        if (provider.home_location and 
            appointment_data.location and
            provider.home_location.latitude and 
            provider.home_location.longitude and
            appointment_data.location.latitude and 
            appointment_data.location.longitude):
            
            # Calculate actual distance
            distance_miles = _calculate_distance_miles(
                provider.home_location.latitude, provider.home_location.longitude,
                appointment_data.location.latitude, appointment_data.location.longitude
            )
            
            # Check distance constraint first
            if distance_miles > max_radius_miles:
                logger.debug(f"Distance too far: {distance_miles:.2f} miles (max: {max_radius_miles})")
                return False
            
            # Check blackout zone constraint (hard constraint)
            if _is_in_blackout_zone(provider, appointment_data.location):
                logger.debug(f"Appointment location in blackout zone")
                return False
            
            # Check geofence constraint - appointment should be within provider's service areas
            if hasattr(provider, 'service_areas') and provider.service_areas is not None:
                service_areas = provider.service_areas
                if isinstance(service_areas, list) and len(service_areas) > 0:
                    # Check if appointment location falls within any of the provider's service area geofences
                    for geofence in service_areas:
                        if geofence.zone_type == "service" and geofence.boundary_wkt:
                            if _is_point_in_wkt_polygon(
                                appointment_data.location.latitude, 
                                appointment_data.location.longitude, 
                                geofence.boundary_wkt
                            ):
                                logger.debug(f"Geofence check passed: appointment within '{geofence.name}' service area")
                                return True
                    
                    # If we have service areas defined but appointment doesn't fall within any of them
                    logger.debug(f"Geofence check failed: appointment not within any service area geofence")
                    return False
                else:
                    # If no service areas defined, use distance only
                    # logger.debug(f"Distance check passed: {distance_miles:.2f} miles (no service areas defined)")
                    return True
            else:
                # If no service areas defined, use distance only
                # logger.debug(f"Distance check passed: {distance_miles:.2f} miles (no service areas defined)")
                return True
            
        # If no location data, assume it's acceptable
        return True
        
    except Exception as e:
        logger.debug(f"Error checking service area: {e}")
        # Default to True to avoid blocking assignments due to data issues
        return True

def _calculate_distance_miles(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """Calculate distance between two points using Haversine formula."""
    # Convert to radians
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)
    
    # Haversine formula
    dlat = lat2_rad - lat1_rad
    dlon = lon2_rad - lon1_rad
    a = (math.sin(dlat/2)**2 + 
         math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2)
    c = 2 * math.asin(math.sqrt(a))
    
    # Earth radius in miles
    earth_radius_miles = 3959.0
    
    return earth_radius_miles * c

def _is_point_in_wkt_polygon(lat: float, lon: float, wkt: str) -> bool:
    """
    Check if a point (lat, lon) is inside a WKT polygon.
    
    Args:
        lat: Latitude of the point
        lon: Longitude of the point  
        wkt: WKT string in format "POLYGON((lon1 lat1, lon2 lat2, ...))"
    
    Returns:
        bool: True if point is inside polygon, False otherwise
    """
    try:
        # Parse WKT polygon
        if not wkt.startswith("POLYGON((") or not wkt.endswith("))"):
            # logger.debug(f"Invalid WKT format: {wkt}")
            return False
        
        # Extract coordinates from WKT: "POLYGON((lon1 lat1, lon2 lat2, ...))"
        coords_str = wkt[9:-2]  # Remove "POLYGON((" and "))"
        
        # Parse coordinate pairs
        coords = []
        for coord_pair in coords_str.split(","):
            coord_pair = coord_pair.strip()
            if coord_pair:
                parts = coord_pair.split()
                if len(parts) == 2:
                    lon_coord = float(parts[0])
                    lat_coord = float(parts[1])
                    coords.append((lat_coord, lon_coord))
        
        if len(coords) < 3:
            # logger.debug(f"Invalid polygon: need at least 3 points, got {len(coords)}")
            return False
        
        # Use ray casting algorithm to determine if point is inside polygon
        return _ray_casting_point_in_polygon(lat, lon, coords)
        
    except Exception as e:
        # logger.debug(f"Error parsing WKT polygon: {e}")
        return False

def _ray_casting_point_in_polygon(lat: float, lon: float, polygon_coords: list) -> bool:
    """
    Ray casting algorithm to determine if a point is inside a polygon.
    
    Args:
        lat: Latitude of the point
        lon: Longitude of the point
        polygon_coords: List of (lat, lon) tuples forming the polygon
    
    Returns:
        bool: True if point is inside polygon, False otherwise
    """
    n = len(polygon_coords)
    if n < 3:
        return False
    
    inside = False
    j = n - 1
    
    for i in range(n):
        # Check if point is on the same side of the edge as the ray
        if (((polygon_coords[i][0] > lat) != (polygon_coords[j][0] > lat)) and
            (lon < (polygon_coords[j][1] - polygon_coords[i][1]) * 
             (lat - polygon_coords[i][0]) / 
             (polygon_coords[j][0] - polygon_coords[i][0]) + polygon_coords[i][1])):
            inside = not inside
        j = i
    
    return inside
